import logging
import socket
import threading
import time
from functools import lru_cache

from celery import states
from celery.app.task import Task
from celery.signals import task_postrun, task_prerun
from django.conf import settings
from prometheus_client import (
    CollectorRegistry,
    Counter,
    Gauge,
    Histogram,
    push_to_gateway,
)
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

logger = logging.getLogger(__name__)
registry = CollectorRegistry()

# Define metrics
celery_task_succeeded = Gauge(
    "celery_task_succeeded",
    "Number of succeeded tasks",
    ["task_name"],
    registry=registry,
)

celery_task_failed = Gauge(
    "celery_task_failed",
    "Number of failed tasks",
    ["task_name", "exception"],
    registry=registry,
)

celery_task_runtime = Histogram(
    "celery_task_runtime",
    "Task runtime in seconds",
    ["task_name"],
    registry=registry,
)

celery_active_tasks = Gauge(
    "celery_active_tasks",
    "Number of active tasks",
    ["task_name"],
    registry=registry,
)

# Add new metrics for test case execution
test_case_succeeded = Gauge(
    "test_case_succeeded",
    "Number of succeeded test case executions",
    ["env_name", "run_type", "report_name"],
    registry=registry,
)

test_case_failed = Gauge(
    "test_case_failed",
    "Number of failed test case executions",
    ["env_name", "run_type", "report_name"],
    registry=registry,
)

test_case_abnormal = Gauge(
    "test_case_abnormal",
    "Number of abnormal test case executions",
    ["env_name", "run_type", "report_name"],
    registry=registry,
)

test_case_runtime = Histogram(
    "test_case_runtime",
    "Test case execution runtime in seconds",
    ["env_name", "run_type", "report_name"],
    registry=registry,
)


@lru_cache
def get_host_name():
    return socket.gethostname()


# Celery event hooks
@task_prerun.connect
def task_prerun_handler(task_id, task: Task, *args, **kwargs):
    task_name = task.name.split(".")[-1] if task.name else "unknown"
    celery_active_tasks.labels(task_name=task_name).inc()
    setattr(task, "_starttime", time.monotonic())
    push_metrics(task_id)


@task_postrun.connect
def task_postrun_handler(
    task_id: str,
    task: Task,
    *args,
    retval=None,
    state: str = "",
    **kwargs,
):
    task_name = task.name.split(".")[-1] if task.name else "unknown"
    start_time = getattr(task, "_starttime", None)
    if start_time:
        runtime = time.monotonic() - start_time
        celery_task_runtime.labels(task_name=task_name).observe(runtime)

    # Handle general metrics
    celery_active_tasks.labels(task_name=task_name).dec()

    # Handle general success/failure metrics
    if state == states.SUCCESS:
        celery_task_succeeded.labels(task_name=task_name).inc()
    elif state == states.FAILURE:
        exception = type(args[0]).__name__ if args else "Unknown"
        celery_task_failed.labels(task_name=task_name, exception=exception).inc()
    push_metrics(task_id)


@retry(
    stop=stop_after_attempt(2),
    wait=wait_exponential(multiplier=1, min=4, max=10),
    retry=retry_if_exception_type(Exception),
    reraise=True,
)
def push_metrics(task_id: str = ""):
    try:
        logger.info(
            f"[{task_id}] Pushing metrics to gateway {settings.PUSH_GATEWAY_URL} ..."
        )
        push_to_gateway(
            settings.PUSH_GATEWAY_URL,
            job="celery_exporter",
            grouping_key={"instance": get_host_name()},
            registry=registry,
        )
        logger.info(
            f"[{task_id}] Successfully pushed celery metrics to {settings.PUSH_GATEWAY_URL}"
        )
    except Exception as e:
        logger.error(f"[{task_id}] Failed to celery push metrics: {e}")
        raise  # Re-raise the exception to trigger retry


# Add background thread for periodic metric pushing
class MetricPusherThread(threading.Thread):
    _instance = None
    _lock = threading.Lock()

    def __init__(self):
        super().__init__(daemon=True)
        self._stop_event = threading.Event()

    def run(self):
        while not self._stop_event.is_set():
            try:
                push_metrics("periodic")
            except Exception as e:
                logger.error(f"Periodic metric push failed: {e}")
            self._stop_event.wait(settings.PUSH_GATEWAY_INTELVAL_SECONDS)

    def stop(self):
        self._stop_event.set()

    @classmethod
    def get_instance(cls):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = cls()
                    cls._instance.start()
        return cls._instance


# Start the periodic pusher thread when module is imported
# MetricPusherThread.get_instance()
