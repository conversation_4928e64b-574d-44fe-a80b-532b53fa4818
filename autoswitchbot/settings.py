"""
Django settings for autoswitchbot project.

Generated by 'django-admin startproject' using Django 4.2.5.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
import sys
from datetime import timed<PERSON>ta
from pathlib import Path

import dynaconf  # noqa

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-8*hf3%f$w_bbp%2o=1y(db92!62@-9q(+t&*b=o=#eg8%e&#^p"

# Application definition

INSTALLED_APPS = [
    # "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    # "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    "rest_framework.authtoken",
    "dj_rest_auth",
    "drf_spectacular",
    "django.contrib.sites",
    "allauth",
    "allauth.account",
    "dj_rest_auth.registration",
    "allauth.socialaccount",
    "allauth.socialaccount.providers.feishu",
    "app.accounts",  # regist custom provider
    "app.project",
    "app.simulator",
    "app.configuration",
    "app.testcase",
    "app.coverage",
    "channels",
    "django_celery_beat",
]
# SILENCED_SYSTEM_CHECKS = [
#     "cachalot.W001",  # Cache backend 'django.core.cache.backends.redis.RedisCache' is not supported by django-cachalot
# ]

DJANGO_EASY_AUDIT_UNREGISTERED_URLS_EXTRA = [r"^/$", r"^/health$"]

ACCOUNT_AUTHENTICATION_METHOD = "email"  # default: 'user_email'
ACCOUNT_EMAIL_REQUIRED = True  # default: False
ACCOUNT_USERNAME_REQUIRED = False  # default: True

AUTHENTICATION_BACKENDS = (
    "django.contrib.auth.backends.ModelBackend",
    "allauth.account.auth_backends.AuthenticationBackend",
)
SITE_ID = 1

REST_FRAMEWORK = {
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticatedOrReadOnly",
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "app.accounts.repositories.CsrfExemptSessionAuthentication",
    ],
    "DATETIME_FORMAT": "%Y-%m-%d %H:%M:%S.%f",
    "DATETIME_INPUT_FORMATS": ["%Y-%m-%d %H:%M:%S%z"],
    "DATE_FORMAT": "%Y-%m-%d",
    "DATE_INPUT_FORMATS": ["%Y-%m-%d"],
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
    "DEFAULT_RENDERER_CLASSES": ["rest_framework.renderers.JSONRenderer"],
    "DEFAULT_PARSER_CLASSES": ["rest_framework.parsers.JSONParser"],
    "DEFAULT_FILTER_BACKENDS": [
        "django_filters.rest_framework.DjangoFilterBackend",
        "rest_framework.filters.OrderingFilter",
    ],
    "DEFAULT_PAGINATION_CLASS": "app.pagination.CachedPageNumberPagination",
    "EXCEPTION_HANDLER": "app.middleware.custom_exception_handler",
}
ACCOUNT_ADAPTER = "app.accounts.repositories.MyAccountAdapter"
SOCIALACCOUNT_STORE_TOKENS = True  # default: False
SOCIALACCOUNT_ADAPTER = "app.accounts.repositories.MySocialAccountAdapter"

SPECTACULAR_SETTINGS = {
    "TITLE": "自动化测试平台",
    "DESCRIPTION": "为了自动化测试",
    "VERSION": "1.0.0",
    "SERVE_INCLUDE_SCHEMA": False,
    # 'COMPONENT_SPLIT_REQUEST': True
    "SERIALIZER_EXTENSIONS": [
        "app.serializers.WrappedResponseExtension",
    ],
}

MIDDLEWARE = [
    "app.middleware.GZipMiddlewareConditional",
    "app.middleware.RequestTimingMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    # "django.middleware.common.CommonMiddleware",
    # "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "allauth.account.middleware.AccountMiddleware",
    # "django.contrib.messages.middleware.MessageMiddleware",
    # "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "autoswitchbot.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "autoswitchbot.wsgi.application"
ASGI_APPLICATION = "autoswitchbot.asgi.application"

# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "Asia/Shanghai"

USE_I18N = True

USE_TZ = True  # 提交的时间，按TZ时区到后端

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = "static/"

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# 日志目录
LOGGING_DIR = Path(BASE_DIR, "logs")
if not Path(LOGGING_DIR).exists():
    Path(LOGGING_DIR).mkdir()

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": "{asctime} {levelname} {filename}:{lineno:d} {message}",
            "style": "{",
        },
        "simple": {
            "format": "{asctime} {levelname} {message}",
            "style": "{",
        },
        "celery": {
            "format": "{asctime} {levelname} {threadName} {filename}:{lineno:d} {message}",
            "style": "{",
        },
    },
    "filters": {
        "require_debug_true": {
            "()": "django.utils.log.RequireDebugTrue",
        },
    },
    "handlers": {
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "standard",
        },
        "sql": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "standard",
        },
        "mail_admins": {
            "level": "ERROR",
            "class": "django.utils.log.AdminEmailHandler",
            "filters": [],
        },
        "django": {
            "level": "INFO",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": Path(LOGGING_DIR, "django.log"),
            "maxBytes": 1024 * 1024 * 500,
            "backupCount": 10,
            "formatter": "standard",
        },
        "celery_console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "celery",
        },
        "celery": {
            "level": "INFO",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": Path(LOGGING_DIR, "celery.log"),
            "maxBytes": 1024 * 1024 * 500,
            "backupCount": 10,
            "formatter": "celery",
        },
    },
    "loggers": {
        "": {
            "handlers": ["console", "django"],
            "level": "INFO",
            "propagate": False,
        },
        "django": {
            "handlers": ["console", "django"],
            "level": "INFO",
            "propagate": False,
        },
        "app": {
            "handlers": (
                ["celery_console", "celery"]
                if "celery" in sys.argv[0]
                else ["console", "django"]
            ),
            "level": "INFO",
            "propagate": False,
        },
        # "django.db.backends": {
        #     "level": "DEBUG",
        #     "handlers": ["sql"],
        #     "propagate": False,
        # },
    },
}

CELERY_TIMEZONE = TIME_ZONE
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_SERIALIZER = "json"
CELERY_TASK_RESULT_EXPIRES = timedelta(hours=1)
CELERY_WORKER_POOL = "threads"
# CELERY_WORKER_POOL = "gevent"
# CELERY_WORKER_POOL = "eventlet"
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1
CELERY_WORKER_CONCURRENCY = 3  # 默认使用cpu count
CELERY_REDIS_RETRY_ON_TIMEOUT = True
CELERY_REDIS_SOCKET_CONNECT_TIMEOUT = 10  # 10秒
CELERY_REDIS_BACKEND_HEALTH_CHECK_INTERVAL = 30  # 30秒
CELERY_WORKER_SEND_TASK_EVENTS = True  # -E
CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP = True
CELERY_BROKER_CONNECTION_MAX_RETRIES = None
# CELERY_WORKER_CANCEL_LONG_RUNNING_TASKS_ON_CONNECTION_LOSS = True
CELERY_WORKER_HIJACK_ROOT_LOGGER = False  # False celery的由django管理
CELERY_BEAT_SCHEDULER = "django_celery_beat.schedulers:DatabaseScheduler"
CELERY_TASK_QUEUES = {
    "simulator_queue": {
        "exchange": "simulator_queue",
        "exchange_type": "direct",
        "binding_key": "simulator_queue",
    },
    "testcase_queue": {
        "exchange": "testcase_queue",
        "exchange_type": "direct",
        "binding_key": "testcase_queue",
    },
    "coverage_queue": {
        "exchange": "coverage_queue",
        "exchange_type": "direct",
        "binding_key": "coverage_queue",
    },
}
CELERY_TASK_ROUTES = {
    # testcase 任务
    "app.testcase.tasks.*": {"queue": "testcase_queue"},
    # simulator 任务
    "app.simulator.tasks.*": {"queue": "simulator_queue"},
    # 维护任务使用默认队列
    "app.testcase.tasks.delete_old_test_reports": {"queue": "celery"},
    "app.simulator.tasks.delete_old_property": {"queue": "celery"},
}

# Celery Configuration
CELERY_TASK_TIME_LIMIT = 30 * 60
CELERY_IMPORTS = ("autoswitchbot.metrics",)

# CELERY_ROUTES_ROUTES = {
#     'simulator.tasks.*': {'queue': 'simulator_queue'},
# }
PUSH_GATEWAY_URL = "http://prometheus-pushgateway.monitor:9091"
PUSH_GATEWAY_INTELVAL_SECONDS = 30

# Amazon S3 settings
# AWS_STORAGE_BUCKET_NAME = "switchbot-runner-report"
# AWS_S3_CUSTOM_DOMAIN = f"{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com"
# AWS_S3_OBJECT_PARAMETERS = {
#     "CacheControl": "max-age=86400",
# }
# AWS_DEFAULT_ACL = "private"
# AWS_QUERYSTRING_AUTH = True  # 预签名URL的生成和使用时需要
# Tell Django to use S3 for file storage
# DEFAULT_FILE_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "autoswitchbot.settings")  # add
settings = dynaconf.DjangoDynaconf(
    __name__,
    PRELOAD_FOR_DYNACONF=["../settings.yaml", "../.secrets.yaml"],
)  # noqa
