# fix ImportError: cannot import name 'Celery' from 'celery'
from __future__ import absolute_import

import logging
import os

from celery import Celery
from celery.schedules import crontab

# from dynaconf import settings
from celery.signals import setup_logging

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "autoswitchbot.settings")

app = Celery("autoswitchbot")

app.config_from_object("django.conf:settings", namespace="CELERY")

app.autodiscover_tasks()

# Configure periodic tasks
# 这里使用的+8 时区
app.conf.beat_schedule = {
    "delete-old-test-report": {
        "task": "app.testcase.tasks.delete_old_test_reports",
        "schedule": crontab(minute=0),
    },
    "delete-old-property": {
        "task": "app.simulator.tasks.delete_old_property",
        "schedule": crontab(minute=0),
    },
}

logger = logging.getLogger(__name__)


# @worker_ready.connect
# def start_online_device_at_start(sender, **kwargs):
#     from app.simulator.tasks import start_mqtt_clients, start_mqtt_watchers

#     start_mqtt_clients.delay()
#     start_mqtt_watchers.delay()


@setup_logging.connect
def setup_celery_logging(**kwargs):
    """配置 Celery Worker 的日志"""
    import logging.config

    from celery.signals import after_setup_logger
    from django.conf import settings

    @after_setup_logger.connect
    def setup_loggers(logger, *args, **kwargs):
        # 使用 Django 的日志配置
        logging.config.dictConfig(settings.LOGGING)

        # 确保 app logger 使用 celery handler
        app_logger = logging.getLogger("app")
        app_logger.propagate = False  # 防止日志传播
