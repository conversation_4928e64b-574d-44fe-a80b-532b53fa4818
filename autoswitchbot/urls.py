"""
URL configuration for autoswitchbot project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from allauth.account.views import confirm_email as account_confirm_email
from allauth.account.views import login as account_login
from allauth.account.views import signup as account_signup
from allauth.socialaccount.views import signup as socialaccount_signup

# from django.contrib import admin
from django.urls import include, path
from drf_spectacular.views import SpectacularJSONAPIView, SpectacularSwaggerView

from app.accounts.views import PasswordResetConfirmView
from app.views import HealthCheckView

urlpatterns = [
    # path("admin/", admin.site.urls),
    path("schema/", SpectacularJSONAPIView.as_view(), name="schema"),
    path(
        "schema/swagger-ui",
        SpectacularSwaggerView.as_view(url_name="schema"),
        name="swagger-ui",
    ),
    # fix bug for dj_rest_auth Reverse for 'password_reset_confirm' not found
    # proxy for front-end password reset page
    path(
        "reset_confirm/<str:uidb64>/<str:token>",
        PasswordResetConfirmView.as_view(),
        name="password_reset_confirm",
    ),
    path("socialaccount_signup", socialaccount_signup, name="socialaccount_signup"),
    path("account_login", account_login, name="account_login"),
    path("account_signup", account_signup, name="account_signup"),
    path(
        "account_confirm_email/<str:key>",
        account_confirm_email,
        name="account_confirm_email",
    ),
    path("api/", include("app.urls")),
    path("health", HealthCheckView.as_view()),
]
