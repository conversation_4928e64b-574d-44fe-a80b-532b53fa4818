"""
ASGI config for autoswitchbot project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/asgi/
"""

import logging
import os

from channels.auth import AuthMiddlewareStack
from channels.routing import ProtocolType<PERSON>outer, URLRouter
from django.core.asgi import get_asgi_application

import autoswitchbot.routing

logger = logging.getLogger("django")

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "autoswitchbot.settings")
application = ProtocolTypeRouter(
    {
        "http": get_asgi_application(),
        "websocket": AuthMiddlewareStack(
            URLRouter(autoswitchbot.routing.websocket_urlpatterns)
        ),
    }
)

logger.info("asgi.application is running")
