"""
WSGI config for autoswitchbot project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/wsgi/
"""

import logging
import os

from django.core.wsgi import get_wsgi_application

logger = logging.getLogger("django")

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "autoswitchbot.settings")

application = get_wsgi_application()

logger.info("wsgi.application is running")
