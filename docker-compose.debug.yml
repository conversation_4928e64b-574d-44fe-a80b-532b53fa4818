version: '3.9'
services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.debug
    image: autoswitchbot-fe
    container_name: autoswitchbot-fe
    ports:
      - '8001:8001'
    restart: unless-stopped
    # volumes:
    # - /tmp/node_modules/.cache/mfsu:/data/node_modules/.cache/mfsu
    command: ['sh', '-c', 'yarn && yarn run start:docker']
    environment:
      - TZ=Asia/Shanghai
    networks:
      - autoswitchbot_default

networks:
  autoswitchbot_default:
    external: true
