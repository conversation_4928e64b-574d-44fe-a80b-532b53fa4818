FROM python:3.13-slim

COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/
COPY --from=443283509441.dkr.ecr.ap-east-1.amazonaws.com/switchbot-test/switchbot-runner:2025.04.09.1759 /data/runner_venv /data/runner_venv
COPY --from=golang:1.22-bookworm /usr/local/go /usr/local/go

ENV PATH=$PATH:/usr/local/go/bin
ENV GOPATH=/go
ENV UV_PROJECT_ENVIRONMENT=/usr/local
WORKDIR /data

RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources && \
	apt-get update && apt-get install -y --no-install-recommends \
    libpq-dev git openssh-client && \
    rm -rf /var/lib/apt/lists/* \
    chmod a+x /*.sh
