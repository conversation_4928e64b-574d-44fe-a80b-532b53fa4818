version: "3"
services:
  pg:
    image: postgres:16-alpine
    container_name: autoswitchbot-pg
    restart: unless-stopped
    volumes:
      - "postgres-data:/var/lib/postgresql/data/"
    environment:
      POSTGRES_DB: autoswitchbot-db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456

  redis:
    image: redis:7-alpine
    container_name: autoswitchbot-redis
    restart: unless-stopped
    volumes:
      - "redis-conf:/usr/local/etc/redis"

  django:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
    container_name: autoswitchbot-django
    environment:
      - DJANGO_ENV=docker
      - TZ=Asia/Shanghai
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    command: ["sleep", "infinity"]
    volumes:
      - ..:/data/autoswitchbot:cached
    ports:
      - "8000:8000"
    depends_on:
      - pg
      - redis

  # testcase:
  #   build:
  #     context: ..
  #     dockerfile: .devcontainer/Dockerfile
  #   container_name: autoswitchbot-testcase
  #   command: ["sh", "docker-celeryworker.sh", "testcase_queue"]
  #   volumes:
  #     - ..:/data/autoswitchbot:cached
  #   restart: unless-stopped
  #   environment:
  #     - DJANGO_ENV=docker
  #     - TZ=Asia/Shanghai

  # worker:
  #   build:
  #     context: ..
  #     dockerfile: .devcontainer/Dockerfile
  #   container_name: autoswitchbot-worker
  #   command: ["sh", "docker-celeryworker.sh", "simulator_queue"]
  #   volumes:
  #     - ..:/data/autoswitchbot:cached
  #   restart: unless-stopped
  #   environment:
  #     - DJANGO_ENV=docker
  #     - TZ=Asia/Shanghai

  # beat:
  #   build:
  #     context: ..
  #     dockerfile: .devcontainer/Dockerfile
  #   container_name: autoswitchbot-beat
  #   command: ["sh", "docker-celerybeat.sh"]
  #   volumes:
  #     - ..:/data/autoswitchbot:cached
  #   restart: unless-stopped
  #   environment:
  #     - DJANGO_ENV=docker
  #     - TZ=Asia/Shanghai

volumes:
  postgres-data:
  redis-conf:
