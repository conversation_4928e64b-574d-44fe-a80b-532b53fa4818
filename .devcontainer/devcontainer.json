{"name": "AutoSwitchBot Dev Environment", "dockerComposeFile": ["docker-compose.yml"], "service": "django", "workspaceFolder": "/data/autoswitchbot", "customizations": {"vscode": {"extensions": ["ms-python.python", "ms-python.vscode-pylance", "esbenp.prettier-vscode", "mhutchie.git-graph", "ms-python.black-formatter", "streetsidesoftware.code-spell-checker", "njpwerner.autodocstring", "github.copilot"], "settings": {"python.defaultInterpreterPath": "/data/autoswitchbot/.venv/bin/python", "python.terminal.activateEnvironment": true, "python.venvPath": "/data/autoswitchbot/.venv", "terminal.integrated.defaultProfile.linux": "bash"}}}, "forwardPorts": [8000], "postCreateCommand": "uv sync", "remoteUser": "root"}