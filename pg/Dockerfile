FROM  docker.io/bitnami/postgresql:16.4.0-debian-12-r14

USER root
# Install pg_repack
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    wget \
    gnupg \
    lsb-release && \
    wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add - && \
    echo "deb http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
    postgresql-16-repack && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* && \
    mkdir -p /opt/bitnami/postgresql/lib/ && \
    mkdir -p /opt/bitnami/postgresql/share/extension/ && \
    cp /usr/lib/postgresql/16/lib/pg_repack.so /opt/bitnami/postgresql/lib/ && \
    cp /usr/share/postgresql/16/extension/pg_repack* /opt/bitnami/postgresql/share/extension/

# 使用 postgres 用户安装插件

# pg_repack -h localhost -p 5432 -U postgres -d autoswitchbot-db -t test_report

USER 1001
