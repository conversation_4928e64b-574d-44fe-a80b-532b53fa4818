# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: ./pay/创建无效的订阅.yaml
from switchbotrunner import SwitchBotRunner, Config, Step, RunRequest
from switchbotrunner import RunApiGatewayRequest
from switchbotrunner import RunKinesisRequest
from switchbotrunner import RunSqsRequest
from switchbotrunner import RunLambdaRequest

class TestCase(SwitchBotRunner):

    

    config = Config("创建无效的订阅").variables(**{'account_api': 'https://account.api.woankeji.cn', 'client_id': 'fi1ynu021pdjgpzj4jtlxwq718', 'device_id': 'F94E2221-4CED-4305-89A8-DE18EBA7DE97', 'username': '<EMAIL>', 'password': '********', 'region': 'us-east-1'})

    teststeps = [
        
            Step(RunRequest("用户登录").with_variables(**{'aaa': '${get_uuid1()}'}).post("${account_api}/account/api/v1/user/login").with_headers(**{'Content-Type': 'application/json'}).with_json({'deviceInfo': {'deviceName': 'iPhone', 'appVersion': '7.6', 'model': 'iPhone 11 Pro', 'deviceId': '${device_id}'}, 'clientId': '${get_uuid1()}', 'password': '${password}', 'username': '${username}', 'grantType': 'password'}).extract().with_jmespath("body.body.access_token", 'token').validate().assert_equal("status_code", 200).assert_equal("body.statusCode", 100)),
        
            Step(RunRequest("用户获取个人信息").post("${account_api}/account/api/v1/user/userinfo").with_headers(**{'Content-Type': 'application/json', 'Authorization': '${token}'}).with_json({}).extract().with_jmespath("body.body.userID", 'user_id').validate().assert_equal("status_code", 200).assert_equal("body.statusCode", 100)),
        
            Step(RunRequest("用户创建订阅").post("${iap_api}/v1/cloudStorage/create").with_headers(**{'Content-Type': 'application/json', 'Authorization': '${token}'}).with_json({'productID': '', 'platform': 'google_play'}).validate().assert_equal("status_code", 200).assert_equal("body.resultCode", 190)),
        
    ]

if __name__ == "__main__":
    TestCase().test_start()
