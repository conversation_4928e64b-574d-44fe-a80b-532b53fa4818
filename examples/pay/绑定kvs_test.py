# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: ./pay/绑定kvs.yaml
from switchbotrunner import S<PERSON><PERSON>ot<PERSON><PERSON><PERSON>, Config, Step, RunRequest
from switchbotrunner import RunApiGatewayRequest
from switchbotrunner import RunKinesisRequest
from switchbotrunner import RunSqsRequest
from switchbotrunner import RunLambdaRequest

class TestCase(SwitchBotRunner):

    

    config = Config("绑定kvs").variables(**{'kvs_api': 'https://wonderlabs.us.api.woankeji.cn/kvs'})

    teststeps = [
        
            Step(RunRequest("用户登录").post("${account_api}/account/api/v1/user/login").with_headers(**{'Content-Type': 'application/json'}).with_json({'deviceInfo': {'deviceName': 'iPhone', 'appVersion': '7.6', 'model': 'iPhone 11 Pro', 'deviceId': '${device_id}'}, 'clientId': '${client_id}', 'password': '${password}', 'username': '${username}', 'grantType': 'password'}).extract().with_jmespath("body.body.access_token", 'token').validate().assert_equal("status_code", 200).assert_equal("body.statusCode", 100)),
        
            Step(RunRequest("用户获取个人信息").post("${account_api}/account/api/v1/user/userinfo").with_headers(**{'Content-Type': 'application/json', 'Authorization': '${token}'}).with_json({}).extract().with_jmespath("body.body.userID", 'user_id').validate().assert_equal("status_code", 200).assert_equal("body.statusCode", 100)),
        
            Step(RunRequest("申请绑定 token").with_variables(**{'request_id': '${get_uuid()}'}).post("${kvs_api}/v1/postToken").with_headers(**{'Content-Type': 'application/json', 'Authorization': '${token}'}).with_json({'token': '${request_id}', 'groupID': ''}).validate().assert_equal("status_code", 200).assert_equal("body.resultCode", 100)),
        
    ]

if __name__ == "__main__":
    TestCase().test_start()
