config:
    name: "用户登录刷新token"
    # env test us-east-1
    # variables:
        # account_api: https://account.api.woankeji.cn
        # client_id: "fi1ynu021pdjgpzj4jtlxwq718"
        # device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
        # username: "<EMAIL>"
        # password: "********"
        # region: "us-east-1"
        # aws_env: test
        # iap_api: https://pay.api.woankeji.cn/iap
        # kvs_api: https://wonderlabs.us.api.woankeji.cn/kvs

teststeps:
    -   name: 用户登录
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
            refresh_token: body.body.refresh_token
    -   name: 用户获取个人信息
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/userinfo
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            user_id: body.body.userID
    -   name: 用户刷新 token
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/token/refresh
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "clientId": "${client_id}",
                "refreshToken": "${refresh_token}",
                "deviceId": "${device_id}",
                "userId": "${user_id}"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            new_token: body.body.access_token
    -   name: 用户获取个人信息
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/userinfo
            headers:
                Content-Type: "application/json"
                Authorization: "${new_token}"
            json: {
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            user_id: body.body.userID
    -   name: 用户刷新 token
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/token/refresh
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "clientId": "${client_id}",
                "refreshToken": "${refresh_token}",
                "deviceId": "${device_id}",
                "userId": "${user_id}"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            new_new_token: body.body.access_token
    -   name: 用户获取个人信息
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/userinfo
            headers:
                Content-Type: "application/json"
                Authorization: "${new_token}"
            json: {
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            user_id: body.body.userID
