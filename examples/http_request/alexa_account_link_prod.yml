config:
    name: "alexa account link"
    variables:
        wonderlabs_api: "https://l9ren7efdj.execute-api.us-east-1.amazonaws.com/developStage"
        account_api: "https://bana6ys6sk.execute-api.us-east-1.amazonaws.com/prod"
        username: "<EMAIL>"
        password: ""
        client_id: "fi1ynu021pdjgpzj4jtlxwq718"
        device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"

teststeps:
    -   name: Login User
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
    -   name: Get Alexa Url
        request:
            method: POST
            url: ${wonderlabs_api}/user/v2/accountLink
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "version": release,
                "action": "getAlexaUrl",
                "platform": "IOS"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
            -   eq: [ "body.message", "OK" ]
        extract:
            alexa_url: body.body.AlexaAppUrl
            lwa_url: body.body.LWAUrl
    -   name: Get Login With Amazon
        request:
            method: GET
            url: ${lwa_url}
            headers:
                Cookie: session-id=131-3963814-1558824; session-id-time=2325817403l; ubid-main=130-3704816-6038268; lwa-context=b06496e7787c4cc2ca1de6306fd8a1c7
        validate:
            -   eq: [ "status_code", 200 ]
#    -   name: Get Code
#        request:
#            method: POST
#            url: https://api.amazon.com/auth/o2/token
#            headers:
#                Content-Type: "application/x-www-form-urlencoded"
#            data: {
#                "client_id": "amzn1.application-oa2-client.2577453e4f9548d39041fca6bc32edfb",
#                "client_secret": "2670cd7981376e280996563d88bd1c6116275702cbcfca734450747d960bfd56",
#                "grant_type": "client_credentials",
#                "scope": "alexa::skills:account_linking",
#                "response_type": "code"
#            }
#        validate:
#            -   eq: [ "status_code", 200 ]
#            -   eq: [ "body.statusCode", 100 ]
#        extract:
#            code: body.body.code
#    -   name: Get Access Token
#        request:
#            method: POST
#            url: ${merchant_api}/v1/oauth/alexatoken
#            headers:
#                Content-Type: "application/json"
#            json: {
#                "client_id": "${third_party_alexa_client_id}",
#                "code": "${code}",
#                "client_secret": "${third_party_alexa_client_secret}",
#                "grant_type": "authorization_code",
#            }
#        validate:
#            -   eq: [ "status_code", 200 ]
##            -   eq: [ "body.statusCode", 100 ]
