# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: alexa_oauth_token.yml
from switchbotrunner import S<PERSON><PERSON>ot<PERSON><PERSON><PERSON>, Config, Step, RunRequest
from switchbotrunner import RunApiGatewayRequest
from switchbotrunner import RunKinesisRequest
from switchbotrunner import RunS<PERSON>sRequest
from switchbotrunner import RunLambdaRequest


class TestCaseAlexaOauthToken(SwitchBotRunner):

    config = Config("Alexa oauth token").variables(
        **{
            "account_api": "https://account.api.woankeji.cn",
            "merchant_api": "https://account.api.woankeji.cn/merchant",
            "username": "<EMAIL>",
            "password": "********",
            "client_id": "fi1ynu021pdjgpzj4jtlxwq718",
            "device_id": "F94E2221-4CED-4305-89A8-DE18EBA7DE97",
            "third_party_alexa_client_id": "jfi0t49948tjivgggz4k2uagyi",
            "third_party_alexa_client_secret": "siousey4mjcw81tlsatrezqzad4prpvnxb088ytx",
        }
    )

    teststeps = [
        Step(
            RunRequest("Login User")
            .post("${account_api}/account/api/v1/user/login")
            .with_headers(**{"Content-Type": "application/json"})
            .with_json(
                {
                    "deviceInfo": {
                        "deviceName": "iPhone",
                        "appVersion": "7.6",
                        "model": "iPhone 11 Pro",
                        "deviceId": "${device_id}",
                    },
                    "clientId": "${client_id}",
                    "password": "${password}",
                    "username": "${username}",
                    "grantType": "password",
                }
            )
            .extract()
            .with_jmespath("body.body.access_token", "token")
            .validate()
            .assert_equal("status_code", 200)
            .assert_equal("body.statusCode", 100)
        ),
        Step(
            RunRequest("Get Code")
            .post("${merchant_api}/v1/oauth/code")
            .with_headers(
                **{"Content-Type": "application/json", "Authorization": "${token}"}
            )
            .with_json(
                {
                    "clientId": "${third_party_alexa_client_id}",
                    "redirectUri": "https://bruceauth.auth.us-east-1.amazoncognito.com/oauth2/token",
                    "responseType": "code",
                    "scope": "api_login",
                    "state": "TEST",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}",
                    "deviceName": "iPhone",
                }
            )
            .extract()
            .with_jmespath("body.body.code", "code")
            .validate()
            .assert_equal("status_code", 200)
            .assert_equal("body.statusCode", 100)
        ),
        Step(
            RunRequest("Get Access Token")
            .post("${merchant_api}/v1/oauth/alexatoken")
            .with_headers(**{"Content-Type": "application/json"})
            .with_json(
                {
                    "client_id": "${third_party_alexa_client_id}",
                    "code": "${code}",
                    "client_secret": "${third_party_alexa_client_secret}",
                    "grant_type": "authorization_code",
                }
            )
            .validate()
            .assert_equal("status_code", 200)
        ),
    ]


if __name__ == "__main__":
    TestCaseAlexaOauthToken().test_start()
