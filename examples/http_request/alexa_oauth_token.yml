config:
    name: "<PERSON><PERSON> oauth token"
    variables:
        account_api: "https://account.api.woankeji.cn"
        merchant_api: "https://account.api.woankeji.cn/merchant"
        username: "<EMAIL>"
        password: "********"
        client_id: "fi1ynu021pdjgpzj4jtlxwq718"
        device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
        third_party_alexa_client_id: "jfi0t49948tjivgggz4k2uagyi"
        third_party_alexa_client_secret: "siousey4mjcw81tlsatrezqzad4prpvnxb088ytx"
    #    base_url: ""
#    verify: False

teststeps:
    -   name: Login User
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
    -   name: Get Code
        request:
            method: POST
            url: ${merchant_api}/v1/oauth/code
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "clientId": "${third_party_alexa_client_id}",
                "redirectUri": "https://bruceauth.auth.us-east-1.amazoncognito.com/oauth2/token",
                "responseType": "code",
                "scope": "api_login",
                "state": "TEST",
                "model": "iPhone 11 Pro",
                "deviceId": "${device_id}",
                "deviceName": "iPhone",
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            code: body.body.code
    -   name: Get Access Token
        request:
            method: POST
            url: ${merchant_api}/v1/oauth/alexatoken
            headers:
                Content-Type: "application/json"
            json: {
                "client_id": "${third_party_alexa_client_id}",
                "code": "${code}",
                "client_secret": "${third_party_alexa_client_secret}",
                "grant_type": "authorization_code",
            }
        validate:
            -   eq: [ "status_code", 200 ]
#            -   eq: [ "body.statusCode", 100 ]
