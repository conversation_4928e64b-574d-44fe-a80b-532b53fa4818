# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: alexa_account_link_prod.yml
from switchbotrunner import S<PERSON><PERSON>otRun<PERSON>, Config, Step, RunRequest
from switchbotrunner import RunApiGatewayRequest
from switchbotrunner import RunKinesisRequest
from switchbotrunner import RunSqsRequest
from switchbotrunner import RunLambdaRequest


class TestCaseAlexaAccountLinkProd(SwitchBotRunner):

    config = Config("alexa account link").variables(
        **{
            "wonderlabs_api": "https://l9ren7efdj.execute-api.us-east-1.amazonaws.com/developStage",
            "account_api": "https://bana6ys6sk.execute-api.us-east-1.amazonaws.com/prod",
            "username": "<EMAIL>",
            "password": "",
            "client_id": "fi1ynu021pdjgpzj4jtlxwq718",
            "device_id": "F94E2221-4CED-4305-89A8-DE18EBA7DE97",
        }
    )

    teststeps = [
        Step(
            RunRequest("Login User")
            .post("${account_api}/account/api/v1/user/login")
            .with_headers(**{"Content-Type": "application/json"})
            .with_json(
                {
                    "deviceInfo": {
                        "deviceName": "iPhone",
                        "appVersion": "7.6",
                        "model": "iPhone 11 Pro",
                        "deviceId": "${device_id}",
                    },
                    "clientId": "${client_id}",
                    "password": "${password}",
                    "username": "${username}",
                    "grantType": "password",
                }
            )
            .extract()
            .with_jmespath("body.body.access_token", "token")
            .validate()
            .assert_equal("status_code", 200)
            .assert_equal("body.statusCode", 100)
        ),
        Step(
            RunRequest("Get Alexa Url")
            .post("${wonderlabs_api}/user/v2/accountLink")
            .with_headers(
                **{"Content-Type": "application/json", "Authorization": "${token}"}
            )
            .with_json(
                {"version": "release", "action": "getAlexaUrl", "platform": "IOS"}
            )
            .extract()
            .with_jmespath("body.body.AlexaAppUrl", "alexa_url")
            .with_jmespath("body.body.LWAUrl", "lwa_url")
            .validate()
            .assert_equal("status_code", 200)
            .assert_equal("body.statusCode", 100)
            .assert_equal("body.message", "OK")
        ),
        Step(
            RunRequest("Get Login With Amazon")
            .get("${lwa_url}")
            .with_headers(
                **{
                    "Cookie": "session-id=131-3963814-1558824; session-id-time=2325817403l; ubid-main=130-3704816-6038268; lwa-context=b06496e7787c4cc2ca1de6306fd8a1c7"
                }
            )
            .validate()
            .assert_equal("status_code", 200)
        ),
    ]


if __name__ == "__main__":
    TestCaseAlexaAccountLinkProd().test_start()
