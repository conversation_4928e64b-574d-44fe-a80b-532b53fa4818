config:
    name: "耗材列表"
    variables:
        wonderlabs_api: https://l9ren7efdj.execute-api.us-east-1.amazonaws.com/developStage
        account_api: https://bana6ys6sk.execute-api.us-east-1.amazonaws.com/prod
        device_api: https://6oi8p0504l.execute-api.us-east-1.amazonaws.com/prod
        robot_api: https://lonvwnxxn7.execute-api.us-east-1.amazonaws.com/prod
        simulate_device_api: http://192.168.2.8:8001
        username: "<EMAIL>"
        password: "aa123456"
        client_id: "fi1ynu021pdjgpzj4jtlxwq718"
        device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
        s10_device_id: 2DBA9D991A4C
        group_id: G01-*************-4a63 # 默认家庭

teststeps:
    -   name: 用户登录
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
    -   name: 上报耗材情况1
        request:
            method: POST
            url: ${simulate_device_api}/api/simulate/command
            headers:
                Content-Type: "application/json"
            json: { "mac": "${s10_device_id}","props": [ { "consumableList": [
                { "type": "dustStrainer","workingTime": 1000 },
                { "type": "sideBrush","workingTime": 1000 },
                { "type": "rollBrush","workingTime": 1000 },
                { "type": "rollWipe","workingTime": 1000 },
                { "type": "sewageBox","workingTime": 1000 },
                { "type": "sewagePool","workingTime": 1000 },
                { "type": "dustBag","workingTime": -1 },null ] } ] }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.success", True ]
        teardown_hooks:
            - ${sleep(3)}
    -   name: 耗材列表1
    # "sewageStrainer", // 污水滤网
        request:
            method: POST
            url: ${robot_api}/v1/consumable/list
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "deviceID": "${s10_device_id}",
                "language": "cn"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 100 ]
            -   eq: [ "body.data[*].name", ['尘盒滤网', '边刷', '胶刷', '拖地滚筒', '污水盒', '污水蓄水盒', '集尘袋']]
            -   eq: [ "body.data[*].type", ['dustStrainer', 'sideBrush', 'rollBrush', 'rollWipe', 'sewageBox', 'sewagePool', 'dustBag']]
            -   eq: [ "body.data[*].workingTime", [1000, 1000, 1000, 1000, 1000, 1000, -1]]
            -   eq: [ "body.data[*].workingLife", [540000, 540000, 1080000, 288000, 540000, 1080000, -1]]
    -   name: 上报耗材情况2
        request:
            method: POST
            url: ${simulate_device_api}/api/simulate/command
            headers:
                Content-Type: "application/json"
            json: { "mac": "${s10_device_id}","props": [ { "consumableList": [
                { "type": "dustStrainer","workingTime": 2000 },
                { "type": "sideBrush","workingTime": 2000 },
                { "type": "rollBrush","workingTime": 2000 },
                { "type": "rollWipe","workingTime": 2000 },
                { "type": "sewageBox","workingTime": 2000 },
                { "type": "sewagePool","workingTime": 2000 },
                { "type": "dustBag","workingTime": -1 },null ] } ] }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.success", True ]
        teardown_hooks:
            - ${sleep(3)}
    -   name: 耗材列表2
    # "sewageStrainer", // 污水滤网
        request:
            method: POST
            url: ${robot_api}/v1/consumable/list
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "deviceID": "${s10_device_id}",
                "language": "cn"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 100 ]
            -   eq: [ "body.data[*].name", ['尘盒滤网', '边刷', '胶刷', '拖地滚筒', '污水盒', '污水蓄水盒', '集尘袋']]
            -   eq: [ "body.data[*].type", ['dustStrainer', 'sideBrush', 'rollBrush', 'rollWipe', 'sewageBox', 'sewagePool', 'dustBag']]
            -   eq: [ "body.data[*].workingTime", [2000, 2000, 2000, 2000, 2000, 2000, -1]]
            -   eq: [ "body.data[*].workingLife", [540000, 540000, 1080000, 288000, 540000, 1080000, -1]]
