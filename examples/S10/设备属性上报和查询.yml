config:
    name: "设备属性上报和查询"
    variables:
        wonderlabs_api: https://l9ren7efdj.execute-api.us-east-1.amazonaws.com/developStage
        account_api: https://bana6ys6sk.execute-api.us-east-1.amazonaws.com/prod
        device_api: https://6oi8p0504l.execute-api.us-east-1.amazonaws.com/prod
        robot_api: https://lonvwnxxn7.execute-api.us-east-1.amazonaws.com/prod
        simulate_device_api: http://192.168.2.8:8001
        username: "<EMAIL>"
        password: "aa123456"
        client_id: "fi1ynu021pdjgpzj4jtlxwq718"
        device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
        s10_device_id: 2DBA9D991A4C
        group_id: G01-*************-4a63 # 默认家庭

teststeps:
    -   name: 用户登录
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
    -   name: Device充电中
        request:
            method: POST
            url: ${simulate_device_api}/api/simulate/command
            headers:
                Content-Type: "application/json"
            json: { "mac": "${s10_device_id}","props": [ { "workingStatus": 2 } ] }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.success", True ]
        teardown_hooks:
             - ${sleep(3)}
    -   name: DeviceIDs
        request:
            method: POST
            url: ${device_api}/v1/shadow/getByIDs
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "deviceID": "${s10_device_id}",
                "propertyIDs": [ 1010 ]
            }
        validate:
            -   eq: [ 'body.data."1010".value', 2 ]
    -   name: Device充电完成
        request:
            method: POST
            url: ${simulate_device_api}/api/simulate/command
            headers:
                Content-Type: "application/json"
            json: { "mac": "${s10_device_id}","props": [ { "workingStatus":3 } ] }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.success", True ]
        teardown_hooks:
            - ${sleep(3)}
    -   name: DeviceIDs
        request:
            method: POST
            url: ${device_api}/v1/shadow/getByIDs
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "deviceID":"${s10_device_id}",
                "propertyIDs":[1010]
            }
        validate:
            -   eq: ['body.data."1010".value', 3]
