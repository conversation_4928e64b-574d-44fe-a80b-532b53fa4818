config:
    name: "S10扫地流程-实时查看"
    variables:
        wonderlabs_api: https://l9ren7efdj.execute-api.us-east-1.amazonaws.com/developStage
        account_api: https://bana6ys6sk.execute-api.us-east-1.amazonaws.com/prod
        device_api: https://6oi8p0504l.execute-api.us-east-1.amazonaws.com/prod
        robot_api: https://lonvwnxxn7.execute-api.us-east-1.amazonaws.com/prod
        simulate_device_api: http://192.168.2.8:8001
        username: "<EMAIL>"
        password: "aa123456"
        client_id: "fi1ynu021pdjgpzj4jtlxwq718"
        device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
        s10_device_id: 2DBA9D991A4C
        task_id: "${get_uuid()}"
        region: "us-east-1"
        # task_id: 54865a19-9387-4350-ab69-a91d4ba77bc5

teststeps:
    -   name: 用户登录
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
    -   name: 用户获取个人信息
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/userinfo
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            user_id: body.body.userID
    -   name: 虚拟设备确保在线
        request:
            method: PUT
            url: ${simulate_device_api}/api/simulate/device/${s10_device_id}
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "env": "${simulate_device_env}",
                "region": "${region}",
                "status": "on",
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.success", True ]
    -   name: 配置虚拟设备扫地任务回调
        request:
            method: POST
            url: ${simulate_device_api}/api/simulate/callback_config
            headers:
                Content-Type: "application/json"
            json: {
                "name": "startSweep",
                "mac": "${s10_device_id}",
                "function_id": 1001,
                "callback_template": {
                    "return": [ 0 ],
                    "actions": [
                        {
                            "type": "props",
                            "value": { "1043": 1 }
                        },
                        {
                            "type": "props",
                            "value": { "1002": '0.1.285.240119' }
                        },
                        {
                            "type": "props",
                            "value": { "1010": 9,"1011": [ ],"1012": "${task_id}","1013": 4,"1023": "123","1032": { "action": "clean_all","actionParam": { "mode": { "fan_level": 1,"times": 1,"type": "sweep","water_level": 2 } },"source": "app","taskId": "${task_id}" },"1064": "sweep" }
                        },
                        {
                            "type": "robot_task",
                            "param": { "task_id": "${task_id}","area": 100, "speed": 100 }
                        },
                        {
                            "type": "props",
                            "value": { "1010": 1,"1012": "","1013": 0,"1015": "${task_id}","1056": { "status": "UnSync","taskID": "${task_id}" }
                            }
                        }
                    ]
                }
            }
        validate:
            -   eq: [ "status_code", 201 ]
            -   eq: [ "body.success", True ]
    -   name: 配置虚拟设备同步扫地结果回调
        request:
            method: POST
            url: ${simulate_device_api}/api/simulate/callback_config
            headers:
                Content-Type: "application/json"
            json: {
                "name": "startSweep",
                "mac": "${s10_device_id}",
                "function_id": 1035,
                "callback_template": {
                    "return": [ 0, { "action": "clean_all","actionParam": { "mode": { "fan_level": 2,"times": 1,"type": "sweep","water_level": 2 } },"cleanTime": 15551,"duration": 3600000,"estimateArea": 120.0,"result": "success","source": "app","startTime": "${seconds()}","stopTime": "${seconds(3600)}","taskArea": 100.0,"taskID": "${task_id}" } ],
                    "actions": [
                        {
                            "type": "props",
                            "value": { "1056": { "status": "InSync","taskID": "${task_id}" } }
                        },
                        {
                            "type": "props",
                            "value": { "1056": { "status": "Synced","taskID": "${task_id}" } }
                        }
                    ]
                }
            }
        validate:
            -   eq: [ "status_code", 201 ]
            -   eq: [ "body.success", True ]
    -   name: app调用实时查看轨迹点
        request:
            method: POST
            url: ${robot_api}/v1/task/monitor/open
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {"deviceID": "${s10_device_id}"}
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 200 ]
    -   name: 开始扫地
        request:
            method: POST
            url: ${device_api}/cmd/api/v1/func/invoke
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {"deviceID":"${s10_device_id}","functionID":1001,"notify":{"type":"mqtt","url":"v1_1/${user_id}/APP_TEST_${user_id}/funcResp"},"optSrc":"app","params":{"0":"clean_all","1":{"mode":{"fan_level":1,"times":1,"type":"sweep","water_level":1}}},"requestID":"${task_id}","timeout":20000}
        teardown_hooks:
            - ${sleep(60)} # 100个点0.5s一个
    # 校验 手机mqtt 已经收到转发的轨迹点和 message
    -   name: app关闭实时查看轨迹点
        request:
            method: POST
            url: ${robot_api}/v1/task/monitor/close
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {"deviceID": "${s10_device_id}"}
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 200 ]
    -   name: 获取轨迹点
        request:
            method: POST
            url: ${robot_api}/v1/task/trackPose
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "DeviceID": "${s10_device_id}",
                "TaskID": "${task_id}",
                "StartIndex": 0,
                "EndIndex": 100
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   length_equal: ["body.data.data", 101]
    -   name: 查询清扫报告
        request:
            method: POST
            url: ${robot_api}/v1/taskRecord/list
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "deviceID": "${s10_device_id}",
                "page": 0
            }
