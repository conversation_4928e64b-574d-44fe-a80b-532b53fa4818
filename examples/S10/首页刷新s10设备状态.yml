config:
    name: "首页刷新s10设备状态"
    variables:
        wonderlabs_api: https://l9ren7efdj.execute-api.us-east-1.amazonaws.com/developStage
        account_api: https://bana6ys6sk.execute-api.us-east-1.amazonaws.com/prod
        device_api: https://6oi8p0504l.execute-api.us-east-1.amazonaws.com/prod
        robot_api: https://lonvwnxxn7.execute-api.us-east-1.amazonaws.com/prod
        user_group_api: https://rh3axc6yo6.execute-api.us-east-1.amazonaws.com/prod
        simulate_device_api: http://***********:8001
        username: "<EMAIL>"
        password: "aa123456"
        client_id: "fi1ynu021pdjgpzj4jtlxwq718"
        device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
        s10_device_id: 2DBA9D991A4C

teststeps:
    -   name: 用户登录
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
    -   name: 首页获取所有设备物模型属性
        request:
            method: POST
            url: ${user_group_api}/userGroup/v1/user/notifyAllDeviceProperty
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 100 ]
    # 校验虚拟设备收到的物理型属性值
    # 首页校验66 在线状态、1010 设备工作状态
