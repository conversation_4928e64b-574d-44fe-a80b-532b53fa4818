config:
  name: 获取设备列表v1.1
  variables:
    open_api_v2: "https://d-vsp5hvxzf2.execute-api.us-east-1.amazonaws.com"
    open_api_v2_host: "api.switch-bot.com"
    open_api_token: "d8851bbbc2c199512be1ee55f12a448930505fc6fd20195607f7c8bb649a373f1e619e9e92240b17f793b31b0a3de7b6"
    open_api_secret: "67b4d79245a052cca971182d7b4b9f00"
teststeps:
  - name: 获取设备列表v1.1
    variables:
      timestamp: "${get_timestamp()}"
      nonce: "${get_timestamp()}"
      sign: "${open_api_sign($open_api_token, $open_api_secret, $timestamp,$nonce)}"
    request:
      method: GET
      url: "${open_api_v2}/v1.1/devices"
      headers:
        {
          Content-Type: application/json,
          Authorization: "${open_api_token}",
          t: "${timestamp}",
          sign: "${sign}",
          nonce: "${nonce}",
          Host: "${open_api_v2_host}",
        }
    validate:
      - { eq: [status_code, 200] }
      - { eq: [body.statusCode, 100] }
