# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: link_to_TEST_shadow.yml
from switchbotrunner import (
    Config,
    RunApiGatewayRequest,
    RunKinesisRequest,
    RunLambdaRequest,
    RunRequest,
    RunSqsRequest,
    Step,
    SwitchBotRunner,
)


class TestCaseLinkToTestShadow(SwitchBotRunner):
    config = (
        Config("数据转发上行流量测试 link_to_TEST_shadow")
        .variables(
            **{
                "us_sn": "fO6zUtYSczVzP8fz",
                "us_client_id": "HUB2-C9F8C8F7368C",
                "ap_sn": "UpVddDIUb4HejTpe",
                "ap_client_id": "BLE-CAEAD9055236",
            }
        )
        .aws_env("test")
    )

    teststeps = [
        Step(
            RunKinesisRequest("设备属于亚区，在美区上报事件")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "us-east-1",
                "handleDataReportManager",
                {
                    "sn": "SJkObDAjxBvzlS6Q",
                    "eventType": "link_to_TEST_shadow",
                    "clientId": "BLE-DC5C43B3F35D",
                    "data": "BCD|010211024|644b45d3|54f333507dea4e0101dc03c89daf",
                    "timestamp": "${ts}",
                },
            )
            .extract()
            .with_jmespath("request.data", "mqtt_data")
        ),
    ]


if __name__ == "__main__":
    TestCaseLinkToTestShadow().test_start()
