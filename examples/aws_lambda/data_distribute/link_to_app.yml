config:
    name: "数据转发上行流量测试 link_to_app"
    variables:
        us_sn: "qayJotTvLsW4gODB" # WoLinkMini
        us_client_id: "BLE-E5CE8392644B"
        ap_sn: "UpVddDIUb4HejTpe"
        ap_client_id: "BLE-CAEAD9055236"

teststeps:
    -   name: "设备属于美区，在亚区上报事件"
        variables:
            ts: ${timestamp()}
        kinesis_request:
            region: ap-northeast-1
            function: test_lambda_data_distribute_link_app
#            version: release
            data:
                {
                    "sn": "${us_sn}",
                    "eventType": "link_to_app",
                    "clientId": "${us_client_id}",
                    "data": "SCANR;88:3dd79623e86793780057|${ts}",
                    "timestamp": "${ts}"
                }
        extract:
            mqtt_data: "request.data"
        validate_log:
            - logs: [ "test_lambda_data_distribute_link_app", "${mqtt_data}", "${ts}", "ap-northeast-1"]
            - logs: [ "test_lambda_data_distribute_link_app", "${mqtt_data}", "${ts}", "us-east-1"]
            - logs: [ "handleIoTCommand", "${mqtt_data}", "${ts}", "us-east-1"]
    -   name: "设备属于亚区，在美区上报事件"
        variables:
            ts: ${timestamp()}
        kinesis_request:
            region: us-east-1
            function: test_lambda_data_distribute_link_app
#            version: release
            data:
                {
                    "sn": "${ap_sn}",
                    "eventType": "link_to_app",
                    "clientId": "${ap_client_id}",
                    "data": "SCANR;88:3dd79623e86793780057|${ts}",
                    "timestamp": "${ts}"
                }
        extract:
            mqtt_data: "request.data"
        validate_log:
            - logs: [ "test_lambda_data_distribute_link_app", "${mqtt_data}", "${ts}", "us-east-1"]
            - logs: [ "test_lambda_data_distribute_link_app", "${mqtt_data}", "${ts}", "ap-northeast-1"]
            - logs: [ "handleIoTCommand", "${mqtt_data}", "${ts}", "ap-northeast-1"]
