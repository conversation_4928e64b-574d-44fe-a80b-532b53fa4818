# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: link_to_report.yml
from switchbotrunner import S<PERSON><PERSON>ot<PERSON><PERSON><PERSON>, Config, Step, RunRequest
from switchbotrunner import RunApiGatewayRequest
from switchbotrunner import RunKinesisRequest
from switchbotrunner import <PERSON><PERSON><PERSON>s<PERSON>e<PERSON>
from switchbotrunner import RunLambdaRequest


class TestCaseLinkToReport(SwitchBotRunner):

    config = Config("数据转发上行流量测试 link_to_report").variables(
        **{
            "us_sn": "qayJotTvLsW4gODB",
            "us_client_id": "BLE-E5CE8392644B",
            "ap_sn": "UpVddDIUb4HejTpe",
            "ap_client_id": "BLE-CAEAD9055236",
        }
    )

    teststeps = [
        Step(
            RunKinesisRequest("设备属于美区，在亚区上报事件")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "dT": 35,
                        "cT": 1,
                        "isC": 1,
                        "tS": "${ts}",
                        "dM": "a0b76548653e",
                        "cD": "a0b76548653e48161c0000",
                        "bM": "ebb57df4b2e4",
                        "v": 1,
                    },
                }
            )
            .request(
                "ap-northeast-1",
                "test_lambda_data_distribute_property",
                {
                    "sn": "${us_sn}",
                    "eventType": "link_to_report",
                    "clientId": "${us_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_data_distribute_property",
                "${mqtt_data}",
                "${ts}",
                "ap-northeast-1",
            )
            .assert_logs(
                "handleDataReportManager", "${mqtt_data}", "${ts}", "us-east-1"
            )
            .assert_not_logs(
                "handleDataReportManager", "${mqtt_data}", "${ts}", "ap-northeast-1"
            )
        ),
        Step(
            RunKinesisRequest("设备属于亚区，在美区上报事件")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "dT": 35,
                        "cT": 1,
                        "isC": 1,
                        "tS": "${ts}",
                        "dM": "a0b76548653e",
                        "cD": "a0b76548653e48161c0000",
                        "bM": "ebb57df4b2e4",
                        "v": 1,
                    },
                }
            )
            .request(
                "us-east-1",
                "test_lambda_data_distribute_property",
                {
                    "sn": "${ap_sn}",
                    "eventType": "link_to_report",
                    "clientId": "${ap_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_data_distribute_property",
                "${mqtt_data}",
                "${ts}",
                "us-east-1",
            )
            .assert_logs(
                "handleDataReportManager", "${mqtt_data}", "${ts}", "ap-northeast-1"
            )
            .assert_not_logs(
                "handleDataReportManager", "${mqtt_data}", "${ts}", "us-east-1"
            )
        ),
    ]


if __name__ == "__main__":
    TestCaseLinkToReport().test_start()
