# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: link_to_app.yml
from switchbotrunner import S<PERSON><PERSON>ot<PERSON><PERSON><PERSON>, Config, Step, RunRequest
from switchbotrunner import RunApiGatewayRequest
from switchbotrunner import RunKinesisRequest
from switchbotrunner import <PERSON><PERSON><PERSON>s<PERSON>equest
from switchbotrunner import RunLambdaRequest


class TestCaseLinkToApp(SwitchBotRunner):

    config = Config("数据转发上行流量测试 link_to_app").variables(
        **{
            "us_sn": "qayJotTvLsW4gODB",
            "us_client_id": "BLE-E5CE8392644B",
            "ap_sn": "UpVddDIUb4HejTpe",
            "ap_client_id": "BLE-CAEAD9055236",
        }
    )

    teststeps = [
        Step(
            RunKinesisRequest("设备属于美区，在亚区上报事件")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "ap-northeast-1",
                "test_lambda_data_distribute_link_app",
                {
                    "sn": "${us_sn}",
                    "eventType": "link_to_app",
                    "clientId": "${us_client_id}",
                    "data": "SCANR;88:3dd79623e86793780057|${ts}",
                    "timestamp": "${ts}",
                },
            )
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_data_distribute_link_app",
                "${mqtt_data}",
                "${ts}",
                "ap-northeast-1",
            )
            .assert_logs(
                "test_lambda_data_distribute_link_app",
                "${mqtt_data}",
                "${ts}",
                "us-east-1",
            )
            .assert_logs("handleIoTCommand", "${mqtt_data}", "${ts}", "us-east-1")
        ),
        Step(
            RunKinesisRequest("设备属于亚区，在美区上报事件")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "us-east-1",
                "test_lambda_data_distribute_link_app",
                {
                    "sn": "${ap_sn}",
                    "eventType": "link_to_app",
                    "clientId": "${ap_client_id}",
                    "data": "SCANR;88:3dd79623e86793780057|${ts}",
                    "timestamp": "${ts}",
                },
            )
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_data_distribute_link_app",
                "${mqtt_data}",
                "${ts}",
                "us-east-1",
            )
            .assert_logs(
                "test_lambda_data_distribute_link_app",
                "${mqtt_data}",
                "${ts}",
                "ap-northeast-1",
            )
            .assert_logs("handleIoTCommand", "${mqtt_data}", "${ts}", "ap-northeast-1")
        ),
    ]


if __name__ == "__main__":
    TestCaseLinkToApp().test_start()
