# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: ./link_to_cmd_matter.yml
from switchbotrunner import (
    Config,
    RunApiGatewayRequest,
    RunKinesisRequest,
    RunLambdaRequest,
    RunRequest,
    RunSqsRequest,
    Step,
    SwitchBotRunner,
)


class TestCaseLinkToCmdMatter(SwitchBotRunner):
    config = Config("数据转发v2上行流量测试 link_to_cmd_matter").variables(
        **{
            "us_sn": "qayJotTvLsW4gODB",
            "us_client_id": "BLE-E5CE8392644B",
            "ap_sn": "UpVddDIUb4HejTpe",
            "ap_client_id": "BLE-CAEAD9055236",
        }
    )

    teststeps = [
        Step(
            RunKinesisRequest("设备属于美区，在美区上报事件")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "us-east-1",
                "test_lambda_iot_link_to_router",
                {
                    "sn": "${us_sn}",
                    "eventType": "link_to_cmd_matter",
                    "clientId": "${us_client_id}",
                    "data": "${ts}",
                    "timestamp": "${ts}",
                },
            )
            .with_version(release)
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "us-east-1"
            )
            .assert_logs("handleMatterCommand", "${mqtt_data}", "${ts}", "us-east-1")
            .assert_not_logs(
                "handleMatterCommand", "${mqtt_data}", "${ts}", "ap-northeast-1"
            )
        ),
        Step(
            RunKinesisRequest("设备属于美区，在亚区上报事件")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "ap-northeast-1",
                "test_lambda_iot_link_to_router",
                {
                    "sn": "${us_sn}",
                    "eventType": "link_to_cmd_matter",
                    "clientId": "${us_client_id}",
                    "data": "${ts}",
                    "timestamp": "${ts}",
                },
            )
            .with_version(release)
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_iot_link_to_router",
                "${mqtt_data}",
                "${ts}",
                "ap-northeast-1",
            )
            .assert_logs("handleMatterCommand", "${mqtt_data}", "${ts}", "us-east-1")
            .assert_not_logs(
                "handleMatterCommand", "${mqtt_data}", "${ts}", "ap-northeast-1"
            )
        ),
        Step(
            RunKinesisRequest("设备属于亚区，在美区上报事件")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "us-east-1",
                "test_lambda_iot_link_to_router",
                {
                    "sn": "${ap_sn}",
                    "eventType": "link_to_cmd_matter",
                    "clientId": "${ap_client_id}",
                    "data": "${ts}",
                    "timestamp": "${ts}",
                },
            )
            .with_version(release)
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "us-east-1"
            )
            .assert_logs(
                "handleMatterCommand", "${mqtt_data}", "${ts}", "ap-northeast-1"
            )
            .assert_not_logs(
                "handleMatterCommand", "${mqtt_data}", "${ts}", "us-east-1"
            )
        ),
        Step(
            RunKinesisRequest("设备属于亚区，在亚区上报事件")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "ap-northeast-1",
                "test_lambda_iot_link_to_router",
                {
                    "sn": "${ap_sn}",
                    "eventType": "link_to_cmd_matter",
                    "clientId": "${ap_client_id}",
                    "data": "${ts}",
                    "timestamp": "${ts}",
                },
            )
            .with_version(release)
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_iot_link_to_router",
                "${mqtt_data}",
                "${ts}",
                "ap-northeast-1",
            )
            .assert_logs(
                "handleMatterCommand", "${mqtt_data}", "${ts}", "ap-northeast-1"
            )
            .assert_not_logs(
                "handleMatterCommand", "${mqtt_data}", "${ts}", "us-east-1"
            )
        ),
    ]


if __name__ == "__main__":
    TestCaseLinkToCmdMatter().test_start()
