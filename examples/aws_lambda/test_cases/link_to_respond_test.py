# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: ./link_to_respond.yml
from switchbotrunner import (
    Config,
    RunApiGatewayRequest,
    RunKinesisRequest,
    RunLambdaRequest,
    RunRequest,
    RunSqsRequest,
    Step,
    SwitchBotRunner,
)


class TestCaseLinkToRespond(SwitchBotRunner):
    config = Config("数据转发v2上行流量测试 link_to_respond").variables(
        **{
            "us_sn": "lCDgpBqT4FjRdc31",
            "us_client_id": "BLE-EEB0EBC30FE3",
            "ap_sn": "UpVddDIUb4HejTpe",
            "ap_client_id": "BLE-CAEAD9055236",
        }
    )

    teststeps = [
        Step(
            RunKinesisRequest("设备属于美区，在美区上报事件")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "protocolVersion": 1,
                        "count": 1,
                        "messages": [
                            {
                                "id": "16ce4f0a-bece-4c10-83fc-f7a84fe2aaed",
                                "eventType": "Wo.Device.CommandRespond",
                                "data": {
                                    "deviceID": "EEB0EBC30FE3",
                                    "contentType": "hex",
                                    "correlationID": "33cf51db-2faf-42dc-9250-7e677c9250c4",
                                    "createTime": "${timestamp()}",
                                    "commandType": 1,
                                    "ackType": "succeed",
                                    "content": "570101|014890",
                                },
                            }
                        ],
                    },
                }
            )
            .request(
                "us-east-1",
                "test_lambda_iot_link_to_router",
                {
                    "sn": "${us_sn}",
                    "eventType": "link_to_respond",
                    "clientId": "${us_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .with_version(release)
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "us-east-1"
            )
            .assert_logs("handleIoTCommand", "${mqtt_data}", "${ts}", "us-east-1")
            .assert_not_logs(
                "handleIoTCommand", "${mqtt_data}", "${ts}", "ap-northeast-1"
            )
        ),
        Step(
            RunKinesisRequest("设备属于美区，在亚区上报事件")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "protocolVersion": 1,
                        "count": 1,
                        "messages": [
                            {
                                "id": "16ce4f0a-bece-4c10-83fc-f7a84fe2aaed",
                                "eventType": "Wo.Device.CommandRespond",
                                "data": {
                                    "deviceID": "EEB0EBC30FE3",
                                    "contentType": "hex",
                                    "correlationID": "33cf51db-2faf-42dc-9250-7e677c9250c4",
                                    "createTime": "${timestamp()}",
                                    "commandType": 1,
                                    "ackType": "succeed",
                                    "content": "570101|014890",
                                },
                            }
                        ],
                    },
                }
            )
            .request(
                "ap-northeast-1",
                "test_lambda_iot_link_to_router",
                {
                    "sn": "${us_sn}",
                    "eventType": "link_to_respond",
                    "clientId": "${us_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .with_version(release)
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_iot_link_to_router",
                "${mqtt_data}",
                "${ts}",
                "ap-northeast-1",
            )
            .assert_logs("handleIoTCommand", "${mqtt_data}", "${ts}", "us-east-1")
            .assert_not_logs(
                "handleIoTCommand", "${mqtt_data}", "${ts}", "ap-northeast-1"
            )
        ),
        Step(
            RunKinesisRequest("设备属于亚区，在美区上报事件")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "protocolVersion": 1,
                        "count": 1,
                        "messages": [
                            {
                                "id": "16ce4f0a-bece-4c10-83fc-f7a84fe2aaed",
                                "eventType": "Wo.Device.CommandRespond",
                                "data": {
                                    "deviceID": "EEB0EBC30FE3",
                                    "contentType": "hex",
                                    "correlationID": "33cf51db-2faf-42dc-9250-7e677c9250c4",
                                    "createTime": "${timestamp()}",
                                    "commandType": 1,
                                    "ackType": "succeed",
                                    "content": "570101|014890",
                                },
                            }
                        ],
                    },
                }
            )
            .request(
                "us-east-1",
                "test_lambda_iot_link_to_router",
                {
                    "sn": "${ap_sn}",
                    "eventType": "link_to_respond",
                    "clientId": "${ap_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .with_version(release)
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "us-east-1"
            )
            .assert_logs("handleIoTCommand", "${mqtt_data}", "${ts}", "ap-northeast-1")
            .assert_not_logs("handleIoTCommand", "${mqtt_data}", "${ts}", "us-east-1")
        ),
        Step(
            RunKinesisRequest("设备属于亚区，在亚区上报事件")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "protocolVersion": 1,
                        "count": 1,
                        "messages": [
                            {
                                "id": "16ce4f0a-bece-4c10-83fc-f7a84fe2aaed",
                                "eventType": "Wo.Device.CommandRespond",
                                "data": {
                                    "deviceID": "EEB0EBC30FE3",
                                    "contentType": "hex",
                                    "correlationID": "33cf51db-2faf-42dc-9250-7e677c9250c4",
                                    "createTime": "${timestamp()}",
                                    "commandType": 1,
                                    "ackType": "succeed",
                                    "content": "570101|014890",
                                },
                            }
                        ],
                    },
                }
            )
            .request(
                "ap-northeast-1",
                "test_lambda_iot_link_to_router",
                {
                    "sn": "${ap_sn}",
                    "eventType": "link_to_respond",
                    "clientId": "${ap_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .with_version(release)
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_iot_link_to_router",
                "${mqtt_data}",
                "${ts}",
                "ap-northeast-1",
            )
            .assert_logs("handleIoTCommand", "${mqtt_data}", "${ts}", "ap-northeast-1")
            .assert_not_logs("handleIoTCommand", "${mqtt_data}", "${ts}", "us-east-1")
        ),
    ]


if __name__ == "__main__":
    TestCaseLinkToRespond().test_start()
