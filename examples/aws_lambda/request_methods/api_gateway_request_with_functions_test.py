# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: api_gateway_request_with_functions.yml
from switchbotrunner import Switch<PERSON>otRun<PERSON>, Config, Step, RunRequest
from switchbotrunner import RunApiGatewayRequest
from switchbotrunner import RunKinesisRequest
from switchbotrunner import RunSqsRequest
from switchbotrunner import RunLambdaRequest


class TestCaseApiGatewayRequestWithFunctions(SwitchBotRunner):

    config = Config(
        "api-gateway request methods testcase with functions", case_id="test"
    ).variables(**{"email": "<EMAIL>", "password": "********"})

    teststeps = [
        Step(
            RunApiGatewayRequest("get with params")
            .with_variables(**{"foo1": "bar11", "foo2": "bar21"})
            .request(
                "us-east-1",
                "test_lambda_account_api",
                "POST",
                "/account/api/v1/user/login",
            )
            .with_version("release")
            .with_headers(**{"User-Agent": "HttpRunner/${get_httprunner_version()}"})
            .with_json(
                {
                    "clientId": "fi1ynu021pdjgpzj4jtlxwq718",
                    "grantType": "password",
                    "username": "${email}",
                    "password": "${password}",
                    "deviceInfo": {
                        "deviceName": "iPhone",
                        "appVersion": "7.6",
                        "model": "iPhone 11 Pro",
                        "deviceId": "F94E2221-4CED-4305-89A8-DE18EBA7DE97",
                    },
                }
            )
            .extract()
            .with_jmespath("request.username", "data")
        ),
    ]


if __name__ == "__main__":
    TestCaseApiGatewayRequestWithFunctions().test_start()
