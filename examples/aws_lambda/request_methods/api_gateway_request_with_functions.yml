config:
    name: "api-gateway request methods testcase with functions"
    case_id: test
    variables:
        email: "<EMAIL>"
        password: "********"

teststeps:
    -   name: get with params
        variables:
            foo1: bar11
            foo2: bar21
        api_gateway_request:
            region: us-east-1
            function: test_lambda_account_api
            method: POST
            path: /account/api/v1/user/login
            version: release
            headers:
                User-Agent: HttpRunner/${get_httprunner_version()}
            json: { "clientId": "fi1ynu021pdjgpzj4jtlxwq718",
                    "grantType": "password",
                    "username": "${email}",
                    "password": "${password}",
                    "deviceInfo":
                      {
                          "deviceName": "iPhone",
                          "appVersion": "7.6",
                          "model": "iPhone 11 Pro",
                          "deviceId": "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
                      }
            }
        extract:
            data: "request.username"
#    validate:
#        - eq: ["status_code", 200]
#        - eq: ["body.args.foo1", "bar11"]
#        - eq: ["body.args.sum_v", "3"]
#        - eq: ["body.args.foo2", "bar21"]
