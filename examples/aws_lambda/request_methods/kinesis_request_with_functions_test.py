# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: kinesis_request_with_functions.yml
from switchbotrunner import Switch<PERSON>otRun<PERSON>, Config, Step, RunRequest
from switchbotrunner import RunApiGatewayRequest
from switchbotrunner import RunKinesisRequest
from switchbotrunner import RunSqsRequest
from switchbotrunner import RunLambdaRequest


class TestCaseKinesisRequestWithFunctions(SwitchBotRunner):

    config = Config("数据转发v2上行流量测试 link_to_data WoLinkMini").variables(
        **{
            "us_sn": "qayJotTvLsW4gODB",
            "us_client_id": "BLE-E5CE8392644B",
            "ap_sn": "UpVddDIUb4HejTpe",
            "ap_client_id": "BLE-CAEAD9055236",
        }
    )

    teststeps = [
        Step(
            RunKinesisRequest("设备属于美区，在美区上报事件")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "protocolVersion": 1,
                        "count": 1,
                        "messages": [
                            {
                                "id": "40826533-d128-4750-8365-27bfe8d9b50f",
                                "eventType": "Wo.Hub.SyncBindDevices",
                                "data": {
                                    "deviceID": "caead9055236",
                                    "createTime": "${ts}",
                                    "contentType": "base64",
                                    "commandLevel": 5,
                                    "content": "CMD|2001a000A|AAAYAA==18",
                                },
                            }
                        ],
                    },
                }
            )
            .request(
                "us-east-1",
                "test_lambda_iot_link_to_router",
                {
                    "sn": "${us_sn}",
                    "eventType": "link_to_data",
                    "clientId": "${us_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .with_version("release")
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "us-east-1"
            )
            .assert_logs(
                "handleDataReportManager", "${mqtt_data}", "${ts}", "us-east-1"
            )
            .assert_not_logs(
                "handleDataReportManager", "${mqtt_data}", "${ts}", "ap-northeast-1"
            )
        ),
        Step(
            RunKinesisRequest("设备属于美区，在亚区上报事件")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "protocolVersion": 1,
                        "count": 1,
                        "messages": [
                            {
                                "id": "40826533-d128-4750-8365-27bfe8d9b50f",
                                "eventType": "Wo.Hub.SyncBindDevices",
                                "data": {
                                    "deviceID": "caead9055236",
                                    "createTime": "${ts}",
                                    "contentType": "base64",
                                    "commandLevel": 5,
                                    "content": "CMD|2001a000A|AAAYAA==18",
                                },
                            }
                        ],
                    },
                }
            )
            .request(
                "ap-northeast-1",
                "test_lambda_iot_link_to_router",
                {
                    "sn": "${us_sn}",
                    "eventType": "link_to_data",
                    "clientId": "${us_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .with_version("release")
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_iot_link_to_router",
                "${mqtt_data}",
                "${ts}",
                "ap-northeast-1",
            )
            .assert_logs(
                "handleDataReportManager", "${mqtt_data}", "${ts}", "us-east-1"
            )
            .assert_not_logs(
                "handleDataReportManager", "${mqtt_data}", "${ts}", "ap-northeast-1"
            )
        ),
        Step(
            RunKinesisRequest("设备属于亚区，在美区上报事件")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "protocolVersion": 1,
                        "count": 1,
                        "messages": [
                            {
                                "id": "40826533-d128-4750-8365-27bfe8d9b50f",
                                "eventType": "Wo.Hub.SyncBindDevices",
                                "data": {
                                    "deviceID": "caead9055236",
                                    "createTime": "${ts}",
                                    "contentType": "base64",
                                    "commandLevel": 5,
                                    "content": "CMD|2001a000A|AAAYAA==18",
                                },
                            }
                        ],
                    },
                }
            )
            .request(
                "us-east-1",
                "test_lambda_iot_link_to_router",
                {
                    "sn": "${ap_sn}",
                    "eventType": "link_to_data",
                    "clientId": "${ap_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .with_version("release")
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "us-east-1"
            )
            .assert_logs(
                "handleDataReportManager", "${mqtt_data}", "${ts}", "ap-northeast-1"
            )
            .assert_not_logs(
                "handleDataReportManager", "${mqtt_data}", "${ts}", "us-east-1"
            )
        ),
        Step(
            RunKinesisRequest("设备属于亚区，在亚区上报事件")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "protocolVersion": 1,
                        "count": 1,
                        "messages": [
                            {
                                "id": "40826533-d128-4750-8365-27bfe8d9b50f",
                                "eventType": "Wo.Hub.SyncBindDevices",
                                "data": {
                                    "deviceID": "caead9055236",
                                    "createTime": "${ts}",
                                    "contentType": "base64",
                                    "commandLevel": 5,
                                    "content": "CMD|2001a000A|AAAYAA==18",
                                },
                            }
                        ],
                    },
                }
            )
            .request(
                "ap-northeast-1",
                "test_lambda_iot_link_to_router",
                {
                    "sn": "${ap_sn}",
                    "eventType": "link_to_data",
                    "clientId": "${ap_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .with_version("release")
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_iot_link_to_router",
                "${mqtt_data}",
                "${ts}",
                "ap-northeast-1",
            )
            .assert_logs(
                "handleDataReportManager", "${mqtt_data}", "${ts}", "ap-northeast-1"
            )
            .assert_not_logs(
                "handleDataReportManager", "${mqtt_data}", "${ts}", "us-east-1"
            )
        ),
    ]


if __name__ == "__main__":
    TestCaseKinesisRequestWithFunctions().test_start()
