config:
    name: "test_lambda_iot_app_to_link_router"
    aws_env: test
    variables:
        us_sn: "qayJotTvLsW4gODB" # gTWjrVjcTJZ4M1Zp,HMM-D4D975352C7A
        us_client_id: "BLE-E5CE8392644B"
        ap_sn: "UpVddDIUb4HejTpe"
        ap_client_id: "BLE-CAEAD9055236"

teststeps:
    -   name: 设备归属美区，APP在美区下发的控制命令，本区不再转发iot
        variables:
            ts: ${timestamp()}
        kinesis_request:
            region: us-east-1
            function: test_lambda_iot_app_to_link_router
            version: release
            data: {
                    "sn": "${us_sn}",
                    "eventType": "app_to_link",
                    "clientId": "APP_${ts}",
                    "data": {"type":"Buffer","data":[67,77,68,32,51,53,48,50,55,49,48,50,51,32,0,0,30,111,246,20,141,127,88,86,17,153,87,75,135,42,128,248,16,247,153,105,45,122,12,146,129,149,0]},
                    "timestamp": "${ts}"
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "`botRegion(us)` is the same as `currentRegion(us)`, skip!"]
    # -   name: 设备归属亚区，APP在美区下发的控制命令，转发到跨区iot
    #     variables:
    #         ts: ${timestamp()}
    #     kinesis_request:
    #         region: us-east-1
    #         function: test_lambda_iot_app_to_link_router
    #         version: release
    #         data: {
    #                 "sn": "${ap_sn}",
    #                 "eventType": "app_to_link",
    #                 "clientId": "APP_${ts}",
    #                 "data": {"type":"Buffer","data":[67,77,68,32,51,53,48,50,55,49,48,50,51,32,0,0,30,111,246,20,141,127,88,86,17,153,87,75,135,42,128,248,16,247,153,105,45,122,12,146,129,149,0]},
    #                 "timestamp": "${ts}"
    #             }
    #     extract:
    #         lambda_log: 'log_result'
    #     validate:
    #         - contains: ["${lambda_log}", "app_to_link, resendRegion: ap"]
    # -   name: 在美区下发的控制命令，保留APP，过滤丢弃后台的
    #     variables:
    #         ts: ${timestamp()}
    #     kinesis_request:
    #         region: us-east-1
    #         function: test_lambda_iot_app_to_link_router
    #         version: release
    #         data: {
    #                 "sn": "${us_sn}",
    #                 "eventType": "app_to_link",
    #                 "clientId": "test_${ts}",
    #                 "data": {"type":"Buffer","data":[67,77,68,32,51,53,48,50,55,49,48,50,51,32,0,0,30,111,246,20,141,127,88,86,17,153,87,75,135,42,128,248,16,247,153,105,45,122,12,146,129,149,0]},
    #                 "timestamp": "${ts}"
    #             }
    #     extract:
    #         lambda_log: 'log_result'
    #     validate:
    #         - contains: ["${lambda_log}", "clientId is not 'APP_', skip!"]
    # -   name: 设备归属亚区，APP在亚区下发的控制命令，本区不再转发iot
    #     variables:
    #         ts: ${timestamp()}
    #     kinesis_request:
    #         region: ap-northeast-1
    #         function: test_lambda_iot_app_to_link_router
    #         version: release
    #         data: {
    #                 "sn": "${ap_sn}",
    #                 "eventType": "app_to_link",
    #                 "clientId": "APP_${ts}",
    #                 "data": {"type":"Buffer","data":[67,77,68,32,51,53,48,50,55,49,48,50,51,32,0,0,30,111,246,20,141,127,88,86,17,153,87,75,135,42,128,248,16,247,153,105,45,122,12,146,129,149,0]},
    #                 "timestamp": "${ts}"
    #             }
    #     extract:
    #         lambda_log: 'log_result'
    #     validate:
    #         - contains: ["${lambda_log}", "`botRegion(ap)` is the same as `currentRegion(ap)`, skip!"]
    # -   name: 设备归属美区，APP在亚区下发的控制命令，转发到跨区iot
    #     variables:
    #         ts: ${timestamp()}
    #     kinesis_request:
    #         region: ap-northeast-1
    #         function: test_lambda_iot_app_to_link_router
    #         version: release
    #         data: {
    #                 "sn": "${us_sn}",
    #                 "eventType": "app_to_link",
    #                 "clientId": "APP_${ts}",
    #                 "data": {"type":"Buffer","data":[67,77,68,32,51,53,48,50,55,49,48,50,51,32,0,0,30,111,246,20,141,127,88,86,17,153,87,75,135,42,128,248,16,247,153,105,45,122,12,146,129,149,0]},
    #                 "timestamp": "${ts}"
    #             }
    #     extract:
    #         lambda_log: 'log_result'
    #     validate:
    #         - contains: ["${lambda_log}", "app_to_link, resendRegion: us"]
    # -   name: 在亚区下发的控制命令，保留APP，过滤丢弃后台的
    #     variables:
    #         ts: ${timestamp()}
    #     kinesis_request:
    #         region: ap-northeast-1
    #         function: test_lambda_iot_app_to_link_router
    #         version: release
    #         data: {
    #                 "sn": "${ap_sn}",
    #                 "eventType": "app_to_link",
    #                 "clientId": "test_${ts}",
    #                 "data": {"type":"Buffer","data":[67,77,68,32,51,53,48,50,55,49,48,50,51,32,0,0,30,111,246,20,141,127,88,86,17,153,87,75,135,42,128,248,16,247,153,105,45,122,12,146,129,149,0]},
    #                 "timestamp": "${ts}"
    #             }
    #     extract:
    #         lambda_log: 'log_result'
    #     validate:
    #         - contains: ["${lambda_log}", "clientId is not 'APP_', skip!"]
