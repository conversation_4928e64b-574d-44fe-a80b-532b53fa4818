# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: lambda_iot_app_to_link_router.yml
from switchbotrunner import SwitchBotRunner, Config, Step, RunRequest
from switchbotrunner import RunApiGatewayRequest
from switchbotrunner import RunKinesisRequest
from switchbotrunner import RunSqsRequest
from switchbotrunner import RunLambdaRequest


class TestCaseLambdaIotAppToLinkRouter(SwitchBotRunner):

    config = Config("test_lambda_iot_app_to_link_router").variables(
        **{
            "us_sn": "qayJotTvLsW4gODB",
            "us_client_id": "BLE-E5CE8392644B",
            "ap_sn": "UpVddDIUb4HejTpe",
            "ap_client_id": "BLE-CAEAD9055236",
        }
    )

    teststeps = [
        Step(
            RunKinesisRequest("设备归属美区，APP在美区下发的控制命令，本区不再转发iot")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "us-east-1",
                "test_lambda_iot_app_to_link_router",
                {
                    "sn": "${us_sn}",
                    "eventType": "app_to_link",
                    "clientId": "APP_${ts}",
                    "data": {
                        "type": "Buffer",
                        "data": [
                            67,
                            77,
                            68,
                            32,
                            51,
                            53,
                            48,
                            50,
                            55,
                            49,
                            48,
                            50,
                            51,
                            32,
                            0,
                            0,
                            30,
                            111,
                            246,
                            20,
                            141,
                            127,
                            88,
                            86,
                            17,
                            153,
                            87,
                            75,
                            135,
                            42,
                            128,
                            248,
                            16,
                            247,
                            153,
                            105,
                            45,
                            122,
                            12,
                            146,
                            129,
                            149,
                            0,
                        ],
                    },
                    "timestamp": "${ts}",
                },
            )
            .with_version("release")
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains(
                "${lambda_log}",
                "`botRegion(us)` is the same as `currentRegion(us)`, skip!",
            )
        ),
    ]


if __name__ == "__main__":
    TestCaseLambdaIotAppToLinkRouter().test_start()
