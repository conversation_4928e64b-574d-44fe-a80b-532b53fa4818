# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: ./lambda_iot_app_to_router.yml
from switchbotrunner import (
    Config,
    RunApiGatewayRequest,
    RunKinesisRequest,
    RunLambdaRequest,
    RunRequest,
    RunSqsRequest,
    Step,
    SwitchBotRunner,
)


class TestCaseLambdaIotAppToRouter(SwitchBotRunner):
    config = (
        Config("test_lambda_iot_app_to_router")
        .variables(
            **{
                "us_sn": "qayJotTvLsW4gODB",
                "us_client_id": "BLE-E5CE8392644B",
                "ap_sn": "UpVddDIUb4HejTpe",
                "ap_client_id": "BLE-CAEAD9055236",
            }
        )
        .aws_env("test")
    )

    teststeps = [
        Step(
            RunKinesisRequest("设备归属美区，美区后台下发的控制命令，发往本区iot")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "us-east-1",
                "test_lambda_iot_app_to_router",
                {
                    "topic": "switchlink/${us_sn}/app_to_link",
                    "sn": "${us_sn}",
                    "data": {
                        "type": "Buffer",
                        "data": [
                            67,
                            77,
                            68,
                            32,
                            51,
                            53,
                            48,
                            50,
                            55,
                            49,
                            48,
                            50,
                            51,
                            32,
                            0,
                            0,
                            30,
                            111,
                            246,
                            20,
                            141,
                            127,
                            88,
                            86,
                            17,
                            153,
                            87,
                            75,
                            135,
                            42,
                            128,
                            248,
                            16,
                            247,
                            153,
                            105,
                            45,
                            122,
                            12,
                            146,
                            129,
                            149,
                            0,
                        ],
                    },
                },
            )
            .with_version("release")
            .with_encoded_data(False)
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_link, resendRegion: us")
        ),
        Step(
            RunKinesisRequest("设备归属亚区，美区后台下发的控制命令，发往跨区iot")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "us-east-1",
                "test_lambda_iot_app_to_router",
                {
                    "topic": "switchlink/${ap_sn}/app_to_link",
                    "sn": "${ap_sn}",
                    "data": {
                        "type": "Buffer",
                        "data": [
                            67,
                            77,
                            68,
                            32,
                            51,
                            53,
                            48,
                            50,
                            55,
                            49,
                            48,
                            50,
                            51,
                            32,
                            0,
                            0,
                            30,
                            111,
                            246,
                            20,
                            141,
                            127,
                            88,
                            86,
                            17,
                            153,
                            87,
                            75,
                            135,
                            42,
                            128,
                            248,
                            16,
                            247,
                            153,
                            105,
                            45,
                            122,
                            12,
                            146,
                            129,
                            149,
                            0,
                        ],
                    },
                },
            )
            .with_version("release")
            .with_encoded_data(False)
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_link, resendRegion: ap")
        ),
        Step(
            RunKinesisRequest("设备归属亚区，亚区后台下发的控制命令，发往本区iot")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "ap-northeast-1",
                "test_lambda_iot_app_to_router",
                {
                    "topic": "switchlink/${ap_sn}/app_to_link",
                    "sn": "${ap_sn}",
                    "data": {
                        "type": "Buffer",
                        "data": [
                            67,
                            77,
                            68,
                            32,
                            51,
                            53,
                            48,
                            50,
                            55,
                            49,
                            48,
                            50,
                            51,
                            32,
                            0,
                            0,
                            30,
                            111,
                            246,
                            20,
                            141,
                            127,
                            88,
                            86,
                            17,
                            153,
                            87,
                            75,
                            135,
                            42,
                            128,
                            248,
                            16,
                            247,
                            153,
                            105,
                            45,
                            122,
                            12,
                            146,
                            129,
                            149,
                            0,
                        ],
                    },
                },
            )
            .with_version("release")
            .with_encoded_data(False)
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_link, resendRegion: ap")
        ),
        Step(
            RunKinesisRequest("设备归属美区，亚区后台下发的控制命令，发往跨区iot")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "ap-northeast-1",
                "test_lambda_iot_app_to_router",
                {
                    "topic": "switchlink/${us_sn}/app_to_link",
                    "sn": "${us_sn}",
                    "data": {
                        "type": "Buffer",
                        "data": [
                            67,
                            77,
                            68,
                            32,
                            51,
                            53,
                            48,
                            50,
                            55,
                            49,
                            48,
                            50,
                            51,
                            32,
                            0,
                            0,
                            30,
                            111,
                            246,
                            20,
                            141,
                            127,
                            88,
                            86,
                            17,
                            153,
                            87,
                            75,
                            135,
                            42,
                            128,
                            248,
                            16,
                            247,
                            153,
                            105,
                            45,
                            122,
                            12,
                            146,
                            129,
                            149,
                            0,
                        ],
                    },
                },
            )
            .with_version("release")
            .with_encoded_data(False)
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_link, resendRegion: us")
        ),
    ]


if __name__ == "__main__":
    TestCaseLambdaIotAppToRouter().test_start()
