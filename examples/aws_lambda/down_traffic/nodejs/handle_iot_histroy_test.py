# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: ./nodejs/handle_iot_histroy.yml
from switchbotrunner import (
    Config,
    RunApiGatewayRequest,
    RunKinesisRequest,
    RunLambdaRequest,
    RunRequest,
    RunSqsRequest,
    Step,
    SwitchBotRunner,
)


class TestCaseHandle<PERSON>ot<PERSON>istroy(SwitchBotRunner):
    config = (
        Config("handle_iot_histroy")
        .variables(
            **{
                "us_user_id": "83b0dfbe-8774-4fc9-9bff-95228955bdbd",
                "us_user_name": "<EMAIL>",
                "us_hub_sn": "A2z8hejKE6uq0C2S",
                "us_hub_client_id": "BLE-CDDB08455451",
                "us_plugus_sn": "5Nr2i017t57258e6",
                "us_plugus_client_id": "ECFABC5DD129",
                "ap_user_id": "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb",
                "ap_user_name": "<EMAIL>",
                "ap_hub_sn": "JfhowtEOEJzQZaXs",
                "ap_hub_client_id": "BLE-JfhowtEOEJzQZaXs",
                "ap_plugus_sn": "uY3cFqvhy0kVhej4",
                "ap_plugus_client_id": "70041D806532",
            }
        )
        .aws_env("test")
    )

    teststeps = [
        Step(
            RunKinesisRequest("设备上报状态触发, us发往本区kds")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "us-east-1",
                "handle_iot_histroy",
                {
                    "deviceID": "${us_plugus_client_id}",
                    "deviceType": "WoPlugUS",
                    "data": [
                        {
                            "idx": "35a",
                            "utcTime": "${ts}",
                            "power": "0",
                            "onTime": "15",
                        },
                        {
                            "idx": "35b",
                            "utcTime": "${ts}",
                            "power": "0",
                            "onTime": "15",
                        },
                    ],
                },
            )
            .with_encoded_data(False)
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_link")
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
        Step(
            RunKinesisRequest("设备上报状态触发, ap发往本区kds")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "ap-northeast-1",
                "handle_iot_histroy",
                {
                    "deviceID": "${ap_plugus_client_id}",
                    "deviceType": "WoPlugUS",
                    "data": [
                        {
                            "idx": "35a",
                            "utcTime": "${ts}",
                            "power": "0",
                            "onTime": "15",
                        },
                        {
                            "idx": "35b",
                            "utcTime": "${ts}",
                            "power": "0",
                            "onTime": "15",
                        },
                    ],
                },
            )
            .with_encoded_data(False)
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_link")
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
    ]


if __name__ == "__main__":
    TestCaseHandleIotHistroy().test_start()
