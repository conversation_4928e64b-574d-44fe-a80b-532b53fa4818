# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: ./nodejs/handleHubBindMac.yml
from switchbotrunner import (
    Config,
    RunApiGatewayRequest,
    RunKinesisRequest,
    RunLambdaRequest,
    RunRequest,
    RunSqsRequest,
    Step,
    SwitchBotRunner,
)


class TestCaseHandlehubbindmac(SwitchBotRunner):
    config = (
        Config("app_to_link handleHubBindMac")
        .variables(
            **{
                "us_user_id": "83b0dfbe-8774-4fc9-9bff-95228955bdbd",
                "us_hub_sn": "A2z8hejKE6uq0C2S",
                "us_hub_client_id": "BLE-CDDB08455451",
                "us_bind_mac": "${random_mac()}",
                "ap_user_id": "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb",
                "ap_hub_sn": "JfhowtEOEJzQZaXs",
                "ap_hub_client_id": "BLE-JfhowtEOEJzQZaXs",
                "ap_bind_mac": "${random_mac()}",
            }
        )
        .aws_env("test")
    )

    teststeps = [
        Step(
            RunSqsRequest("bind 时更新维护列表, us发往本区kds")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "us-east-1",
                "handleHubBindMac",
                {
                    "userID": "${us_user_id}",
                    "operation": "bind",
                    "sn": "${us_hub_sn}",
                    "deviceMac": "${us_bind_mac}",
                    "deviceType": "WoPresence",
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_data")
            .assert_contains("${lambda_log}", "app_to_link")
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
        Step(
            RunSqsRequest("remove 时更新维护列表, us发往本区kds")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "us-east-1",
                "handleHubBindMac",
                {
                    "userID": "${us_user_id}",
                    "operation": "remove",
                    "sn": "${us_hub_sn}",
                    "deviceMac": "${us_bind_mac}",
                    "deviceType": "WoPresence",
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_data")
            .assert_contains("${lambda_log}", "app_to_link")
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
        Step(
            RunSqsRequest("bind 时更新维护列表, ap发往本区kds")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "ap-northeast-1",
                "handleHubBindMac",
                {
                    "userID": "${ap_user_id}",
                    "operation": "bind",
                    "sn": "${ap_hub_sn}",
                    "deviceMac": "${ap_bind_mac}",
                    "deviceType": "WoPresence",
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_data")
            .assert_contains("${lambda_log}", "app_to_link")
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
        Step(
            RunSqsRequest("remove 时更新维护列表, ap发往本区kds")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "ap-northeast-1",
                "handleHubBindMac",
                {
                    "userID": "${ap_user_id}",
                    "operation": "remove",
                    "sn": "${ap_hub_sn}",
                    "deviceMac": "${ap_bind_mac}",
                    "deviceType": "WoPresence",
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_data")
            .assert_contains("${lambda_log}", "app_to_link")
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
    ]


if __name__ == "__main__":
    TestCaseHandlehubbindmac().test_start()
