config:
    name: handle_iot_histroy
    aws_env: test
    variables:
        us_user_id: "83b0dfbe-8774-4fc9-9bff-95228955bdbd" # <EMAIL>
        us_user_name: "<EMAIL>"
        us_hub_sn: "A2z8hejKE6uq0C2S" # hub2
        us_hub_client_id: "BLE-CDDB08455451"
        us_plugus_sn: "5Nr2i017t57258e6" # ECFABC5DD129
        us_plugus_client_id: "ECFABC5DD129"
        ap_user_id: "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb" # <EMAIL>
        ap_user_name: "<EMAIL>"
        ap_hub_sn: "JfhowtEOEJzQZaXs" # hub2没有，这里用的hub mini
        ap_hub_client_id: "BLE-JfhowtEOEJzQZaXs"
        ap_plugus_sn: "uY3cFqvhy0kVhej4" # 70041D806532
        ap_plugus_client_id: "70041D806532"
teststeps:
    -   name: 设备上报状态触发, us发往本区kds
        variables:
            ts: ${timestamp()}
        kinesis_request:
            region: us-east-1
            function: handle_iot_histroy
            # version: release
            encoded_data: false
            data: {
                "deviceID": "${us_plugus_client_id}","deviceType": "WoPlugUS",
                "data": [
                    { "idx": "35a","utcTime": "${ts}","power": "0","onTime": "15" },
                    { "idx": "35b","utcTime": "${ts}","power": "0","onTime": "15" }
                ] }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_link"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    -   name: 设备上报状态触发, ap发往本区kds
        variables:
            ts: ${timestamp()}
        kinesis_request:
            region: ap-northeast-1
            function: handle_iot_histroy
            # version: release
            encoded_data: false
            data: {
                "deviceID": "${ap_plugus_client_id}","deviceType": "WoPlugUS",
                "data": [
                    { "idx": "35a","utcTime": "${ts}","power": "0","onTime": "15" },
                    { "idx": "35b","utcTime": "${ts}","power": "0","onTime": "15" }
                ] }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_link"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
