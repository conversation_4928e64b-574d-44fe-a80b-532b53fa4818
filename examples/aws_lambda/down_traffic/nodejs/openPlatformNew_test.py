# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: ./nodejs/openPlatformNew.yml
from switchbotrunner import (
    Config,
    RunApiGatewayRequest,
    RunKinesisRequest,
    RunLambdaRequest,
    RunRequest,
    RunSqsRequest,
    Step,
    SwitchBotRunner,
)


class TestCaseOpenplatformnew(SwitchBotRunner):
    config = (
        Config("openPlatformNew")
        .variables(
            **{
                "us_user_id": "83b0dfbe-8774-4fc9-9bff-95228955bdbd",
                "us_user_name": "<EMAIL>",
                "us_hub_sn": "A2z8hejKE6uq0C2S",
                "us_hub_client_id": "BLE-CDDB08455451",
                "us_keypadtouch": "C8D6994045CA",
                "ap_user_id": "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb",
                "ap_user_name": "<EMAIL>",
                "ap_hub_sn": "JfhowtEOEJzQZaXs",
                "ap_hub_client_id": "BLE-JfhowtEOEJzQZaXs",
                "ap_keypadtouch": "D929444ECEB9",
            }
        )
        .aws_env("test")
    )

    teststeps = [
        Step(
            RunLambdaRequest("openApi控制设备, us发往本区kds")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "us-east-1",
                "openPlatformNew",
                {
                    "operation": "deviceCommand",
                    "deviceID": "${us_keypadtouch}",
                    "body": {
                        "commandType": "command",
                        "command": "createKey",
                        "parameter": {
                            "name": "infinity1110",
                            "type": "timeLimit",
                            "password": "119908",
                            "startTime": 1699570800,
                            "endTime": 1699614000,
                        },
                    },
                    "version": 1,
                    "userName": "${us_user_name}",
                    "userID": "${us_user_id}",
                    "authType": "",
                    "platform": "openApi",
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_link")
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
        Step(
            RunLambdaRequest("openApi控制设备, ap发往本区kds")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "ap-northeast-1",
                "openPlatformNew",
                {
                    "operation": "deviceCommand",
                    "deviceID": "${ap_keypadtouch}",
                    "body": {
                        "commandType": "command",
                        "command": "createKey",
                        "parameter": {
                            "name": "infinity1110",
                            "type": "timeLimit",
                            "password": "119908",
                            "startTime": 1699570800,
                            "endTime": 1699614000,
                        },
                    },
                    "version": 1,
                    "userName": "${ap_user_name}",
                    "userID": "${ap_user_id}",
                    "authType": "",
                    "platform": "openApi",
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_link")
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
    ]


if __name__ == "__main__":
    TestCaseOpenplatformnew().test_start()
