config:
    name: "app_to_link deviceGroup"
    aws_env: test
    variables:
        # user_id: "c25ca98a-ec00-4f3c-aa78-66dc81315017" # <EMAIL>
        # group_id: "G01-1698216024891-1b79"
        us_user_id: "83b0dfbe-8774-4fc9-9bff-95228955bdbd" # <EMAIL>
        us_group_id: "G01-1655370021673-efa9"
        ap_user_id: "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb" # <EMAIL>
        ap_group_id: "G01-1689843578503-4d21"

teststeps:
    -   name: 灯泡成组设置默认上电状态(deviceLinkage), us发往本区kds
        lambda_request:
            region: us-east-1
            function: deviceGroup
            # version: release
            # 用户设备2个以上，会给用户设备列表中的灯泡发送app_to_link
            event: {
                "userID": "${us_user_id}",
                "operation": "deviceLinkage",
                "body": {
                    "deviceIDList": [
                        "84F70354A8E2",
                        "6055F9366212"
                    ],
                    "groupID": "${us_group_id}",
                    "groupName": "FFF",
                    "masterDeviceID": "84F70354A8E2"
                }
            }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    -   name: 灯泡成组设置默认上电状态(deviceLinkage), ap发往本区kds
        lambda_request:
            region: ap-northeast-1
            function: deviceGroup
            # version: release
            # 这个亚区用户下面没有2个灯泡。。。
            event: {
                "userID": "${ap_user_id}",
                "operation": "deviceLinkage",
                "body": {
                    "deviceIDList": [
                        "84F70354A8E2",
                        "6055F9366212",
                    ],
                    "groupID": "${ap_group_id}",
                    "groupName": "FFF",
                    "masterDeviceID": "84F70354A8E2"
                }
            }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
