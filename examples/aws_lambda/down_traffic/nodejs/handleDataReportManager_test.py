# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: ./nodejs/handleDataReportManager.yml
from switchbotrunner import (
    Config,
    RunApiGatewayRequest,
    RunKinesisRequest,
    RunLambdaRequest,
    RunRequest,
    RunSqsRequest,
    Step,
    SwitchBotRunner,
)


class TestCaseHandledatareportmanager(SwitchBotRunner):
    config = (
        Config("handleDataReportManager")
        .variables(
            **{
                "us_hub_sn": "A2z8hejKE6uq0C2S",
                "us_hub_client_id": "BLE-CDDB08455451",
                "us_lock": "CECEC8925F1D",
                "ap_hub_sn": "JfhowtEOEJzQZaXs",
                "ap_hub_client_id": "BLE-JfhowtEOEJzQZaXs",
                "ap_lock": "D84876CBE55E",
            }
        )
        .aws_env("test")
    )

    teststeps = [
        Step(
            RunKinesisRequest("设备上报属性状态 link_to_report, us发往本区kds")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "dT": 116,
                        "cT": 1,
                        "isC": 1,
                        "tS": "${ts}",
                        "dM": "a0b76548653e",
                        "cD": "a0b76548653e48161c0000",
                        "bM": "ebb57df4b2e4",
                        "v": 1,
                    },
                }
            )
            .request(
                "us-east-1",
                "handleDataReportManager",
                {
                    "sn": "${ap_hub_sn}",
                    "eventType": "link_to_report",
                    "clientId": "${ap_hub_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_link")
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
        Step(
            RunKinesisRequest("设备上报属性状态 link_to_TEST_shadow CMD, us发往本区kds")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "us-east-1",
                "handleDataReportManager",
                {
                    "sn": "${us_hub_sn}",
                    "eventType": "link_to_TEST_shadow",
                    "clientId": "${us_hub_client_id}",
                    "data": "CMD|01010300A|AAAYAA==18",
                    "timestamp": "${ts}",
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_link")
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
        Step(
            RunKinesisRequest("设备上报属性状态 link_to_data, us发往本区kds")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "protocolVersion": 1,
                        "count": 1,
                        "messages": [
                            {
                                "id": "40826533-d128-4750-8365-27bfe8d9b50f",
                                "eventType": "Wo.Hub.SyncBindDevices",
                                "data": {
                                    "deviceID": "caead9055236",
                                    "createTime": "${ts}",
                                    "contentType": "base64",
                                    "commandLevel": 5,
                                    "content": "CMD|2001a000A|AAAYAA==18",
                                },
                            }
                        ],
                    },
                }
            )
            .request(
                "us-east-1",
                "handleDataReportManager",
                {
                    "sn": "${us_hub_sn}",
                    "eventType": "link_to_data",
                    "clientId": "${us_hub_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_data")
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
        Step(
            RunKinesisRequest("设备上报属性状态 link_to_report, ap发往本区kds")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "dT": 72,
                        "cT": 1,
                        "isC": 1,
                        "tS": "${ts}",
                        "dM": "a0b76548653e",
                        "cD": "a0b76548653e48161c0000",
                        "bM": "ebb57df4b2e4",
                        "v": 1,
                    },
                }
            )
            .request(
                "ap-northeast-1",
                "handleDataReportManager",
                {
                    "sn": "${ap_hub_sn}",
                    "eventType": "link_to_report",
                    "clientId": "${ap_hub_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_link")
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
        Step(
            RunKinesisRequest("设备上报属性状态 link_to_TEST_shadow CMD, ap发往本区kds")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "ap-northeast-1",
                "handleDataReportManager",
                {
                    "sn": "${ap_hub_sn}",
                    "eventType": "link_to_TEST_shadow",
                    "clientId": "${ap_hub_client_id}",
                    "data": "CMD|01010300A|AAAYAA==18",
                    "timestamp": "${ts}",
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_link")
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
        Step(
            RunKinesisRequest("设备上报属性状态 link_to_data, ap发往本区kds")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "protocolVersion": 1,
                        "count": 1,
                        "messages": [
                            {
                                "id": "40826533-d128-4750-8365-27bfe8d9b50f",
                                "eventType": "Wo.Hub.SyncBindDevices",
                                "data": {
                                    "deviceID": "caead9055236",
                                    "createTime": "${ts}",
                                    "contentType": "base64",
                                    "commandLevel": 5,
                                    "content": "CMD|2001a000A|AAAYAA==18",
                                },
                            }
                        ],
                    },
                }
            )
            .request(
                "ap-northeast-1",
                "handleDataReportManager",
                {
                    "sn": "${ap_hub_sn}",
                    "eventType": "link_to_data",
                    "clientId": "${ap_hub_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_data")
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
    ]


if __name__ == "__main__":
    TestCaseHandledatareportmanager().test_start()
