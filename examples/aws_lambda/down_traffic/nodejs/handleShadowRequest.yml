config:
    name: "handleShadowRequest"
    aws_env: test
    variables:
        us_user_id: "83b0dfbe-8774-4fc9-9bff-95228955bdbd" # <EMAIL>
        us_hub_sn: "A2z8hejKE6uq0C2S" # hub2
        us_hub_client_id: "BLE-CDDB08455451"
        us_lock: "E19E217C99F7"
        ap_user_id: "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb" # <EMAIL>
        ap_hub_sn: "JfhowtEOEJzQZaXs" # hub2没有，这里用的hub mini
        ap_hub_client_id: "BLE-JfhowtEOEJzQZaXs"
        ap_lock: "D84876CBE55E"
        # not used: handleInitMacBindList -> sendIotMsgToDevices -> callbackIotToLinkV1(app_to_link)
        # handleUpdataHubBindList -> 
teststeps:
    -   name: 先连接, us发往本区kds
        variables:
            ts: ${timestamp()}
        sqs_request:
            region: us-east-1
            function: handleShadowRequest
            body: {
                "clientId":"${us_hub_sn}","timestamp":"${ts}","eventType":"connected",
                "sessionIdentifier":"8aa3f48f-0993-4600-8077-67a3614f849e",
                "principalIdentifier":"3ea23b147400fea5b38af7bcd6a05ca20280a4f6838ef5360957c434b38cc10f",
                "ipAddress":"**************","versionNumber":1771
                }
    -   name: 先连接再断开更新维护列表, us发往本区kds
        variables:
            ts: ${timestamp()}
        sqs_request:
            region: us-east-1
            function: handleConnectEvent
            # (disconnected_delay)handleDelayMsg -> (subDeviceDisconnect)handleSubDeviceOffline -> 
            # version: release
            body: {
                "deviceID": "${us_lock}",
                "power": "off",
                "sn": "${us_hub_sn}",
                "eventType": "disconnected_delay",
                "action": "subDeviceDisconnect"
            }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_link", "需要绑定真的子设备呀"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    
