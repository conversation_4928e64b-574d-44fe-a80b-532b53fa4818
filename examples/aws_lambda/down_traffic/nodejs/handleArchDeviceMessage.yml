config:
    name: "handleArchDeviceMessage"
    aws_env: test
    variables:
        us_user_id: "83b0dfbe-8774-4fc9-9bff-95228955bdbd" # <EMAIL>
        us_hub_sn: "A2z8hejKE6uq0C2S" # hub2
        us_hub_client_id: "BLE-CDDB08455451"
        us_lock: "CECEC8925F1D"
        ap_user_id: "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb" # <EMAIL>
        ap_hub_sn: "JfhowtEOEJzQZaXs" # hub2没有，这里用的hub mini
        ap_hub_client_id: "BLE-JfhowtEOEJzQZaXs"
        ap_lock: "D84876CBE55E"
        # not used: handleInitMacBindList -> sendIotMsgToDevices -> callbackIotToLinkV1(app_to_link)
        # handleUpdataHubBindList -> 
teststeps:
    -   name: arch联网成功, us发往本区kds
    -   name: arch 2.0更新维护列表, us发往本区kds
        variables:
            ts: ${timestamp()}
        # 需要绑定真的子设备
        # kds 和 sqs 都有
        kinesis_request:
            region: us-east-1
            function: handleArchDeviceMessage
            # version: release
            data: {
                "action": "updateDeviceBindList",
                "deviceMac": "${us_lock}",
                "sn": "${us_hub_sn}",
                "signal": "1e",
                "clientId": "${us_hub_client_id}",
                "hubArch": "2",
                "deviceType": "24"
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_link", "需要绑定真的子设备呀"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    
