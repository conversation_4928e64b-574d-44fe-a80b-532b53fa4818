# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: ./nodejs/deviceGroup.yml
from switchbotrunner import (
    Config,
    RunApiGatewayRequest,
    RunKinesisRequest,
    RunLambdaRequest,
    RunRequest,
    RunSqsRequest,
    Step,
    SwitchBotRunner,
)


class TestCaseDevicegroup(SwitchBotRunner):
    config = (
        Config("app_to_link deviceGroup")
        .variables(
            **{
                "us_user_id": "83b0dfbe-8774-4fc9-9bff-95228955bdbd",
                "us_group_id": "G01-1655370021673-efa9",
                "ap_user_id": "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb",
                "ap_group_id": "G01-1689843578503-4d21",
            }
        )
        .aws_env("test")
    )

    teststeps = [
        Step(
            RunLambdaRequest("灯泡成组设置默认上电状态(deviceLinkage), us发往本区kds")
            .request(
                "us-east-1",
                "deviceGroup",
                {
                    "userID": "${us_user_id}",
                    "operation": "deviceLinkage",
                    "body": {
                        "deviceIDList": ["84F70354A8E2", "6055F9366212"],
                        "groupID": "${us_group_id}",
                        "groupName": "FFF",
                        "masterDeviceID": "84F70354A8E2",
                    },
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
        Step(
            RunLambdaRequest("灯泡成组设置默认上电状态(deviceLinkage), ap发往本区kds")
            .request(
                "ap-northeast-1",
                "deviceGroup",
                {
                    "userID": "${ap_user_id}",
                    "operation": "deviceLinkage",
                    "body": {
                        "deviceIDList": ["84F70354A8E2", "6055F9366212"],
                        "groupID": "${ap_group_id}",
                        "groupName": "FFF",
                        "masterDeviceID": "84F70354A8E2",
                    },
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
    ]


if __name__ == "__main__":
    TestCaseDevicegroup().test_start()
