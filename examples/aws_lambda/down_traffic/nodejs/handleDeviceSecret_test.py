# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: ./nodejs/handleDeviceSecret.yml
from switchbotrunner import (
    Config,
    RunApiGatewayRequest,
    RunKinesisRequest,
    RunLambdaRequest,
    RunRequest,
    RunSqsRequest,
    Step,
    SwitchBotRunner,
)


class TestCaseHandledevicesecret(SwitchBotRunner):
    config = (
        Config("app_to_secret handleDeviceSecret")
        .variables(
            **{
                "us_hub_sn": "A2z8hejKE6uq0C2S",
                "us_hub_client_id": "BLE-CDDB08455451",
                "us_lock": "CECEC8925F1D",
                "ap_hub_sn": "JfhowtEOEJzQZaXs",
                "ap_hub_client_id": "BLE-JfhowtEOEJzQZaXs",
                "ap_lock": "D84876CBE55E",
            }
        )
        .aws_env("test")
    )

    teststeps = [
        Step(
            RunKinesisRequest("matter添加lock, us发往本区kds")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "us-east-1",
                "handleDeviceSecret",
                {
                    "sn": "${us_hub_sn}",
                    "eventType": "link_to_secret",
                    "clientId": "${us_hub_client_id}",
                    "data": "434d44204646303231313030632000001d6f${us_lock}0350",
                    "timestamp": "${ts}",
                },
            )
            .with_data_from_hex(True)
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
        Step(
            RunKinesisRequest("matter添加lock, ap发往本区kds")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "ap-northeast-1",
                "handleDeviceSecret",
                {
                    "sn": "${ap_hub_sn}",
                    "eventType": "link_to_secret",
                    "clientId": "${ap_hub_client_id}",
                    "data": "434d44204646303231313030632000001d6f${ap_lock}0350",
                    "timestamp": "${ts}",
                },
            )
            .with_data_from_hex(True)
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
    ]


if __name__ == "__main__":
    TestCaseHandledevicesecret().test_start()
