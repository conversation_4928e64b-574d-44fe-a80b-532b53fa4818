config:
    name: "数据转发上行流量测试 link_to_report"
    variables:
        us_sn: "qayJotTvLsW4gODB" # WoLinkMini
        us_client_id: "BLE-E5CE8392644B"
        ap_sn: "UpVddDIUb4HejTpe"
        ap_client_id: "BLE-CAEAD9055236"

teststeps:
    -   name: "设备属于美区，在亚区上报事件"
        variables:
            ts: ${timestamp()}
            # cD  [12:14]是设备类型 "48": "WoHand" a0b76548653e48161c0000
            message: {
                "dT":35,
                "cT":1,
                "isC":1,
                "tS":"${ts}",
                "dM":"a0b76548653e",
                "cD":"a0b76548653e48161c0000",
                "bM":"ebb57df4b2e4",
                "v":1
                }
        kinesis_request:
            region: ap-northeast-1
            function: test_lambda_data_distribute_property
#            version: release
            data:
                {
                     "sn": "${us_sn}",
                    "eventType": "link_to_report",
                    "clientId": "${us_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}"
                }
        extract:
            mqtt_data: "request.data"
        validate_log:
            - logs: ["test_lambda_data_distribute_property", "${mqtt_data}", "${ts}", "ap-northeast-1"]
            - logs: ["handleDataReportManager", "${mqtt_data}", "${ts}", "us-east-1"]
            - not_logs: ["handleDataReportManager", "${mqtt_data}", "${ts}", "ap-northeast-1"]
    -   name: "设备属于亚区，在美区上报事件"
        variables:
            ts: ${timestamp()}
            # cD  [12:14]是设备类型 "48": "WoHand" a0b76548653e48161c0000
            message: {
                "dT":35,
                "cT":1,
                "isC":1,
                "tS":"${ts}",
                "dM":"a0b76548653e",
                "cD":"a0b76548653e48161c0000",
                "bM":"ebb57df4b2e4",
                "v":1
                }
        kinesis_request:
            region: us-east-1
            function: test_lambda_data_distribute_property
#            version: release
            data:
                {
                     "sn": "${ap_sn}",
                    "eventType": "link_to_report",
                    "clientId": "${ap_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}"
                }
        extract:
            mqtt_data: "request.data"
        validate_log:
            - logs: ["test_lambda_data_distribute_property", "${mqtt_data}", "${ts}", "us-east-1"]
            - logs: ["handleDataReportManager", "${mqtt_data}", "${ts}", "ap-northeast-1"]
            - not_logs: ["handleDataReportManager", "${mqtt_data}", "${ts}", "us-east-1"]
