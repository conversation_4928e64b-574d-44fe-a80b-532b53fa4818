# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: link_to_TEST_shadow.yml
from switchbotrunner import SwitchBotRunner, Config, Step, RunRequest
from switchbotrunner import RunApiGatewayRequest
from switchbotrunner import RunKinesisRequest
from switchbotrunner import RunSqsRequest
from switchbotrunner import RunLambdaRequest


class TestCaseLinkToTestShadow(SwitchBotRunner):

    config = Config("数据转发上行流量测试 link_to_TEST_shadow").variables(
        **{
            "us_sn": "fO6zUtYSczVzP8fz",
            "us_client_id": "HUB2-C9F8C8F7368C",
            "ap_sn": "UpVddDIUb4HejTpe",
            "ap_client_id": "BLE-CAEAD9055236",
        }
    )

    teststeps = [
        Step(
            RunKinesisRequest("设备属于亚区，在美区上报事件")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "us-east-1",
                "test_lambda_data_distribute_property",
                {
                    "sn": "${ap_sn}",
                    "eventType": "link_to_TEST_shadow",
                    "clientId": "${ap_client_id}",
                    "data": "BCD ff0100000 76C9F8C8F7368C7cdfa15e7f040bfd8764df4f4680060897b2 ${ts}",
                    "timestamp": "${ts}",
                },
            )
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_data_distribute_property",
                "${mqtt_data}",
                "${ts}",
                "us-east-1",
            )
            .assert_logs("handleIoTCommand", "${mqtt_data}", "${ts}", "ap-northeast-1")
        ),
    ]


if __name__ == "__main__":
    TestCaseLinkToTestShadow().test_start()
