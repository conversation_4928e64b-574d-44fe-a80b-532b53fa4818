config:
    name: "数据转发上行流量测试 link_to_status"
    variables:
        us_sn: "fO6zUtYSczVzP8fz" # WoLinkMini 其实需要HUB2
        us_client_id: "HUB2-C9F8C8F7368C"
        ap_sn: "UpVddDIUb4HejTpe"
        ap_client_id: "BLE-CAEAD9055236"
teststeps:
#    -   name: "设备属于美区，在亚区上报事件"
#        variables:
#            ts: ${timestamp()}
#        kinesis_request:
#            region: ap-northeast-1
#            function: test_lambda_data_distribute_property # prod_lambda_data_distribute_link_shandow prod_lambda_data_distribute_link_shandow_2
##            version: release
#            data:
#                {
#                    "sn": "${us_sn}",
#                    "eventType": "link_to_TEST_shadow",
#                    "clientId": "${us_client_id}",
#                    "data": "BCD ff0100000 76C9F8C8F7368C7cdfa15e7f040bfd8764df4f4680060897b2 ${ts}",
#                    "timestamp": "${ts}"
#                }
#        extract:
#            mqtt_data: "request.data"
#        validate_log:
#            - logs: [ "test_lambda_data_distribute_property", "${mqtt_data}", "${ts}", "ap-northeast-1" ]
#            - logs: [ "handleIoTCommand", "${mqtt_data}", "${ts}", "us-east-1" ]
    -   name: "设备属于亚区，在美区上报事件"
        variables:
            ts: ${timestamp()}
        kinesis_request:
            region: us-east-1
            function: test_lambda_data_distribute_property # prod_lambda_data_distribute_link_shandow prod_lambda_data_distribute_link_shandow_2
#            version: release
            data:
                {
                    "sn": "${ap_sn}",
                    "eventType": "link_to_TEST_shadow",
                    "clientId": "${ap_client_id}",
                    "data": "BCD ff0100000 76C9F8C8F7368C7cdfa15e7f040bfd8764df4f4680060897b2 ${ts}",
                    "timestamp": "${ts}"
                }
        extract:
            mqtt_data: "request.data"
        validate_log:
            - logs: [ "test_lambda_data_distribute_property", "${mqtt_data}", "${ts}", "us-east-1" ]
            - logs: [ "handleIoTCommand", "${mqtt_data}", "${ts}", "ap-northeast-1" ]
