config:
    name: "数据转发v2上行流量测试 link_to_data WoLinkMini"
    variables:
        us_sn: "qayJotTvLsW4gODB" # WoLinkMini
        us_client_id: "BLE-E5CE8392644B"
        ap_sn: "UpVddDIUb4HejTpe"
        ap_client_id: "BLE-CAEAD9055236"

teststeps:
    -   name: "设备属于美区，在美区上报事件"
        variables:
            ts: ${timestamp()}
            message:
                {"protocolVersion":1,"count":1,"messages":[
                    {"id":"40826533-d128-4750-8365-27bfe8d9b50f","eventType":"Wo.Hub.SyncBindDevices",
                     "data":
                       {"deviceID":"caead9055236","createTime":"${ts}","contentType":"base64",
                        "commandLevel":5,"content":"CMD|2001a000A|AAAYAA==18"
                       }
                    }
                ]}
        kinesis_request:
            region: us-east-1
            function: test_lambda_iot_link_to_router
            version: release
            data:
                {
                    "sn": "${us_sn}",
                    "eventType": "link_to_data",
                    "clientId": "${us_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}"
                }
        extract:
            mqtt_data: "request.data"
        validate_log:
            - logs: ["test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "us-east-1"]
            - logs: ["handleDataReportManager", "${mqtt_data}", "${ts}", "us-east-1"]
            - not_logs: ["handleDataReportManager", "${mqtt_data}", "${ts}", "ap-northeast-1"]
    -   name: "设备属于美区，在亚区上报事件"
        variables:
            ts: ${timestamp()}
            message:
              { "protocolVersion": 1,"count": 1,"messages": [
                  { "id": "40826533-d128-4750-8365-27bfe8d9b50f","eventType": "Wo.Hub.SyncBindDevices",
                    "data":
                      { "deviceID": "caead9055236","createTime": "${ts}","contentType": "base64",
                        "commandLevel": 5,"content": "CMD|2001a000A|AAAYAA==18"
                      }
                  }
              ] }
        kinesis_request:
            region: ap-northeast-1
            function: test_lambda_iot_link_to_router
            version: release
            data:
              {
                  "sn": "${us_sn}",
                  "eventType": "link_to_data",
                  "clientId": "${us_client_id}",
                  "data": "${message}",
                  "timestamp": "${ts}"
              }
        extract:
            mqtt_data: "request.data"
        validate_log:
            - logs: ["test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "ap-northeast-1" ]
            - logs: [ "handleDataReportManager", "${mqtt_data}", "${ts}", "us-east-1" ]
            - not_logs: [ "handleDataReportManager", "${mqtt_data}", "${ts}", "ap-northeast-1" ]
    -   name: "设备属于亚区，在美区上报事件"
        variables:
            ts: ${timestamp()}
            message:
              { "protocolVersion": 1,"count": 1,"messages": [
                  { "id": "40826533-d128-4750-8365-27bfe8d9b50f","eventType": "Wo.Hub.SyncBindDevices",
                    "data":
                      { "deviceID": "caead9055236","createTime": "${ts}","contentType": "base64",
                        "commandLevel": 5,"content": "CMD|2001a000A|AAAYAA==18"
                      }
                  }
              ] }
        kinesis_request:
            region: us-east-1
            function: test_lambda_iot_link_to_router
            version: release
            data:
              {
                  "sn": "${ap_sn}",
                  "eventType": "link_to_data",
                  "clientId": "${ap_client_id}",
                  "data": "${message}",
                  "timestamp": "${ts}"
              }
        extract:
            mqtt_data: "request.data"
        validate_log:
            - logs: ["test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "us-east-1"]
            - logs: ["handleDataReportManager", "${mqtt_data}", "${ts}", "ap-northeast-1"]
            - not_logs: ["handleDataReportManager", "${mqtt_data}", "${ts}", "us-east-1"]
    -   name: "设备属于亚区，在亚区上报事件"
        variables:
            ts: ${timestamp()}
            message:
              { "protocolVersion": 1,"count": 1,"messages": [
                  { "id": "40826533-d128-4750-8365-27bfe8d9b50f","eventType": "Wo.Hub.SyncBindDevices",
                    "data":
                      { "deviceID": "caead9055236","createTime": "${ts}","contentType": "base64",
                        "commandLevel": 5,"content": "CMD|2001a000A|AAAYAA==18"
                      }
                  }
              ] }
        kinesis_request:
            region: ap-northeast-1
            function: test_lambda_iot_link_to_router
            version: release
            data:
              {
                  "sn": "${ap_sn}",
                  "eventType": "link_to_data",
                  "clientId": "${ap_client_id}",
                  "data": "${message}",
                  "timestamp": "${ts}"
              }
        extract:
            mqtt_data: "request.data"
        validate_log:
            - logs: ["test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "ap-northeast-1"]
            - logs: ["handleDataReportManager", "${mqtt_data}", "${ts}", "ap-northeast-1"]
            - not_logs: ["handleDataReportManager", "${mqtt_data}", "${ts}", "us-east-1"]
