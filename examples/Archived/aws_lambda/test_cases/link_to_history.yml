config:
    name: "数据转发v2上行流量测试 link_to_history"
    variables:
        us_sn: "56Z6ni1yoW8dKpae"
        us_client_id: "PLUG-70041D823202"
        ap_sn: "nyMZXyTZoQUUYtfO"
        ap_client_id: "PLUG-3C6105EC9003"

teststeps:
    -   name: "设备属于美区，在美区上报事件"
        variables:
            ts: ${timestamp()}
            message:
                {
                    "deviceID":"70041D823202","deviceType":"WoPlugUS",
                    "data":[
                          {"idx":"35a","utcTime":"${ts}","power":"0","onTime":"15"},
                          {"idx":"35b","utcTime":"${ts}","power":"0","onTime":"15"}
                    ]}
        kinesis_request:
            region: us-east-1
            function: test_lambda_iot_link_to_router
            version: release
            data:
                {
                    "sn": "${us_sn}",
                    "eventType": "link_to_history",
                    "clientId": "${us_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}"
                }
        extract:
            mqtt_data: "request.data"
        validate_log:
            - logs: ["test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "us-east-1"]
            - logs: ["handle_iot_histroy", "${mqtt_data}", "${ts}", "us-east-1"]
            - not_logs: ["handle_iot_histroy", "${mqtt_data}", "${ts}", "ap-northeast-1"]
    -   name: "设备属于美区，在亚区上报事件"
        variables:
            ts: ${timestamp()}
            message:
              {"deviceID": "70041D823202","deviceType": "WoPlugUS",
                  "data": [
                      { "idx": "35a","utcTime": "${ts}","power": "0","onTime": "15" },
                      { "idx": "35b","utcTime": "${ts}","power": "0","onTime": "15" }
                  ] }
        kinesis_request:
            region: ap-northeast-1
            function: test_lambda_iot_link_to_router
            version: release
            data:
                {
                    "sn": "${us_sn}",
                    "eventType": "link_to_history",
                    "clientId": "${us_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}"
                }
        extract:
            mqtt_data: "request.data"
        validate_log:
            - logs: ["test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "ap-northeast-1" ]
            - logs: [ "handle_iot_histroy", "${mqtt_data}", "${ts}", "us-east-1" ]
            - not_logs: [ "handle_iot_histroy", "${mqtt_data}", "${ts}", "ap-northeast-1" ]
    -   name: "设备属于亚区，在美区上报事件"
        variables:
            ts: ${timestamp()}
            message:
                {
                    "deviceID":"70041D823202","deviceType":"WoPlugUS",
                    "data":[
                          {"idx":"35a","utcTime":"${ts}","power":"0","onTime":"15"},
                          {"idx":"35b","utcTime":"${ts}","power":"0","onTime":"15"}
                    ]}
        kinesis_request:
            region: us-east-1
            function: test_lambda_iot_link_to_router
            version: release
            data:
                {
                    "sn": "${ap_sn}",
                    "eventType": "link_to_history",
                    "clientId": "${ap_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}"
                }
        extract:
            mqtt_data: "request.data"
        validate_log:
            - logs: ["test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "us-east-1"]
            - logs: ["handle_iot_histroy", "${mqtt_data}", "${ts}", "ap-northeast-1"]
            - not_logs: ["handle_iot_histroy", "${mqtt_data}", "${ts}", "us-east-1"]
    -   name: "设备属于亚区，在亚区上报事件"
        variables:
            ts: ${timestamp()}
            message:
              {
                  "deviceID": "70041D823202","deviceType": "WoPlugUS",
                  "data": [
                      { "idx": "35a","utcTime": "${ts}","power": "0","onTime": "15" },
                      { "idx": "35b","utcTime": "${ts}","power": "0","onTime": "15" }
                  ] }
        kinesis_request:
            region: ap-northeast-1
            function: test_lambda_iot_link_to_router
            version: release
            data:
              {
                  "sn": "${ap_sn}",
                  "eventType": "link_to_history",
                  "clientId": "${ap_client_id}",
                  "data": "${message}",
                  "timestamp": "${ts}"
              }
        extract:
            mqtt_data: "request.data"
        validate_log:
            -   logs: [ "test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "ap-northeast-1" ]
            -   logs: [ "handle_iot_histroy", "${mqtt_data}", "${ts}", "ap-northeast-1" ]
            -   not_logs: [ "handle_iot_histroy", "${mqtt_data}", "${ts}", "us-east-1" ]
