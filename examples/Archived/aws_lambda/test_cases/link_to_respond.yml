config:
    name: "数据转发v2上行流量测试 link_to_respond"
    variables:
        us_sn: "lCDgpBqT4FjRdc31" # WoLinkMini
#        us_client_id: "iotconsole-8c6f5b3b-74e7-4896-a0a2-8729d6b4ae54"
        us_client_id: "BLE-EEB0EBC30FE3"
        ap_sn: "UpVddDIUb4HejTpe"
        ap_client_id: "BLE-CAEAD9055236"

teststeps:
    -   name: "设备属于美区，在美区上报事件"
        variables:
            ts: ${timestamp()}
            message:
              {
                  "protocolVersion": 1,
                  "count": 1,
                  "messages": [
                      {
                          "id": "16ce4f0a-bece-4c10-83fc-f7a84fe2aaed",
                          "eventType": "Wo.Device.CommandRespond",
                          "data": {
                              "deviceID": "EEB0EBC30FE3",
                              "contentType": "hex",
                              "correlationID": "33cf51db-2faf-42dc-9250-7e677c9250c4",
                              "createTime": "${timestamp()}",
                              "commandType": 1,
                              "ackType": "succeed",
                              "content": "570101|014890"
                          }
                      }
                  ]
              }
        kinesis_request:
            region: us-east-1
            function: test_lambda_iot_link_to_router
            version: release
            data:
                {
                    "sn": "${us_sn}",
                    "eventType": "link_to_respond",
                    "clientId": "${us_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}"
                }
        extract:
            mqtt_data: "request.data"
        validate_log:
            - logs: ["test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "us-east-1"]
            - logs: ["handleIoTCommand", "${mqtt_data}", "${ts}", "us-east-1"]
            - not_logs: ["handleIoTCommand", "${mqtt_data}", "${ts}", "ap-northeast-1"]
#            - logs: ["Arch20-AckMessageConsumerFunction-v4MLMrypXaNO", "${mqtt_data}", "${ts}", "us-east-1"]  # 测试不通过
#            - not_logs: ["Arch20-AckMessageConsumerFunction-v4MLMrypXaNO", "${mqtt_data}", "${ts}", "ap-northeast-1"]# 测试不通过
    -   name: "设备属于美区，在亚区上报事件"
        variables:
            ts: ${timestamp()}
            message:
              {
                  "protocolVersion": 1,
                  "count": 1,
                  "messages": [
                      {
                          "id": "16ce4f0a-bece-4c10-83fc-f7a84fe2aaed",
                          "eventType": "Wo.Device.CommandRespond",
                          "data": {
                              "deviceID": "EEB0EBC30FE3",
                              "contentType": "hex",
                              "correlationID": "33cf51db-2faf-42dc-9250-7e677c9250c4",
                              "createTime": "${timestamp()}",
                              "commandType": 1,
                              "ackType": "succeed",
                              "content": "570101|014890"
                          }
                      }
                  ]
              }
        kinesis_request:
            region: ap-northeast-1
            function: test_lambda_iot_link_to_router
            version: release
            data:
              {
                  "sn": "${us_sn}",
                  "eventType": "link_to_respond",
                  "clientId": "${us_client_id}",
                  "data": "${message}",
                  "timestamp": "${ts}"
              }
        extract:
            mqtt_data: "request.data"
        validate_log:
            - logs: ["test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "ap-northeast-1" ]
            - logs: [ "handleIoTCommand", "${mqtt_data}", "${ts}", "us-east-1" ]
            - not_logs: [ "handleIoTCommand", "${mqtt_data}", "${ts}", "ap-northeast-1" ]
    -   name: "设备属于亚区，在美区上报事件"
        variables:
            ts: ${timestamp()}
            message:
              {
                  "protocolVersion": 1,
                  "count": 1,
                  "messages": [
                      {
                          "id": "16ce4f0a-bece-4c10-83fc-f7a84fe2aaed",
                          "eventType": "Wo.Device.CommandRespond",
                          "data": {
                              "deviceID": "EEB0EBC30FE3",
                              "contentType": "hex",
                              "correlationID": "33cf51db-2faf-42dc-9250-7e677c9250c4",
                              "createTime": "${timestamp()}",
                              "commandType": 1,
                              "ackType": "succeed",
                              "content": "570101|014890"
                          }
                      }
                  ]
              }
        kinesis_request:
            region: us-east-1
            function: test_lambda_iot_link_to_router
            version: release
            data:
              {
                  "sn": "${ap_sn}",
                  "eventType": "link_to_respond",
                  "clientId": "${ap_client_id}",
                  "data": "${message}",
                  "timestamp": "${ts}"
              }
        extract:
            mqtt_data: "request.data"
        validate_log:
            - logs: ["test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "us-east-1"]
            - logs: ["handleIoTCommand", "${mqtt_data}", "${ts}", "ap-northeast-1"]
            - not_logs: ["handleIoTCommand", "${mqtt_data}", "${ts}", "us-east-1"]
    -   name: "设备属于亚区，在亚区上报事件"
        variables:
            ts: ${timestamp()}
            message:
              {
                  "protocolVersion": 1,
                  "count": 1,
                  "messages": [
                      {
                          "id": "16ce4f0a-bece-4c10-83fc-f7a84fe2aaed",
                          "eventType": "Wo.Device.CommandRespond",
                          "data": {
                              "deviceID": "EEB0EBC30FE3",
                              "contentType": "hex",
                              "correlationID": "33cf51db-2faf-42dc-9250-7e677c9250c4",
                              "createTime": "${timestamp()}",
                              "commandType": 1,
                              "ackType": "succeed",
                              "content": "570101|014890"
                          }
                      }
                  ]
              }
        kinesis_request:
            region: ap-northeast-1
            function: test_lambda_iot_link_to_router
            version: release
            data:
              {
                  "sn": "${ap_sn}",
                  "eventType": "link_to_respond",
                  "clientId": "${ap_client_id}",
                  "data": "${message}",
                  "timestamp": "${ts}"
              }
        extract:
            mqtt_data: "request.data"
        validate_log:
            - logs: ["test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "ap-northeast-1"]
            - logs: ["handleIoTCommand", "${mqtt_data}", "${ts}", "ap-northeast-1"]
            - not_logs: ["handleIoTCommand", "${mqtt_data}", "${ts}", "us-east-1"]
