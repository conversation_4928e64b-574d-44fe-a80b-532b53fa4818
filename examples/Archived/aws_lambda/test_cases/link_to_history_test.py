# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: ./link_to_history.yml
from switchbotrunner import (
    Config,
    RunApiGatewayRequest,
    RunKinesisRequest,
    RunLambdaRequest,
    RunRequest,
    RunSqsRequest,
    Step,
    SwitchBotRunner,
)


class TestCaseLinkToHistory(SwitchBotRunner):
    config = Config("数据转发v2上行流量测试 link_to_history").variables(
        **{
            "us_sn": "56Z6ni1yoW8dKpae",
            "us_client_id": "PLUG-70041D823202",
            "ap_sn": "nyMZXyTZoQUUYtfO",
            "ap_client_id": "PLUG-3C6105EC9003",
        }
    )

    teststeps = [
        Step(
            RunKinesisRequest("设备属于美区，在美区上报事件")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "deviceID": "70041D823202",
                        "deviceType": "WoPlugUS",
                        "data": [
                            {
                                "idx": "35a",
                                "utcTime": "${ts}",
                                "power": "0",
                                "onTime": "15",
                            },
                            {
                                "idx": "35b",
                                "utcTime": "${ts}",
                                "power": "0",
                                "onTime": "15",
                            },
                        ],
                    },
                }
            )
            .request(
                "us-east-1",
                "test_lambda_iot_link_to_router",
                {
                    "sn": "${us_sn}",
                    "eventType": "link_to_history",
                    "clientId": "${us_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .with_version(release)
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "us-east-1"
            )
            .assert_logs("handle_iot_histroy", "${mqtt_data}", "${ts}", "us-east-1")
            .assert_not_logs(
                "handle_iot_histroy", "${mqtt_data}", "${ts}", "ap-northeast-1"
            )
        ),
        Step(
            RunKinesisRequest("设备属于美区，在亚区上报事件")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "deviceID": "70041D823202",
                        "deviceType": "WoPlugUS",
                        "data": [
                            {
                                "idx": "35a",
                                "utcTime": "${ts}",
                                "power": "0",
                                "onTime": "15",
                            },
                            {
                                "idx": "35b",
                                "utcTime": "${ts}",
                                "power": "0",
                                "onTime": "15",
                            },
                        ],
                    },
                }
            )
            .request(
                "ap-northeast-1",
                "test_lambda_iot_link_to_router",
                {
                    "sn": "${us_sn}",
                    "eventType": "link_to_history",
                    "clientId": "${us_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .with_version(release)
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_iot_link_to_router",
                "${mqtt_data}",
                "${ts}",
                "ap-northeast-1",
            )
            .assert_logs("handle_iot_histroy", "${mqtt_data}", "${ts}", "us-east-1")
            .assert_not_logs(
                "handle_iot_histroy", "${mqtt_data}", "${ts}", "ap-northeast-1"
            )
        ),
        Step(
            RunKinesisRequest("设备属于亚区，在美区上报事件")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "deviceID": "70041D823202",
                        "deviceType": "WoPlugUS",
                        "data": [
                            {
                                "idx": "35a",
                                "utcTime": "${ts}",
                                "power": "0",
                                "onTime": "15",
                            },
                            {
                                "idx": "35b",
                                "utcTime": "${ts}",
                                "power": "0",
                                "onTime": "15",
                            },
                        ],
                    },
                }
            )
            .request(
                "us-east-1",
                "test_lambda_iot_link_to_router",
                {
                    "sn": "${ap_sn}",
                    "eventType": "link_to_history",
                    "clientId": "${ap_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .with_version(release)
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_iot_link_to_router", "${mqtt_data}", "${ts}", "us-east-1"
            )
            .assert_logs(
                "handle_iot_histroy", "${mqtt_data}", "${ts}", "ap-northeast-1"
            )
            .assert_not_logs("handle_iot_histroy", "${mqtt_data}", "${ts}", "us-east-1")
        ),
        Step(
            RunKinesisRequest("设备属于亚区，在亚区上报事件")
            .with_variables(
                **{
                    "ts": "${timestamp()}",
                    "message": {
                        "deviceID": "70041D823202",
                        "deviceType": "WoPlugUS",
                        "data": [
                            {
                                "idx": "35a",
                                "utcTime": "${ts}",
                                "power": "0",
                                "onTime": "15",
                            },
                            {
                                "idx": "35b",
                                "utcTime": "${ts}",
                                "power": "0",
                                "onTime": "15",
                            },
                        ],
                    },
                }
            )
            .request(
                "ap-northeast-1",
                "test_lambda_iot_link_to_router",
                {
                    "sn": "${ap_sn}",
                    "eventType": "link_to_history",
                    "clientId": "${ap_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}",
                },
            )
            .with_version(release)
            .extract()
            .with_jmespath("request.data", "mqtt_data")
            .validate_log()
            .assert_logs(
                "test_lambda_iot_link_to_router",
                "${mqtt_data}",
                "${ts}",
                "ap-northeast-1",
            )
            .assert_logs(
                "handle_iot_histroy", "${mqtt_data}", "${ts}", "ap-northeast-1"
            )
            .assert_not_logs("handle_iot_histroy", "${mqtt_data}", "${ts}", "us-east-1")
        ),
    ]


if __name__ == "__main__":
    TestCaseLinkToHistory().test_start()
