config:
    name: "app_to_link deviceInfoManager"
    aws_env: test
    variables:
        # user_id: "c25ca98a-ec00-4f3c-aa78-66dc81315017" # <EMAIL>
        # group_id: "G01-1698216024891-1b79"
        us_user_id: "83b0dfbe-8774-4fc9-9bff-95228955bdbd" # <EMAIL>
        us_remote_id: "01-202306061500-64055454_7d57f239-190e-4496-a43c-86a98fbb0eff"
        ap_user_id: "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb" # <EMAIL>
        ap_remote_id: "01-202310201013-16853068"

teststeps:
    -   name: Hub2远程下载(deviceInfoManager), us发往本区kds
        lambda_request:
            region: us-east-1
            function: deviceInfoManager
            # version: release
            # WoLinkPro 其实是WoHub2
            # RemoteDeviceTable(user_id, remote_id)
            event: {
                "version": 1,
                "operation": "startRemoteDownLoad",
                "deviceType": "WoLinkPro",
                "userID": "${us_user_id}",
                "userName": "<EMAIL>",
                "appVersion": "7.8",
                "platform": "Android",
                "body": {
                    "deviceID": "EBB50DA36ED8",
                    "remoteID": "${us_remote_id}"
                    }
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    -   name: Hub2远程下载(deviceInfoManager), ap发往本区kds
        lambda_request:
            region: ap-northeast-1
            function: deviceInfoManager
            # version: release
            event: {
                "version": 1,
                "operation": "startRemoteDownLoad",
                "deviceType": "WoLinkPro",
                "userID": "${ap_user_id}",
                "userName": "<EMAIL>",
                "appVersion": "7.8",
                "platform": "Android",
                "body": {
                    "deviceID": "EBB50DA36ED8",
                    "remoteID": "${ap_remote_id}"
                    }
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
