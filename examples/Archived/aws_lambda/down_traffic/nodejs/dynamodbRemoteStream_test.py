# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: ./nodejs/dynamodbRemoteStream.yml
from switchbotrunner import (
    Config,
    RunApiGatewayRequest,
    RunKinesisRequest,
    RunLambdaRequest,
    RunRequest,
    RunSqsRequest,
    Step,
    SwitchBotRunner,
)


class TestCaseDynamodbremotestream(SwitchBotRunner):
    config = (
        Config("dynamodbRemoteStream")
        .variables(
            **{
                "us_user_id": "83b0dfbe-8774-4fc9-9bff-95228955bdbd",
                "us_hub_sn": "A2z8hejKE6uq0C2S",
                "us_hub_client_id": "BLE-CDDB08455451",
                "us_lock": "E19E217C99F7",
                "ap_user_id": "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb",
                "ap_hub_sn": "JfhowtEOEJzQZaXs",
                "ap_hub_client_id": "BLE-JfhowtEOEJzQZaXs",
                "ap_lock": "D84876CBE55E",
            }
        )
        .aws_env("test")
    )

    teststeps = [
        Step(
            RunSqsRequest("remoteTb修改事件, us发往本区kds")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "us-east-1",
                "dynamodbRemoteStream",
                {
                    "eventName": "MODIFY",
                    "dynamodb": {
                        "OldImage": {},
                        "NewImage": {
                            "keyDetail": [],
                            "code": "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",
                            "codeType": "4",
                            "parentHubMac": "CD:DB:08:45:54:51",
                            "type": 1,
                            "userID": "83b0dfbe-8774-4fc9-9bff-95228955bdbd",
                            "remoteID": "01-202307271148-10148545",
                            "zipCodeVersion": 10,
                            "infraredAnalysisEnable": False,
                            "sn": "A2z8hejKE6uq0C2S",
                            "hxdIndex": "",
                            "acType": "",
                            "remoteVibration": True,
                            "keyMap": "off:0:418;25,auto,autowin,on,1:418:418;25,auto,1,on,1:836:418;25,auto,2,on,1:1254:418;25,auto,3,on,1:1672:418;25,auto,autowin,on,2:2090:418;25,auto,1,on,2:2508:418;25,auto,2,on,2:2926:418;25,auto,3,on,2:3344:418;25,auto,autowin,on,3:3762:418;25,auto,1,on,3:4180:418;25,auto,2,on,3:4598:418;25,auto,3,on,3:5016:418;25,auto,autowin,on,autodir:5434:418;25,auto,1,on,autodir:5852:418;25,auto,2,on,autodir:6270:418;25,auto,3,on,autodir:6688:418;16,cool,autowin,on,1:7106:418;17,cool,autowin,on,1:7524:418;18,cool,autowin,on,1:7942:418;19,cool,autowin,on,1:8360:418;20,cool,autowin,on,1:8778:418;21,cool,autowin,on,1:9196:418;22,cool,autowin,on,1:9614:418;23,cool,autowin,on,1:10032:418;24,cool,autowin,on,1:10450:418;25,cool,autowin,on,1:10868:418;26,cool,autowin,on,1:11286:418;27,cool,autowin,on,1:11704:418;28,cool,autowin,on,1:12122:418;29,cool,autowin,on,1:12540:418;30,cool,autowin,on,1:12958:418;16,cool,1,on,1:13376:418;17,cool,1,on,1:13794:418;18,cool,1,on,1:14212:418;19,cool,1,on,1:14630:418;20,cool,1,on,1:15048:418;21,cool,1,on,1:15466:418;22,cool,1,on,1:15884:418;23,cool,1,on,1:16302:418;24,cool,1,on,1:16720:418;25,cool,1,on,1:17138:418;26,cool,1,on,1:17556:418;27,cool,1,on,1:17974:418;28,cool,1,on,1:18392:418;29,cool,1,on,1:18810:418;30,cool,1,on,1:19228:418;16,cool,2,on,1:19646:418;17,cool,2,on,1:20064:418;18,cool,2,on,1:20482:418;19,cool,2,on,1:20900:418;20,cool,2,on,1:21318:418;21,cool,2,on,1:21736:418;22,cool,2,on,1:22154:418;23,cool,2,on,1:22572:418;24,cool,2,on,1:22990:418;25,cool,2,on,1:23408:418;26,cool,2,on,1:23826:418;27,cool,2,on,1:24244:418;28,cool,2,on,1:24662:418;29,cool,2,on,1:25080:418;30,cool,2,on,1:25498:418;16,cool,3,on,1:25916:418;17,cool,3,on,1:26334:418;18,cool,3,on,1:26752:418;19,cool,3,on,1:27170:418;20,cool,3,on,1:27588:418;21,cool,3,on,1:28006:418;22,cool,3,on,1:28424:418;23,cool,3,on,1:28842:418;24,cool,3,on,1:29260:418;25,cool,3,on,1:29678:418;26,cool,3,on,1:30096:418;27,cool,3,on,1:30514:418;28,cool,3,on,1:30932:418;29,cool,3,on,1:31350:418;30,cool,3,on,1:31768:418;16,cool,autowin,on,2:32186:418;17,cool,autowin,on,2:32604:418;18,cool,autowin,on,2:33022:418;19,cool,autowin,on,2:33440:418;20,cool,autowin,on,2:33858:418;21,cool,autowin,on,2:34276:418;22,cool,autowin,on,2:34694:418;23,cool,autowin,on,2:35112:418;24,cool,autowin,on,2:35530:418;25,cool,autowin,on,2:35948:418;26,cool,autowin,on,2:36366:418;27,cool,autowin,on,2:36784:418;28,cool,autowin,on,2:37202:418;29,cool,autowin,on,2:37620:418;30,cool,autowin,on,2:38038:418;16,cool,1,on,2:38456:418;17,cool,1,on,2:38874:418;18,cool,1,on,2:39292:418;19,cool,1,on,2:39710:418;20,cool,1,on,2:40128:418;21,cool,1,on,2:40546:418;22,cool,1,on,2:40964:418;23,cool,1,on,2:41382:418;24,cool,1,on,2:41800:418;25,cool,1,on,2:42218:418;26,cool,1,on,2:42636:418;27,cool,1,on,2:43054:418;28,cool,1,on,2:43472:418;29,cool,1,on,2:43890:418;30,cool,1,on,2:44308:418;16,cool,2,on,2:44726:418;17,cool,2,on,2:45144:418;18,cool,2,on,2:45562:418;19,cool,2,on,2:45980:418;20,cool,2,on,2:46398:418;21,cool,2,on,2:46816:418;22,cool,2,on,2:47234:418;23,cool,2,on,2:47652:418;24,cool,2,on,2:48070:418;25,cool,2,on,2:48488:418;26,cool,2,on,2:48906:418;27,cool,2,on,2:49324:418;28,cool,2,on,2:49742:418;29,cool,2,on,2:50160:418;30,cool,2,on,2:50578:418;16,cool,3,on,2:50996:418;17,cool,3,on,2:51414:418;18,cool,3,on,2:51832:418;19,cool,3,on,2:52250:418;20,cool,3,on,2:52668:418;21,cool,3,on,2:53086:418;22,cool,3,on,2:53504:418;23,cool,3,on,2:53922:418;24,cool,3,on,2:54340:418;25,cool,3,on,2:54758:418;26,cool,3,on,2:55176:418;27,cool,3,on,2:55594:418;28,cool,3,on,2:56012:418;29,cool,3,on,2:56430:418;30,cool,3,on,2:56848:418;16,cool,autowin,on,3:57266:418;17,cool,autowin,on,3:57684:418;18,cool,autowin,on,3:58102:418;19,cool,autowin,on,3:58520:418;20,cool,autowin,on,3:58938:418;21,cool,autowin,on,3:59356:418;22,cool,autowin,on,3:59774:418;23,cool,autowin,on,3:60192:418;24,cool,autowin,on,3:60610:418;25,cool,autowin,on,3:61028:418;26,cool,autowin,on,3:61446:418;27,cool,autowin,on,3:61864:418;28,cool,autowin,on,3:62282:418;29,cool,autowin,on,3:62700:418;30,cool,autowin,on,3:63118:418;16,cool,1,on,3:63536:418;17,cool,1,on,3:63954:418;18,cool,1,on,3:64372:418;19,cool,1,on,3:64790:418;20,cool,1,on,3:65208:418;21,cool,1,on,3:65626:418;22,cool,1,on,3:66044:418;23,cool,1,on,3:66462:418;24,cool,1,on,3:66880:418;25,cool,1,on,3:67298:418;26,cool,1,on,3:67716:418;27,cool,1,on,3:68134:418;28,cool,1,on,3:68552:418;29,cool,1,on,3:68970:418;30,cool,1,on,3:69388:418;16,cool,2,on,3:69806:418;17,cool,2,on,3:70224:418;18,cool,2,on,3:70642:418;19,cool,2,on,3:71060:418;20,cool,2,on,3:71478:418;21,cool,2,on,3:71896:418;22,cool,2,on,3:72314:418;23,cool,2,on,3:72732:418;24,cool,2,on,3:73150:418;25,cool,2,on,3:73568:418;26,cool,2,on,3:73986:418;27,cool,2,on,3:74404:418;28,cool,2,on,3:74822:418;29,cool,2,on,3:75240:418;30,cool,2,on,3:75658:418;16,cool,3,on,3:76076:418;17,cool,3,on,3:76494:418;18,cool,3,on,3:76912:418;19,cool,3,on,3:77330:418;20,cool,3,on,3:77748:418;21,cool,3,on,3:78166:418;22,cool,3,on,3:78584:418;23,cool,3,on,3:79002:418;24,cool,3,on,3:79420:418;25,cool,3,on,3:79838:418;26,cool,3,on,3:80256:418;27,cool,3,on,3:80674:418;28,cool,3,on,3:81092:418;29,cool,3,on,3:81510:418;30,cool,3,on,3:81928:418;16,cool,autowin,on,autodir:82346:418;17,cool,autowin,on,autodir:82764:418;18,cool,autowin,on,autodir:83182:418;19,cool,autowin,on,autodir:83600:418;20,cool,autowin,on,autodir:84018:418;21,cool,autowin,on,autodir:84436:418;22,cool,autowin,on,autodir:84854:418;23,cool,autowin,on,autodir:85272:418;24,cool,autowin,on,autodir:85690:418;25,cool,autowin,on,autodir:86108:418;26,cool,autowin,on,autodir:86526:418;27,cool,autowin,on,autodir:86944:418;28,cool,autowin,on,autodir:87362:418;29,cool,autowin,on,autodir:87780:418;30,cool,autowin,on,autodir:88198:418;16,cool,1,on,autodir:88616:418;17,cool,1,on,autodir:89034:418;18,cool,1,on,autodir:89452:418;19,cool,1,on,autodir:89870:418;20,cool,1,on,autodir:90288:418;21,cool,1,on,autodir:90706:418;22,cool,1,on,autodir:91124:418;23,cool,1,on,autodir:91542:418;24,cool,1,on,autodir:91960:418;25,cool,1,on,autodir:92378:418;26,cool,1,on,autodir:92796:418;27,cool,1,on,autodir:93214:418;28,cool,1,on,autodir:93632:418;29,cool,1,on,autodir:94050:418;30,cool,1,on,autodir:94468:418;16,cool,2,on,autodir:94886:418;17,cool,2,on,autodir:95304:418;18,cool,2,on,autodir:95722:418;19,cool,2,on,autodir:96140:418;20,cool,2,on,autodir:96558:418;21,cool,2,on,autodir:96976:418;22,cool,2,on,autodir:97394:418;23,cool,2,on,autodir:97812:418;24,cool,2,on,autodir:98230:418;25,cool,2,on,autodir:98648:418;26,cool,2,on,autodir:99066:418;27,cool,2,on,autodir:99484:418;28,cool,2,on,autodir:99902:418;29,cool,2,on,autodir:100320:418;30,cool,2,on,autodir:100738:418;16,cool,3,on,autodir:101156:418;17,cool,3,on,autodir:101574:418;18,cool,3,on,autodir:101992:418;19,cool,3,on,autodir:102410:418;20,cool,3,on,autodir:102828:418;21,cool,3,on,autodir:103246:418;22,cool,3,on,autodir:103664:418;23,cool,3,on,autodir:104082:418;24,cool,3,on,autodir:104500:418;25,cool,3,on,autodir:104918:418;26,cool,3,on,autodir:105336:418;27,cool,3,on,autodir:105754:418;28,cool,3,on,autodir:106172:418;29,cool,3,on,autodir:106590:418;30,cool,3,on,autodir:107008:418;25,dry,autowin,on,1:107426:418;25,dry,1,on,1:107844:418;25,dry,2,on,1:108262:418;25,dry,3,on,1:108680:418;25,dry,autowin,on,2:109098:418;25,dry,1,on,2:109516:418;25,dry,2,on,2:109934:418;25,dry,3,on,2:110352:418;25,dry,autowin,on,3:110770:418;25,dry,1,on,3:111188:418;25,dry,2,on,3:111606:418;25,dry,3,on,3:112024:418;25,dry,autowin,on,autodir:112442:418;25,dry,1,on,autodir:112860:418;25,dry,2,on,autodir:113278:418;25,dry,3,on,autodir:113696:418;25,wind,autowin,on,1:114114:418;25,wind,1,on,1:114532:418;25,wind,2,on,1:114950:418;25,wind,3,on,1:115368:418;25,wind,autowin,on,2:115786:418;25,wind,1,on,2:116204:418;25,wind,2,on,2:116622:418;25,wind,3,on,2:117040:418;25,wind,autowin,on,3:117458:418;25,wind,1,on,3:117876:418;25,wind,2,on,3:118294:418;25,wind,3,on,3:118712:418;25,wind,autowin,on,autodir:119130:418;25,wind,1,on,autodir:119548:418;25,wind,2,on,autodir:119966:418;25,wind,3,on,autodir:120384:418;16,heat,autowin,on,1:120802:418;17,heat,autowin,on,1:121220:418;18,heat,autowin,on,1:121638:418;19,heat,autowin,on,1:122056:418;20,heat,autowin,on,1:122474:418;21,heat,autowin,on,1:122892:418;22,heat,autowin,on,1:123310:418;23,heat,autowin,on,1:123728:418;24,heat,autowin,on,1:124146:418;25,heat,autowin,on,1:124564:418;26,heat,autowin,on,1:124982:418;27,heat,autowin,on,1:125400:418;28,heat,autowin,on,1:125818:418;29,heat,autowin,on,1:126236:418;30,heat,autowin,on,1:126654:418;16,heat,1,on,1:127072:418;17,heat,1,on,1:127490:418;18,heat,1,on,1:127908:418;19,heat,1,on,1:128326:418;20,heat,1,on,1:128744:418;21,heat,1,on,1:129162:418;22,heat,1,on,1:129580:418;23,heat,1,on,1:129998:418;24,heat,1,on,1:130416:418;25,heat,1,on,1:130834:418;26,heat,1,on,1:131252:418;27,heat,1,on,1:131670:418;28,heat,1,on,1:132088:418;29,heat,1,on,1:132506:418;30,heat,1,on,1:132924:418;16,heat,2,on,1:133342:418;17,heat,2,on,1:133760:418;18,heat,2,on,1:134178:418;19,heat,2,on,1:134596:418;20,heat,2,on,1:135014:418;21,heat,2,on,1:135432:418;22,heat,2,on,1:135850:418;23,heat,2,on,1:136268:418;24,heat,2,on,1:136686:418;25,heat,2,on,1:137104:418;26,heat,2,on,1:137522:418;27,heat,2,on,1:137940:418;28,heat,2,on,1:138358:418;29,heat,2,on,1:138776:418;30,heat,2,on,1:139194:418;16,heat,3,on,1:139612:418;17,heat,3,on,1:140030:418;18,heat,3,on,1:140448:418;19,heat,3,on,1:140866:418;20,heat,3,on,1:141284:418;21,heat,3,on,1:141702:418;22,heat,3,on,1:142120:418;23,heat,3,on,1:142538:418;24,heat,3,on,1:142956:418;25,heat,3,on,1:143374:418;26,heat,3,on,1:143792:418;27,heat,3,on,1:144210:418;28,heat,3,on,1:144628:418;29,heat,3,on,1:145046:418;30,heat,3,on,1:145464:418;16,heat,autowin,on,2:145882:418;17,heat,autowin,on,2:146300:418;18,heat,autowin,on,2:146718:418;19,heat,autowin,on,2:147136:418;20,heat,autowin,on,2:147554:418;21,heat,autowin,on,2:147972:418;22,heat,autowin,on,2:148390:418;23,heat,autowin,on,2:148808:418;24,heat,autowin,on,2:149226:418;25,heat,autowin,on,2:149644:418;26,heat,autowin,on,2:150062:418;27,heat,autowin,on,2:150480:418;28,heat,autowin,on,2:150898:418;29,heat,autowin,on,2:151316:418;30,heat,autowin,on,2:151734:418;16,heat,1,on,2:152152:418;17,heat,1,on,2:152570:418;18,heat,1,on,2:152988:418;19,heat,1,on,2:153406:418;20,heat,1,on,2:153824:418;21,heat,1,on,2:154242:418;22,heat,1,on,2:154660:418;23,heat,1,on,2:155078:418;24,heat,1,on,2:155496:418;25,heat,1,on,2:155914:418;26,heat,1,on,2:156332:418;27,heat,1,on,2:156750:418;28,heat,1,on,2:157168:418;29,heat,1,on,2:157586:418;30,heat,1,on,2:158004:418;16,heat,2,on,2:158422:418;17,heat,2,on,2:158840:418;18,heat,2,on,2:159258:418;19,heat,2,on,2:159676:418;20,heat,2,on,2:160094:418;21,heat,2,on,2:160512:418;22,heat,2,on,2:160930:418;23,heat,2,on,2:161348:418;24,heat,2,on,2:161766:418;25,heat,2,on,2:162184:418;26,heat,2,on,2:162602:418;27,heat,2,on,2:163020:418;28,heat,2,on,2:163438:418;29,heat,2,on,2:163856:418;30,heat,2,on,2:164274:418;16,heat,3,on,2:164692:418;17,heat,3,on,2:165110:418;18,heat,3,on,2:165528:418;19,heat,3,on,2:165946:418;20,heat,3,on,2:166364:418;21,heat,3,on,2:166782:418;22,heat,3,on,2:167200:418;23,heat,3,on,2:167618:418;24,heat,3,on,2:168036:418;25,heat,3,on,2:168454:418;26,heat,3,on,2:168872:418;27,heat,3,on,2:169290:418;28,heat,3,on,2:169708:418;29,heat,3,on,2:170126:418;30,heat,3,on,2:170544:418;16,heat,autowin,on,3:170962:418;17,heat,autowin,on,3:171380:418;18,heat,autowin,on,3:171798:418;19,heat,autowin,on,3:172216:418;20,heat,autowin,on,3:172634:418;21,heat,autowin,on,3:173052:418;22,heat,autowin,on,3:173470:418;23,heat,autowin,on,3:173888:418;24,heat,autowin,on,3:174306:418;25,heat,autowin,on,3:174724:418;26,heat,autowin,on,3:175142:418;27,heat,autowin,on,3:175560:418;28,heat,autowin,on,3:175978:418;29,heat,autowin,on,3:176396:418;30,heat,autowin,on,3:176814:418;16,heat,1,on,3:177232:418;17,heat,1,on,3:177650:418;18,heat,1,on,3:178068:418;19,heat,1,on,3:178486:418;20,heat,1,on,3:178904:418;21,heat,1,on,3:179322:418;22,heat,1,on,3:179740:418;23,heat,1,on,3:180158:418;24,heat,1,on,3:180576:418;25,heat,1,on,3:180994:418;26,heat,1,on,3:181412:418;27,heat,1,on,3:181830:418;28,heat,1,on,3:182248:418;29,heat,1,on,3:182666:418;30,heat,1,on,3:183084:418;16,heat,2,on,3:183502:418;17,heat,2,on,3:183920:418;18,heat,2,on,3:184338:418;19,heat,2,on,3:184756:418;20,heat,2,on,3:185174:418;21,heat,2,on,3:185592:418;22,heat,2,on,3:186010:418;23,heat,2,on,3:186428:418;24,heat,2,on,3:186846:418;25,heat,2,on,3:187264:418;26,heat,2,on,3:187682:418;27,heat,2,on,3:188100:418;28,heat,2,on,3:188518:418;29,heat,2,on,3:188936:418;30,heat,2,on,3:189354:418;16,heat,3,on,3:189772:418;17,heat,3,on,3:190190:418;18,heat,3,on,3:190608:418;19,heat,3,on,3:191026:418;20,heat,3,on,3:191444:418;21,heat,3,on,3:191862:418;22,heat,3,on,3:192280:418;23,heat,3,on,3:192698:418;24,heat,3,on,3:193116:418;25,heat,3,on,3:193534:418;26,heat,3,on,3:193952:418;27,heat,3,on,3:194370:418;28,heat,3,on,3:194788:418;29,heat,3,on,3:195206:418;30,heat,3,on,3:195624:418;16,heat,autowin,on,autodir:196042:418;17,heat,autowin,on,autodir:196460:418;18,heat,autowin,on,autodir:196878:418;19,heat,autowin,on,autodir:197296:418;20,heat,autowin,on,autodir:197714:418;21,heat,autowin,on,autodir:198132:418;22,heat,autowin,on,autodir:198550:418;23,heat,autowin,on,autodir:198968:418;24,heat,autowin,on,autodir:199386:418;25,heat,autowin,on,autodir:199804:418;26,heat,autowin,on,autodir:200222:418;27,heat,autowin,on,autodir:200640:418;28,heat,autowin,on,autodir:201058:418;29,heat,autowin,on,autodir:201476:418;30,heat,autowin,on,autodir:201894:418;16,heat,1,on,autodir:202312:418;17,heat,1,on,autodir:202730:418;18,heat,1,on,autodir:203148:418;19,heat,1,on,autodir:203566:418;20,heat,1,on,autodir:203984:418;21,heat,1,on,autodir:204402:418;22,heat,1,on,autodir:204820:418;23,heat,1,on,autodir:205238:418;24,heat,1,on,autodir:205656:418;25,heat,1,on,autodir:206074:418;26,heat,1,on,autodir:206492:418;27,heat,1,on,autodir:206910:418;28,heat,1,on,autodir:207328:418;29,heat,1,on,autodir:207746:418;30,heat,1,on,autodir:208164:418;16,heat,2,on,autodir:208582:418;17,heat,2,on,autodir:209000:418;18,heat,2,on,autodir:209418:418;19,heat,2,on,autodir:209836:418;20,heat,2,on,autodir:210254:418;21,heat,2,on,autodir:210672:418;22,heat,2,on,autodir:211090:418;23,heat,2,on,autodir:211508:418;24,heat,2,on,autodir:211926:418;25,heat,2,on,autodir:212344:418;26,heat,2,on,autodir:212762:418;27,heat,2,on,autodir:213180:418;28,heat,2,on,autodir:213598:418;29,heat,2,on,autodir:214016:418;30,heat,2,on,autodir:214434:418;16,heat,3,on,autodir:214852:418;17,heat,3,on,autodir:215270:418;18,heat,3,on,autodir:215688:418;19,heat,3,on,autodir:216106:418;20,heat,3,on,autodir:216524:418;21,heat,3,on,autodir:216942:418;22,heat,3,on,autodir:217360:418;23,heat,3,on,autodir:217778:418;24,heat,3,on,autodir:218196:418;25,heat,3,on,autodir:218614:418;26,heat,3,on,autodir:219032:418;27,heat,3,on,autodir:219450:418;28,heat,3,on,autodir:219868:418;29,heat,3,on,autodir:220286:418;30,heat,3,on,autodir:220704:418",
                            "offLineCommandEnable": False,
                            "brandEn": "",
                            "dbVersion": 28,
                            "groupID": "G01-1655370021673-efa9",
                            "priority": "",
                            "userName": "<EMAIL>",
                            "environmentDataEnable": False,
                            "createTime": "",
                            "serial": 0,
                            "unLearnKeyEnable": True,
                            "remoteName": "空调 23",
                        },
                    },
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_link", "需要绑定真的子设备呀")
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
    ]


if __name__ == "__main__":
    TestCaseDynamodbremotestream().test_start()
