config:
    name: MeterFactoryDataDelete
    aws_env: test
    variables:
        us_user_id: "83b0dfbe-8774-4fc9-9bff-95228955bdbd" # <EMAIL>
        us_user_name: "<EMAIL>"
        us_hub_sn: "A2z8hejKE6uq0C2S" # hub2
        us_hub_client_id: "BLE-CDDB08455451"
        us_keypadtouch: "C8D6994045CA"
        ap_user_id: "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb" # <EMAIL>
        ap_user_name: "<EMAIL>"
        ap_hub_sn: "JfhowtEOEJzQZaXs" # hub2没有，这里用的hub mini
        ap_hub_client_id: "BLE-JfhowtEOEJzQZaXs"
        ap_keypadtouch: "D929444ECEB9" # 亚区keypadtouch被解绑了，还需要设备才能运行成功
teststeps:
    -   name: meter产测定制topic, us发往本区kds
        variables:
            ts: ${timestamp()}
        lambda_request:
            region: us-east-1
            function: MeterFactoryDataDelete
            # version: release
            event: 
                {
                "sn":"${us_hub_sn}",
                "clientId":"${us_hub_client_id}",
                "data":"ZDY5ZDY2ODIyODVhNTcwMGU0fGQ3NWYxY2MyODA2Mjc3MDBlNA==",
                "timestamp":"${ts}"
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_link"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    -   name: meter产测定制topic, ap发往本区kds
        variables:
            ts: ${timestamp()}
        lambda_request:
            region: ap-northeast-1
            function: MeterFactoryDataDelete
            # version: release
            event: {
                "sn":"${ap_hub_sn}",
                "clientId":"${ap_hub_client_id}",
                "data":"ZDY5ZDY2ODIyODVhNTcwMGU0fGQ3NWYxY2MyODA2Mjc3MDBlNA==",
                "timestamp":"${ts}"
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_link"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    
