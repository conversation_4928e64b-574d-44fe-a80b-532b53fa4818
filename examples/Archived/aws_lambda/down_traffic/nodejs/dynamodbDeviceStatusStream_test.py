# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: dynamodbDeviceStatusStream.yml
from switchbotrunner import (
    Config,
    RunApiGatewayRequest,
    RunKinesisRequest,
    RunLambdaRequest,
    RunRequest,
    RunSqsRequest,
    Step,
    SwitchBotRunner,
)


class TestCaseDynamodbdevicestatusstream(SwitchBotRunner):
    config = (
        Config("dynamodbDeviceStatusStream")
        .variables(
            **{
                "us_user_id": "83b0dfbe-8774-4fc9-9bff-95228955bdbd",
                "us_hub_sn": "A2z8hejKE6uq0C2S",
                "us_hub_client_id": "BLE-CDDB08455451",
                "us_lock": "E19E217C99F7",
                "ap_user_id": "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb",
                "ap_hub_sn": "JfhowtEOEJzQZaXs",
                "ap_hub_client_id": "BLE-JfhowtEOEJzQZaXs",
                "ap_lock": "D84876CBE55E",
            }
        )
        .aws_env("test")
    )

    teststeps = [
        Step(
            RunSqsRequest("先连接再断开更新维护列表, us发往本区kds")
            .with_variables(**{"ts": "${timestamp()}"})
            .request(
                "us-east-1",
                "dynamodbDeviceStatusStream",
                {
                    "deviceID": "${us_lock}",
                    "power": "off",
                    "sn": "${us_hub_sn}",
                    "eventType": "disconnected_delay",
                    "action": "subDeviceDisconnect",
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "app_to_link", "需要绑定真的子设备呀")
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
    ]


if __name__ == "__main__":
    TestCaseDynamodbdevicestatusstream().test_start()
