config:
    name: handleShadowAction
    aws_env: test
    variables:
        us_user_id: "83b0dfbe-8774-4fc9-9bff-95228955bdbd" # <EMAIL>
        us_user_name: "<EMAIL>"
        us_hub_sn: "A2z8hejKE6uq0C2S" # hub2
        us_hub_client_id: "BLE-CDDB08455451"
        us_plug_sn: "5Nr2i017t57258e6" # ECFABC5DD129
        ap_user_id: "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb" # <EMAIL>
        ap_user_name: "<EMAIL>"
        ap_hub_sn: "JfhowtEOEJzQZaXs" # hub2没有，这里用的hub mini
        ap_hub_client_id: "BLE-JfhowtEOEJzQZaXs"
teststeps:
    -   name: 通过shadow下发命令, us发往本区kds
        variables:
            ts: ${timestamp()}
        lambda_request:
            region: us-east-1
            function: handleShadowAction
            # version: release
            # 先调用openapi，向设备命令列表里追加命令，然后触发这个lambda，shadowTb对应的sn，需要有commandList待发送
            event: {"userID":"${us_user_id}","source":"request","sn":"${us_plug_sn}"}
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_link"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    # -   name: 进入lock界面获取随机密钥, ap发往本区kds
    #     variables:
    #         ts: ${timestamp()}
    #     lambda_request:
    #         region: ap-northeast-1
    #         function: handleShadowAction
    #         # version: release
    #         event: {
    #             "sn":"${ap_hub_sn}",
    #             "clientId":"${ap_hub_client_id}",
    #             "data":"ZDY5ZDY2ODIyODVhNTcwMGU0fGQ3NWYxY2MyODA2Mjc3MDBlNA==",
    #             "timestamp":"${ts}"
    #             }
    #     extract:
    #         lambda_log: 'log_result'
    #     validate:
    #         - contains: ["${lambda_log}", "app_to_link"]
    #         - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    
