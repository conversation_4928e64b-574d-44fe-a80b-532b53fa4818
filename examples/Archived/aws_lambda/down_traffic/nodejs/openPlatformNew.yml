config:
    name: openPlatformNew
    aws_env: test
    variables:
        us_user_id: "83b0dfbe-8774-4fc9-9bff-95228955bdbd" # <EMAIL>
        us_user_name: "<EMAIL>"
        us_hub_sn: "A2z8hejKE6uq0C2S" # hub2
        us_hub_client_id: "BLE-CDDB08455451"
        us_keypadtouch: "C8D6994045CA"
        ap_user_id: "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb" # <EMAIL>
        ap_user_name: "<EMAIL>"
        ap_hub_sn: "JfhowtEOEJzQZaXs" # hub2没有，这里用的hub mini
        ap_hub_client_id: "BLE-JfhowtEOEJzQZaXs"
        ap_keypadtouch: "D929444ECEB9" # 亚区报 Wrong deviceId, No this device
teststeps:
    -   name: openApi控制设备, us发往本区kds
        variables:
            ts: ${timestamp()}
        lambda_request:
            region: us-east-1
            function: openPlatformNew
            # version: release
            # (deviceCommand)deviceControl -> controlDevice -> checkDeviceStatus(DeviceStatusTb)
            # handleThirdCloudRequest -> sendCommand(vertion=2) -> handleCommand -> executeCommand -> sendIotCommand(app_to_link)
            event: {
                "operation": "deviceCommand",
                "deviceID": "${us_keypadtouch}",
                "body": {
                    "commandType": "command",
                    "command": "createKey",
                    "parameter": {
                        "name": "infinity1110",
                        "type": "timeLimit",
                        "password": "119908",
                        "startTime": 1699570800,
                        "endTime": 1699614000
                    }
                },
                "version": 1,
                "userName": "${us_user_name}",
                "userID": "${us_user_id}",
                "authType": "",
                "platform": "openApi"
            }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_link"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    -   name: openApi控制设备, ap发往本区kds
        variables:
            ts: ${timestamp()}
        lambda_request:
            region: ap-northeast-1
            function: openPlatformNew
            # version: release
            # (deviceCommand)deviceControl -> controlDevice -> checkDeviceStatus(DeviceStatusTb)
            # handleThirdCloudRequest -> sendCommand(vertion=2) -> handleCommand -> executeCommand -> sendIotCommand(app_to_link)
            event: {
                "operation": "deviceCommand",
                "deviceID": "${ap_keypadtouch}",
                "body": {
                    "commandType": "command",
                    "command": "createKey",
                    "parameter": {
                        "name": "infinity1110",
                        "type": "timeLimit",
                        "password": "119908",
                        "startTime": 1699570800,
                        "endTime": 1699614000
                    }
                },
                "version": 1,
                "userName": "${ap_user_name}",
                "userID": "${ap_user_id}",
                "authType": "",
                "platform": "openApi"
            }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_link"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    
