import base64
import json
import random
import time


def get_httprunner_version():
    return "v4.0.0-alpha"


def sum_two(a: int, b: int) -> int:
    return a + b


def b64(data: str) -> str:
    if isinstance(data, dict):
        data = json.dumps(data)
    return base64.b64encode(data.encode("utf-8")).decode("utf-8")


def timestamp() -> int:
    return int(time.time() * 1000)


def random_mac() -> str:
    return "".join(["%02X" % random.randint(0, 255) for _ in range(6)])
