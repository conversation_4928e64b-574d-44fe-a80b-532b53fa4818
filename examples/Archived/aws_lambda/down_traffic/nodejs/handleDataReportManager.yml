config:
    name: "handleDataReportManager"
    aws_env: test
    variables:
        us_hub_sn: "A2z8hejKE6uq0C2S" # hub2
        us_hub_client_id: "BLE-CDDB08455451"
        us_lock: "CECEC8925F1D" # "83b0dfbe-8774-4fc9-9bff-95228955bdbd" # <EMAIL>
        ap_hub_sn: "JfhowtEOEJzQZaXs" # hub2没有，这里用的hub mini
        ap_hub_client_id: "BLE-JfhowtEOEJzQZaXs"
        ap_lock: "D84876CBE55E" # "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb" # <EMAIL>
        # not used: sendSceneResult —> callbackStatusToLink
        # not used: changeOtherHubAndNotifyMsg —> callbackIotToLinkV1
        # not used: handleSubDeviceOffline -> changeHubBindList -> addDeviceToOther -> notifyBindStatus -> callbackIotToLink(app_to_data)/callbackIotToLinkV1(app_to_link)
        
        # handleReportIotRule -> handleVersion_1 

        # handleTestShadowData(CMD) -> handleMacBindList -//查询出用户的所有网关设备，并发送iot消息-> callbackIotToLinkV1(app_to_link)
        # handleTestShadowData(CMD) -> callbackIotToLink(app_to_link)

        # handleTestShadowData(BCD) -> (power === 'on' arch1.5)sendUpdateUserBindMsg -> ???updateDeviceBindList(arch2) -> removeBindList -> callbackIotToLinkV1(app_to_link)

        # handleLinkToData -> handleWoHubSyncBindDevices -> sendObserveDeviceCommand -> callbackIotToLink(app_to_data)
        # handleLinkToData -> handleWoHubSyncBindDevices -> callbackIotToLink(app_to_data)
        
        # handleLinkToStatus -> handleWoDeviceDataReport -> (power === 'on' && contentVersion !== HUB53_VERSION)sendUpdateUserBindMsg -> updateDeviceBindList(arch2) -> removeBindList -> callbackIotToLinkV1(app_to_link)


teststeps:
    -   name: 设备上报属性状态 link_to_report, us发往本区kds
        variables:
            ts: ${timestamp()}
            message: {
                "dT":116,
                "cT":1,
                "isC":1,
                "tS":"${ts}",
                "dM":"a0b76548653e",
                "cD":"a0b76548653e48161c0000",
                "bM":"ebb57df4b2e4",
                "v":1
                }
            # data["isC"] === 1 && data["dM"]
            # "ACTL AA " + data["dM"] + "570009"
        kinesis_request:
            region: us-east-1
            function: handleDataReportManager
            # handleReportIotRule -> handleVersion_1 -> dt 116: handleMeterData -> clearUploadFlag
            # handleReportIotRule -> handleVersion_1 -> dt 72: handleWoHandData -> clearUploadFlag
            # handleReportIotRule -> handleVersion_1 -> dt 0x63: handleCurtainData -> clearUploadFlag
            # handleReportIotRule -> handleVersion_1 -> dt default: clearUploadFlag
            # version: release
            data:
                {
                    "sn": "${ap_hub_sn}",
                    "eventType": "link_to_report",
                    "clientId": "${ap_hub_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}"
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_link"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    -   name: 设备上报属性状态 link_to_TEST_shadow CMD, us发往本区kds
        variables:
            ts: ${timestamp()}
        kinesis_request:
            region: us-east-1
            function: handleDataReportManager
            # handleTestShadowData -> handleMacBindList -//查询出用户的所有网关设备，并发送iot消息-> callbackIotToLinkV1(app_to_link)
            # handleTestShadowData -> callbackIotToLink(app_to_link)
            # version: release
            data:
                {
                    "sn": "${us_hub_sn}",
                    "eventType": "link_to_TEST_shadow",
                    "clientId": "${us_hub_client_id}",
                    "data": "CMD|01010300A|AAAYAA==18",
                    "timestamp": "${ts}"
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_link"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    -   name: 设备上报属性状态 link_to_data, us发往本区kds
        variables:
            ts: ${timestamp()}
            message:
                { "protocolVersion": 1,"count": 1,"messages": [
                    { "id": "40826533-d128-4750-8365-27bfe8d9b50f","eventType": "Wo.Hub.SyncBindDevices",
                        "data":
                        { "deviceID": "caead9055236","createTime": "${ts}","contentType": "base64",
                            "commandLevel": 5,"content": "CMD|2001a000A|AAAYAA==18"
                        }
                    }]
                }
        kinesis_request:
            region: us-east-1
            function: handleDataReportManager
            # handleLinkToData -> handleWoHubSyncBindDevices -> sendObserveDeviceCommand -> callbackIotToLink(app_to_data)
            # handleLinkToData -> handleWoHubSyncBindDevices -> callbackIotToLink(app_to_data)
            # version: release
            data: {
                "sn": "${us_hub_sn}",
                "eventType": "link_to_data",
                "clientId": "${us_hub_client_id}",
                "data": "${message}",
                "timestamp": "${ts}"
            }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_data"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    # -   name: 设备上报属性状态 link_to_status, us发往本区kds
    #     variables:
    #         ts: ${timestamp()}
    #         # contentVersion 1 的话直接转 handleLinkToReport handleVersion_1
    #         # 在线 || contentVersion != 3 (HUB53_VERSION)
    #         # deviceType deviceMac 01在线 signal 
    #         # 6f cf770c4a061f 01 01 e3870820
    #         message:
    #             {"protocolVersion":1,"count":1,"messages":[
    #                 {"id":"4604db47-181c-47f7-9738-bd6e664fa729","eventType":"Wo.Device.DataReport",
    #                 "data":{"deviceID":"cf770c4a061f","createTime":1692356475,"contentVersion":0,"contentType":"hex",
    #                 "content":"6fcf770c4a061f0101e3870820"}
    #                 }]
    #             }
    #     kinesis_request:
    #         region: us-east-1
    #         function: handleDataReportManager
    #         # handleLinkToStatus -> handleWoDeviceDataReport -> (power === 'on' && contentVersion !== HUB53_VERSION)sendUpdateUserBindMsg -> updateDeviceBindList(arch2 kds) -> removeBindList -> callbackIotToLinkV1(app_to_link)
    #         # version: release
    #         data: {
    #             "sn": "${us_hub_sn}",
    #             "eventType": "link_to_status",
    #             "clientId": "${us_hub_client_id}",
    #             "data": "${message}",
    #             "timestamp": "${ts}"
    #         }
    #     extract:
    #         lambda_log: 'log_result'
    #     validate:
    #         - contains: ["${lambda_log}", "app_to_link"]
    #         - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    -   name: 设备上报属性状态 link_to_report, ap发往本区kds
        variables:
            ts: ${timestamp()}
            message: {
                "dT":72,
                "cT":1,
                "isC":1,
                "tS":"${ts}",
                "dM":"a0b76548653e",
                "cD":"a0b76548653e48161c0000",
                "bM":"ebb57df4b2e4",
                "v":1
                }
        kinesis_request:
            region: ap-northeast-1
            function: handleDataReportManager
            # version: release
            data:
                {
                    "sn": "${ap_hub_sn}",
                    "eventType": "link_to_report",
                    "clientId": "${ap_hub_client_id}",
                    "data": "${message}",
                    "timestamp": "${ts}"
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_link"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    -   name: 设备上报属性状态 link_to_TEST_shadow CMD, ap发往本区kds
        variables:
            ts: ${timestamp()}
        kinesis_request:
            region: ap-northeast-1
            function: handleDataReportManager
            # version: release
            data:
                {
                    "sn": "${ap_hub_sn}",
                    "eventType": "link_to_TEST_shadow",
                    "clientId": "${ap_hub_client_id}",
                    "data": "CMD|01010300A|AAAYAA==18",
                    "timestamp": "${ts}"
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_link"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    -   name: 设备上报属性状态 link_to_data, ap发往本区kds
        variables:
            ts: ${timestamp()}
            message:
                { "protocolVersion": 1,"count": 1,"messages": [
                    { "id": "40826533-d128-4750-8365-27bfe8d9b50f","eventType": "Wo.Hub.SyncBindDevices",
                        "data":
                        { "deviceID": "caead9055236","createTime": "${ts}","contentType": "base64",
                            "commandLevel": 5,"content": "CMD|2001a000A|AAAYAA==18"
                        }
                    }]
                }
        kinesis_request:
            region: ap-northeast-1
            function: handleDataReportManager
            # version: release
            data: {
                "sn": "${ap_hub_sn}",
                "eventType": "link_to_data",
                "clientId": "${ap_hub_client_id}",
                "data": "${message}",
                "timestamp": "${ts}"
            }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_data"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
