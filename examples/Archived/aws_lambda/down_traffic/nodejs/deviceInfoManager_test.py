# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: ./nodejs/deviceInfoManager.yml
from switchbotrunner import (
    Config,
    RunApiGatewayRequest,
    RunKinesisRequest,
    RunLambdaRequest,
    RunRequest,
    RunSqsRequest,
    Step,
    SwitchBotRunner,
)


class TestCaseDeviceinfomanager(SwitchBotRunner):
    config = (
        Config("app_to_link deviceInfoManager")
        .variables(
            **{
                "us_user_id": "83b0dfbe-8774-4fc9-9bff-95228955bdbd",
                "us_remote_id": "01-202306061500-64055454_7d57f239-190e-4496-a43c-86a98fbb0eff",
                "ap_user_id": "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb",
                "ap_remote_id": "01-202310201013-16853068",
            }
        )
        .aws_env("test")
    )

    teststeps = [
        Step(
            RunLambdaRequest("Hub2远程下载(deviceInfoManager), us发往本区kds")
            .request(
                "us-east-1",
                "deviceInfoManager",
                {
                    "version": 1,
                    "operation": "startRemoteDownLoad",
                    "deviceType": "WoLinkPro",
                    "userID": "${us_user_id}",
                    "userName": "<EMAIL>",
                    "appVersion": "7.8",
                    "platform": "Android",
                    "body": {"deviceID": "EBB50DA36ED8", "remoteID": "${us_remote_id}"},
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
        Step(
            RunLambdaRequest("Hub2远程下载(deviceInfoManager), ap发往本区kds")
            .request(
                "ap-northeast-1",
                "deviceInfoManager",
                {
                    "version": 1,
                    "operation": "startRemoteDownLoad",
                    "deviceType": "WoLinkPro",
                    "userID": "${ap_user_id}",
                    "userName": "<EMAIL>",
                    "appVersion": "7.8",
                    "platform": "Android",
                    "body": {"deviceID": "EBB50DA36ED8", "remoteID": "${ap_remote_id}"},
                },
            )
            .extract()
            .with_jmespath("log_result", "lambda_log")
            .validate()
            .assert_contains("${lambda_log}", "kds_iot_app_to_topic")
        ),
    ]


if __name__ == "__main__":
    TestCaseDeviceinfomanager().test_start()
