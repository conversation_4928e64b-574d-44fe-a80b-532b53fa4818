config:
    name: "app_to_link handleHubBindMac"
    aws_env: test
    variables:
        us_user_id: "83b0dfbe-8774-4fc9-9bff-95228955bdbd" # <EMAIL>
        us_hub_sn: "A2z8hejKE6uq0C2S" # hub2
        us_hub_client_id: "BLE-CDDB08455451"
        us_bind_mac: "${random_mac()}"
        ap_user_id: "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb" # <EMAIL>
        ap_hub_sn: "JfhowtEOEJzQZaXs" # hub2没有，这里用的hub mini
        ap_hub_client_id: "BLE-JfhowtEOEJzQZaXs"
        ap_bind_mac: "${random_mac()}"
        # bind -> macBindToHubAndSendIot -> sendToHubBindMacListIot -> callbackIotToLink(app_to_link)/callbackIotToLinkNew(app_to_data)
        # remove/unbind -> macRemoveToHubAndSendIot/macUnBindToHubAndSendIot -> removeBindMacFromHub -> sendToHubBindMacListIot -> callbackIotToLink(app_to_link)/callbackIotToLinkNew(app_to_data)
        # addDevice -> updateAllBindDevices -> sendBindListToHub/sendSubDeviecListToHub -> sendIotCommand -> callbackIotToLinkV1(app_to_link)/callbackIotToLinkNew(app_to_data)
        # removeHub -> handleRemoveHub -> handMaintainedListOverToOtherHub -> sendUsersDeviceMacsListToHub -> sendMaintainedListToHub -> callbackIotToLinkV1(app_to_link)/callbackIotToLinkNew(app_to_data)
teststeps:
    -   name: bind 时更新维护列表, us发往本区kds
        variables:
            ts: ${timestamp()}
        sqs_request:
            region: us-east-1
            function: handleHubBindMac
            # version: release
            body: {
                    "userID":"${us_user_id}",
                    "operation":"bind",
                    "sn":"${us_hub_sn}",
                    "deviceMac":"${us_bind_mac}",
                    "deviceType":"WoPresence"
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_data"]
            - contains: ["${lambda_log}", "app_to_link"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    -   name: remove 时更新维护列表, us发往本区kds
        variables:
            ts: ${timestamp()}
        sqs_request:
            region: us-east-1
            function: handleHubBindMac
            # version: release
            body: {
                    "userID":"${us_user_id}",
                    "operation":"remove",
                    "sn":"${us_hub_sn}",
                    "deviceMac":"${us_bind_mac}",
                    "deviceType":"WoPresence"
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_data"]
            - contains: ["${lambda_log}", "app_to_link"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    -   name: bind 时更新维护列表, ap发往本区kds
        variables:
            ts: ${timestamp()}
        sqs_request:
            region: ap-northeast-1
            function: handleHubBindMac
            # version: release
            body: {
                    "userID":"${ap_user_id}",
                    "operation":"bind",
                    "sn":"${ap_hub_sn}",
                    "deviceMac":"${ap_bind_mac}",
                    "deviceType":"WoPresence"
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_data"]
            - contains: ["${lambda_log}", "app_to_link"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    -   name: remove 时更新维护列表, ap发往本区kds
        variables:
            ts: ${timestamp()}
        sqs_request:
            region: ap-northeast-1
            function: handleHubBindMac
            # version: release
            body: {
                    "userID":"${ap_user_id}",
                    "operation":"remove",
                    "sn":"${ap_hub_sn}",
                    "deviceMac":"${ap_bind_mac}",
                    "deviceType":"WoPresence"
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_data"]
            - contains: ["${lambda_log}", "app_to_link"]
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
