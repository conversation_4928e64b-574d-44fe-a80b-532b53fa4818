config:
    name: "app_to_secret handleDeviceSecret"
    aws_env: test
    variables:
        us_hub_sn: "A2z8hejKE6uq0C2S" # hub2
        us_hub_client_id: "BLE-CDDB08455451"
        us_lock: "CECEC8925F1D" # "83b0dfbe-8774-4fc9-9bff-95228955bdbd" # <EMAIL>
        ap_hub_sn: "JfhowtEOEJzQZaXs" # hub2没有，这里用的hub mini
        ap_hub_client_id: "BLE-JfhowtEOEJzQZaXs"
        ap_lock: "D84876CBE55E" # "6af5bf86-77d0-4f1f-b90c-f7f7da7c51cb" # <EMAIL>

teststeps:
    -   name: matter添加lock, us发往本区kds
        variables:
            ts: ${timestamp()}
            # base64.b64decode('Q01EIEZGMDIxMTAwYyAAAB0kznpjo10+Ayo=').hex().split('000001d')[1][2:14].upper()
            # data: 000001d 6f LockdeviceID 0350 # 长度52
            # 000001d 6f F6148D7F5856 0350 
            # DeviceSecretKeyTable(deviceID)
            # DeviceShadowTb(sn)
            # DeviceStatusTb(deviceID)
        kinesis_request:
            region: us-east-1
            function: handleDeviceSecret
            # version: release
            data_from_hex: true
            data:
                {
                    "sn": "${us_hub_sn}",
                    "eventType": "link_to_secret",
                    "clientId": "${us_hub_client_id}",
                    "data": "434d44204646303231313030632000001d6f${us_lock}0350",
                    "timestamp": "${ts}"
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
    -   name: matter添加lock, ap发往本区kds
        variables:
            ts: ${timestamp()}
        kinesis_request:
            region: ap-northeast-1
            function: handleDeviceSecret
            # version: release
            data_from_hex: true
            # sn应该为hub2的sn
            data:
                {
                    "sn": "${ap_hub_sn}",
                    "eventType": "link_to_secret",
                    "clientId": "${ap_hub_client_id}",
                    "data": "434d44204646303231313030632000001d6f${ap_lock}0350",
                    "timestamp": "${ts}"
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "kds_iot_app_to_topic"]
