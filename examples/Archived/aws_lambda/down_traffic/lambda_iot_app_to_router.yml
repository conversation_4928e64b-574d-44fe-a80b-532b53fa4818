config:
    name: "test_lambda_iot_app_to_router"
    aws_env: test
    variables:
        us_sn: "qayJotTvLsW4gODB" # gTWjrVjcTJZ4M1Zp,HMM-D4D975352C7A
        us_client_id: "BLE-E5CE8392644B"
        ap_sn: "UpVddDIUb4HejTpe"
        ap_client_id: "BLE-CAEAD9055236"

teststeps:
    -   name: 设备归属美区，美区后台下发的控制命令，发往本区iot
        variables:
            ts: ${timestamp()}
        kinesis_request:
            region: us-east-1
            function: test_lambda_iot_app_to_router
            version: release
            encoded_data: false # 这里不是从iot来的数据，data部份不需要base64
            data: {
                "topic":"switchlink/${us_sn}/app_to_link",
                "sn":"${us_sn}",
                "data":{"type":"Buffer","data":[67,77,68,32,51,53,48,50,55,49,48,50,51,32,0,0,30,111,246,20,141,127,88,86,17,153,87,75,135,42,128,248,16,247,153,105,45,122,12,146,129,149,0]}
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_link, resendRegion: us"]
    -   name: 设备归属亚区，美区后台下发的控制命令，发往跨区iot
        variables:
            ts: ${timestamp()}
        kinesis_request:
            region: us-east-1
            function: test_lambda_iot_app_to_router
            version: release
            encoded_data: false
            data: {
                "topic":"switchlink/${ap_sn}/app_to_link",
                "sn":"${ap_sn}",
                "data":{"type":"Buffer","data":[67,77,68,32,51,53,48,50,55,49,48,50,51,32,0,0,30,111,246,20,141,127,88,86,17,153,87,75,135,42,128,248,16,247,153,105,45,122,12,146,129,149,0]}
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_link, resendRegion: ap"]
    -   name: 设备归属亚区，亚区后台下发的控制命令，发往本区iot
        variables:
            ts: ${timestamp()}
        kinesis_request:
            region: ap-northeast-1
            function: test_lambda_iot_app_to_router
            version: release
            encoded_data: false # 这里不是从iot来的数据，data部份不需要base64
            data: {
                "topic":"switchlink/${ap_sn}/app_to_link",
                "sn":"${ap_sn}",
                "data":{"type":"Buffer","data":[67,77,68,32,51,53,48,50,55,49,48,50,51,32,0,0,30,111,246,20,141,127,88,86,17,153,87,75,135,42,128,248,16,247,153,105,45,122,12,146,129,149,0]}
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_link, resendRegion: ap"]
    -   name: 设备归属美区，亚区后台下发的控制命令，发往跨区iot
        variables:
            ts: ${timestamp()}
        kinesis_request:
            region: ap-northeast-1
            function: test_lambda_iot_app_to_router
            version: release
            encoded_data: false
            data: {
                "topic":"switchlink/${us_sn}/app_to_link",
                "sn":"${us_sn}",
                "data":{"type":"Buffer","data":[67,77,68,32,51,53,48,50,55,49,48,50,51,32,0,0,30,111,246,20,141,127,88,86,17,153,87,75,135,42,128,248,16,247,153,105,45,122,12,146,129,149,0]}
                }
        extract:
            lambda_log: 'log_result'
        validate:
            - contains: ["${lambda_log}", "app_to_link, resendRegion: us"]
