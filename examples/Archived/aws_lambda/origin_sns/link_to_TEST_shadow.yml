config:
    name: "数据转发上行流量测试 link_to_TEST_shadow"
    aws_env: test
    variables:
        us_sn: "fO6zUtYSczVzP8fz" # WoLinkMini 其实需要HUB2
        us_client_id: "HUB2-C9F8C8F7368C"
        ap_sn: "UpVddDIUb4HejTpe"
        ap_client_id: "BLE-CAEAD9055236"
teststeps:
    -   name: "设备属于亚区，在美区上报事件"
        variables:
            ts: ${timestamp()}
        kinesis_request:
            region: us-east-1
            function: handleDataReportManager
#            version: release
            data:
                {
                    "sn": "SJkObDAjxBvzlS6Q",
                    "eventType": "link_to_TEST_shadow",
                    "clientId": "BLE-DC5C43B3F35D",
                    "data": "BCD|010211024|644b45d3|54f333507dea4e0101dc03c89daf",
                    "timestamp": "${ts}"
                }
        extract:
            mqtt_data: "request.data"
#        validate_log:
#            - logs: [ "test_lambda_data_distribute_property", "${mqtt_data}", "${ts}", "us-east-1" ]
#            - logs: [ "handleIoTCommand", "${mqtt_data}", "${ts}", "ap-northeast-1" ]
