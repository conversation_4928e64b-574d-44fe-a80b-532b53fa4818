config:
  name: "绑定S10"
#  variables:
#    wonderlabs_api: https://2sdhdhwhsd.execute-api.us-east-1.amazonaws.com/developStage
#    account_api: https://y5384v7heb.execute-api.us-east-1.amazonaws.com/prod
#    robot_api: https://m0fcw2hdke.execute-api.us-east-1.amazonaws.com/prod
#    password: "********"
#    client_id: 7iq3mvuryqhw8bdihm5k9uqp13
#    device_id: F94E2221-4CED-4305-89A8-DE18EBA7DE97
#    s10_type: WoSweeperOrigin
#    user_core_api: https://fletznmiza.execute-api.us-east-1.amazonaws.com/prod
#    user_group_api: https://zdqln1l1ud.execute-api.us-east-1.amazonaws.com/prod
#    task_id: ${get_uuid()}
#    map_id: ${get_uuid()}
#    simulate_device_env: test
#    s10_role_alias_url: https://c21h83ofzvo45x.credentials.iot.us-east-1.amazonaws.com/role-aliases/SweeperOriginServiceRoleAlias/credentials
#    s10_bucket: sweeper-origin-map
#    s10_device_id: FFFFFFFFFFFC
#    s10_product: Floor Cleaning Robot S10
#    username: <EMAIL>
#    group_id: G01-*************-b221
#    device_api: https://6iyoi5aigc.execute-api.us-east-1.amazonaws.com/prod
#    scene_api:
#    simulate_device_api: http://autoswitchbot:8000
#    iap_api: https://pay.api.woankeji.cn/iap
#    aws_env: test
#    region: us-east-1
#    join_username: <EMAIL>
#    join_password: "********"
#    open_api: https://bsv10ucgr4.execute-api.us-east-1.amazonaws.com/beta
#
#    group2_id: G01-*************-3ff1
#    homepage_api: https://wonderlabs.us.api.woankeji.cn/homepage
#    fake_api: http://localhost:8088

teststeps:
  - name: 用户登录
    request:
      method: POST
      url: ${account_api}/account/api/v1/user/login
      headers:
        Content-Type: "application/json"
      json: {
        "deviceInfo": {
          "deviceName": "iPhone",
          "appVersion": "7.6",
          "model": "iPhone 11 Pro",
          "deviceId": "${device_id}"
        },
        "clientId": "${client_id}",
        "password": "${password}",
        "username": "${username}",
        "grantType": "password"
      }
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [ "body.statusCode", 100 ]
    extract:
      token: body.body.access_token
  - name: 获取家庭列表
    request:
      method: POST
      url: ${homepage_api}/v1/group/getall
      headers:
        Content-Type: "application/json"
        Authorization: ${token}
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [ "body.resultCode", 100 ]
    extract:
      group_id: "body.data.groups[0].groupID"
  - name: 增加一个S10
    request:
      method: POST
      url: ${fake_api}/fake/create_device
      headers:
        Content-Type: "application/json"
        Authorization: ${token}
      json:
#        deviceID: "A0A3B32CA786"
        device_type: "WoSweeperOrigin"
#        group_id: "${group_id}"
        group_id: "${group_id}"
    validate:
      - eq: [ "status_code", 200 ]
      # {"resultCode":200,"data":{"token":"a6719d74d2c847d5","url":"us","urlType":"short","encrypt":["5701CB55C23250","5701CB55CD1F57141C","5701CB55CD1F50149C6D8E990C298BB0FC","5701CB55CD1F50149DB9B4F50EDD12B842"],"communicationKey":{"keyId":"f4","keyType":"80","key":"29165fdbbd3a7ba1fd2c33d949a3731f","createTime":1701328217049},"originKey":"e28a37f36a489d30c5f2d27e24b1cd9c","urlEnv":"test"},"message":""}
      # userID: 460e2e5e-0b3a-47a5-98b3-31df4cbc1cb3
      # userName: <EMAIL>
      # platform: Android
