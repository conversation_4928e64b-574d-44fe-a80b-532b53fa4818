config:
    name: "创建有效的谷歌订阅"
    # env test us-east-1
    # variables:
        # account_api: https://account.api.woankeji.cn
        # client_id: "fi1ynu021pdjgpzj4jtlxwq718"
        # device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
        # username: "<EMAIL>"
        # password: "********"
        # region: "us-east-1"
        # aws_env: test
        # iap_api: https://pay.api.woankeji.cn/iap

teststeps:
    -   name: 用户登录
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
    -   name: 用户获取个人信息
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/userinfo
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            user_id: body.body.userID
    -   name: 用户创建订阅
        request:
            method: POST
            url: ${iap_api}/v1/cloudStorage/create
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "productID": "com.woan.2cams_monthly",
                "platform": "google_play"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 100 ]
        extract:
            subscription_id: body.data.id
    -   name: 用户订阅后绑定设备
        request:
            method: POST
            url: ${iap_api}/v1/cloudStorage/bindDevices
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "id": "${subscription_id}",
                "deviceIDs": ["123", "456"]
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 100 ]
    -   name: 用户获取订阅列表
        request:
            method: POST
            url: ${iap_api}/v1/cloudStorage/list
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "language": "en",
                "primaryDeviceID": "123"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 100 ]
            -   contains: ["body.data.subscriptions[*].id", "${subscription_id}"]
    -   name: 用户获取订阅列表
        request:
            method: POST
            url: ${iap_api}/v1/cloudStorage/list
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "language": "en",
                "primaryDeviceID": "456"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 100 ]
            -   contains: ["body.data.subscriptions[*].id", "${subscription_id}"]
