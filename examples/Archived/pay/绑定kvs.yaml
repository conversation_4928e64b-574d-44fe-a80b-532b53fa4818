config:
    name: "绑定kvs"
    # env test us-east-1
    variables:
        # account_api: https://account.api.woankeji.cn
        # client_id: "fi1ynu021pdjgpzj4jtlxwq718"
        # device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
        # username: "<EMAIL>"
        # password: "********"
        # region: "us-east-1"
        # aws_env: test
        # iap_api: https://pay.api.woankeji.cn/iap
        kvs_api: https://wonderlabs.us.api.woankeji.cn/kvs

teststeps:
    -   name: 用户登录
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
    -   name: 用户获取个人信息
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/userinfo
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            user_id: body.body.userID
    -   name: 申请绑定 token
        variables:
            request_id: "${get_uuid()}"
        request:
            method: POST
            url: ${kvs_api}/v1/postToken
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "token": "${request_id}",
                "groupID": ""
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 100 ]
    
