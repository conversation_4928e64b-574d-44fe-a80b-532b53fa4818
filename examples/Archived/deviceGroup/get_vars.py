import requests

session = requests.Session()

# 'test_us-east-1' : '3'
env_map = {}


def get_envs():
    resp = session.get(
        "http://k8s-autotest-8f54d1a076-488200880.ap-east-1.elb.amazonaws.com/api/env?pageSize=50"
    )
    resp.raise_for_status()
    data = resp.json()["data"]
    # print(data)
    for item in data:
        env_map[f'{item["key"]}_{item["region"]}'] = item["id"]


get_envs()


def get_vars(env_label: str, pytest: bool = False):
    env_id = env_map[env_label]
    resp = session.get(
        "http://k8s-autotest-8f54d1a076-488200880.ap-east-1.elb.amazonaws.com/api/var"
    )
    resp.raise_for_status()
    data = resp.json()["data"]
    # print(data)
    if pytest:
        for item in data:
            if item["values"].get(env_id):
                print("        \"{}\": \"{}\",".format(item["key"], item["values"].get(env_id)))
    else:
        print("    variables:")
        for item in data:
            print("        {}: {}".format(item["key"], item["values"].get(env_id, "")))


if __name__ == "__main__":
    # test us-east-1 3
    get_vars('prod_us-east-1', True)
