config:
    name: "获取家庭组、房间、设备排序"
    variables:
        wonderlabs_api: https://2sdhdhwhsd.execute-api.us-east-1.amazonaws.com/developStage
        account_api: https://y5384v7heb.execute-api.us-east-1.amazonaws.com/prod
        robot_api: https://m0fcw2hdke.execute-api.us-east-1.amazonaws.com/prod
        password: "********"
        client_id: 7iq3mvuryqhw8bdihm5k9uqp13
        device_id: F94E2221-4CED-4305-89A8-DE18EBA7DE97
        s10_type: WoSweeperOrigin
        user_core_api: https://fletznmiza.execute-api.us-east-1.amazonaws.com/prod
        user_group_api: https://zdqln1l1ud.execute-api.us-east-1.amazonaws.com/prod
        task_id: ${get_uuid()}
        map_id: ${get_uuid()}
        simulate_device_env: test
        s10_role_alias_url: https://c21h83ofzvo45x.credentials.iot.us-east-1.amazonaws.com/role-aliases/SweeperOriginServiceRoleAlias/credentials
        s10_bucket: sweeper-origin-map
        s10_device_id: FFFFFFFFFFFC
        s10_product: Floor Cleaning Robot S10
        username: <EMAIL>
        group_id: G01-*************-b221
        device_api: https://6iyoi5aigc.execute-api.us-east-1.amazonaws.com/prod
        scene_api:
        simulate_device_api: http://autoswitchbot:8000
        iap_api: https://pay.api.woankeji.cn/iap
        aws_env: test
        region: us-east-1
        join_username: <EMAIL>
        join_password: "********"
        open_api: https://bsv10ucgr4.execute-api.us-east-1.amazonaws.com/beta

teststeps:
    -   name: 用户登录
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
    -   name: 获取家庭组、房间、设备排序
        request:
            method: GET
            url: ${wonderlabs_api}/devicegroup/v1/group/groups
            headers:
                Content-Type: "application/json"
                Authorization: ${token}
        validate:
        -   eq: [ "status_code", 200 ]
        -   eq: [ "body.statusCode", 100 ]
