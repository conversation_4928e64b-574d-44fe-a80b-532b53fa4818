config:
    name: "设备移动家庭/房间"
#    variables:
#        wonderlabs_api: https://2sdhdhwhsd.execute-api.us-east-1.amazonaws.com/developStage
#        account_api: https://y5384v7heb.execute-api.us-east-1.amazonaws.com/prod
#        robot_api: https://m0fcw2hdke.execute-api.us-east-1.amazonaws.com/prod
#        password: "********"
#        client_id: 7iq3mvuryqhw8bdihm5k9uqp13
#        device_id: F94E2221-4CED-4305-89A8-DE18EBA7DE97
#        s10_type: WoSweeperOrigin
#        user_core_api: https://fletznmiza.execute-api.us-east-1.amazonaws.com/prod
#        user_group_api: https://zdqln1l1ud.execute-api.us-east-1.amazonaws.com/prod
#        task_id: ${get_uuid()}
#        map_id: ${get_uuid()}
#        simulate_device_env: test
#        s10_role_alias_url: https://c21h83ofzvo45x.credentials.iot.us-east-1.amazonaws.com/role-aliases/SweeperOriginServiceRoleAlias/credentials
#        s10_bucket: sweeper-origin-map
#        s10_device_id: FFFFFFFFFFFC
#        s10_product: Floor Cleaning Robot S10
#        username: <EMAIL>
#        group_id: G01-*************-b221
#        device_api: https://6iyoi5aigc.execute-api.us-east-1.amazonaws.com/prod
#        scene_api:
#        simulate_device_api: http://autoswitchbot:8000
#        iap_api: https://pay.api.woankeji.cn/iap
#        aws_env: test
#        region: us-east-1
#        join_username: <EMAIL>
#        join_password: "********"
#        open_api: https://bsv10ucgr4.execute-api.us-east-1.amazonaws.com/beta
#
#        group2_id: G01-*************-3ff1

teststeps:
    -   name: 用户登录
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
    -   name: 获取设备列表中的一个设备
        request:
            method: POST
            url: ${wonderlabs_api}/device/v3/getdevice
            headers:
                Content-Type: "application/json"
                Authorization: ${token}
            json:
                required_type: "All"
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            device_mac: body.body.Items[0].device_mac
    -   name: 查询当前家庭信息下的房间
        request:
            method: POST
            url: ${wonderlabs_api}/devicegroup/v2/group/groupInfo
            headers:
                Content-Type: "application/json"
                Authorization: ${token}
            json:
                groupID: "${group_id}"
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            room1_id: "body.body.groupInfo.rooms[0].roomID"
    - name: 查询家庭2信息下的房间
      request:
          method: POST
          url: ${wonderlabs_api}/devicegroup/v2/group/groupInfo
          headers:
              Content-Type: "application/json"
              Authorization: ${token}
          json:
              groupID: "${group2_id}"
      validate:
          - eq: [ "status_code", 200 ]
          - eq: [ "body.statusCode", 100 ]
      extract:
          room2_id: "body.body.groupInfo.rooms[0].roomID"
    -   name: 从家庭1房间1移动到家庭2房间1
        request:
            method: POST
            url: ${wonderlabs_api}/devicegroup/v1/device/changeroom
            headers:
                Content-Type: "application/json"
                Authorization: ${token}
            json:
                deviceID: "${device_mac}"
                moveIn:
                    groupID: "${group2_id}"
                    roomID: "${room2_id}"
                moveOut:
                    groupID: "${group_id}"
                    roomID: "${room1_id}"
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
    -   name: 查询设备在家庭2房间下
        request:
            method: POST
            url: ${wonderlabs_api}/devicegroup/v2/group/groupInfo
            headers:
                Content-Type: "application/json"
                Authorization: ${token}
            json:
                groupID: "${group2_id}"
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
            -   contains: [ "body.body.groupInfo.rooms[0].roomInfo.sortArray", "${device_mac}" ]
    -   name: 从家庭2房间1移动到家庭1默认房间
        request:
            method: POST
            url: ${wonderlabs_api}/devicegroup/v1/device/changeroom
            headers:
                Content-Type: "application/json"
                Authorization: ${token}
            json:
                deviceID: "${device_mac}"
                moveIn:
                    groupID: "${group_id}"
                moveOut:
                    groupID: "${group2_id}"
                    roomID: "${room2_id}"
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
    -   name: 查询设备在家庭1默认房间下
        request:
            method: POST
            url: ${wonderlabs_api}/devicegroup/v2/group/groupInfo
            headers:
                Content-Type: "application/json"
                Authorization: ${token}
            json:
                groupID: "${group_id}"
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
            -   contains: [ "body.body.groupInfo.defaultRoom.sortArray", "${device_mac}" ]
