config:
    name: "app获取s3权限-获取其他人地图"
    variables:
        wonderlabs_api: https://l9ren7efdj.execute-api.us-east-1.amazonaws.com/developStage
        account_api: https://bana6ys6sk.execute-api.us-east-1.amazonaws.com/prod
        device_api: https://6oi8p0504l.execute-api.us-east-1.amazonaws.com/prod
        robot_api: https://lonvwnxxn7.execute-api.us-east-1.amazonaws.com/prod
        simulate_device_api: http://192.168.2.8:8001
        username: "<EMAIL>"
        password: "aa123456"
        client_id: "fi1ynu021pdjgpzj4jtlxwq718"
        device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
        s10_device_id: 2DBA9D991A4C
        group_id: G01-*************-4a63 # 默认家庭
        s10_role_alias_url: https://c1uvqmjoxoa1ul.credentials.iot.us-east-1.amazonaws.com/role-aliases/SweeperOriginServiceRoleAlias/credentials
        s10_bucket: prod-us-sweeper-origin
        

teststeps:
    -   name: 用户登录
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
    -   name: 用户获取个人信息
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/userinfo
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            user_id: body.body.userID
    -   name: app获取s3权限
        request:
            method: POST
            url: ${robot_api}/v1/task/getPolicy
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {}
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 100 ]
        extract:
            access_key_id: body.data.AccessKeyId
            secret_access_key: body.data.SecretAccessKey
            session_token: body.data.SessionToken
    # -   name: 校验s3权限
    # # 后端给的ListBucket权限不对，应该给bucket_name
    #     request:
    #         method: POST
    #         url: ${simulate_device_api}/api/simulate/s3_policy
    #         headers:
    #             Content-Type: "application/json"
    #         json: {
    #             "access_key_id": "${access_key_id}",
    #             "secret_access_key": "${secret_access_key}",
    #             "session_token": "${session_token}",
    #             "action": "ListBucket",
    #             "destination": "prod-us-sweeper-origin"
    #         }
    #     validate:
    #         -   eq: [ "status_code", 200 ]
    #         -   eq: [ "body.success", True ]
    -   name: 校验app s3权限-GetObject没权限
        request:
            method: POST
            url: ${simulate_device_api}/api/simulate/s3_policy
            headers:
                Content-Type: "application/json"
            json: {
                "access_key_id": "${access_key_id}",
                "secret_access_key": "${secret_access_key}",
                "session_token": "${session_token}",
                "action": "GetObject",
                "destination": "${s10_bucket}/Thing_${user_id}_aaa/current_map/123/refined_map.json"
            }
        validate:
            -   eq: [ "status_code", 403 ]
            -   eq: [ "body.success", False ]
