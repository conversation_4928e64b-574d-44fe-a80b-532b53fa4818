config:
    name: "测试场景执行-立即集尘"

teststeps:
    -   name: 用户登录
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
    -   name: 用户获取个人信息
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/userinfo
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            user_id: body.body.userID
    -   name: 用户获取家庭信息
        request:
            method: POST
            url: ${user_core_api}/v1/group/getall
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 100 ]
        extract:
            group_id: body.data.groups[0].groupID
    -   name: 虚拟设备确保在线
        request:
            method: PUT
            url: ${simulate_device_api}/api/simulate/device/${s10_device_id}
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "env": "${simulate_device_env}",
                "region": "${region}",
                "status": "on",
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.success", True ]
    -   name: 配置虚拟设备执行自清洁1039回调
        request:
            method: POST
            url: ${simulate_device_api}/api/simulate/callback_config
            headers:
                Content-Type: "application/json"
            json: {
                "name": "selfCleaning",
                "mac": "${s10_device_id}",
                "function_id": 1039,
                "callback_template": {
                    "return": [ 0 ],
                }
            }
        validate:
            -   eq: [ "status_code", 201 ]
            -   eq: [ "body.success", True ]
    -   name: 测试场景执行-立即集尘
        variables:
            default_name: ${get_suffix(${s10_device_id},2)}
        request:
            method: POST
            url: ${scene_api}/scene/v1/scene/testRun
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "actions": [{
                    "data": {
                        "deviceID": "${s10_device_id}",
                        "deviceName": "${s10_product} ${default_name}",
                        "deviceType": "${s10_type}",
                        "roomName": "不在任何房间",
                    },
                    "id": "${get_uuid()}",
                    "optID": "s10_act_start_dust_collection"
                }],
                "groupID": "${group_id}",
                "sceneName": "",
                "sceneType": "scene"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 100 ]
    -   name: 查询是否收到该方法请求
        request:
            method: GET
            url: ${simulate_device_api}/api/simulate/funcs
            params: {"device_id": "${s10_device_id}"}
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.success", True ]
            -   eq: [ "body.data[0].func_id",  1039 ]
            -   eq: [ "body.data[0].params", {'0': 4} ]
