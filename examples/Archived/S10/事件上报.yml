config:
    name: "事件上报"
    variables:
        wonderlabs_api: https://l9ren7efdj.execute-api.us-east-1.amazonaws.com/developStage
        account_api: https://bana6ys6sk.execute-api.us-east-1.amazonaws.com/prod
        device_api: https://6oi8p0504l.execute-api.us-east-1.amazonaws.com/prod
        robot_api: https://lonvwnxxn7.execute-api.us-east-1.amazonaws.com/prod
        simulate_device_api: http://192.168.2.8:8001
        username: "<EMAIL>"
        password: "aa123456"
        client_id: "fi1ynu021pdjgpzj4jtlxwq718"
        device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
        s10_device_id: 2DBA9D991A4C
        group_id: G01-*************-4a63 # 默认家庭

teststeps:
    -   name: 用户登录
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
    -   name: 虚拟设备使用用户id作为clientID连接mqtt
        request:
            method: PUT
            url: ${simulate_device_api}/api/simulate/device/${s10_device_id}
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "env": "${simulate_device_env}",
                "region": "${region}",
                "status": "on",
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.success", True ]
    -   name: 在完成探索后，回充之前
        request:
            method: POST
            url: ${simulate_device_api}/api/simulate/command
            headers:
                Content-Type: "application/json"
            json: { "mac": "${s10_device_id}","props": [ { "event": 4 } ] }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.success", True ]
        teardown_hooks:
            - ${sleep(3)}
    -   name: DeviceIDs
        request:
            method: POST
            url: ${device_api}/v1/shadow/getByIDs
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "deviceID": "${s10_device_id}",
                "propertyIDs": [ 1044 ]
            }
        validate:
            -   eq: [ 'body.data."1044".value', 4 ]
    
