# config:
#     name: "获取S10扫地机常用属性"

# teststeps:
#     -   name: 用户登录
#         request:
#             method: POST
#             url: ${account_api}/account/api/v1/user/login
#             headers:
#                 Content-Type: "application/json"
#             json: {
#                 "deviceInfo": {
#                     "deviceName": "iPhone",
#                     "appVersion": "7.6",
#                     "model": "iPhone 11 Pro",
#                     "deviceId": "${device_id}"
#                 },
#                 "clientId": "${client_id}",
#                 "password": "${password}",
#                 "username": "${username}",
#                 "grantType": "password"
#             }
#         validate:
#             -   eq: [ "status_code", 200 ]
#             -   eq: [ "body.statusCode", 100 ]
#         extract:
#             token: body.body.access_token
#     -   name: 用户获取个人信息
#         request:
#             method: POST
#             url: ${account_api}/account/api/v1/user/userinfo
#             headers:
#                 Content-Type: "application/json"
#                 Authorization: "${token}"
#             json: {
#             }
#         validate:
#             -   eq: [ "status_code", 200 ]
#             -   eq: [ "body.statusCode", 100 ]
#         extract:
#             user_id: body.body.userID
#     -   name: 用户获取家庭信息
#         request:
#             method: POST
#             url: ${user_core_api}/v1/group/getall
#             headers:
#                 Content-Type: "application/json"
#                 Authorization: "${token}"
#             json: {
#             }
#         validate:
#             -   eq: [ "status_code", 200 ]
#             -   eq: [ "body.resultCode", 100 ]
#         extract:
#             group_id: body.data.groups[0].groupID
#     -   name: 成员获取扫地机默认属性
#         request:
#             method: POST
#             url: ${device_api}/v1/shadow/getByIDs
#             headers:
#                 Content-Type: "application/json"
#                 Authorization: "${token}"
#             json: {
#                 "deviceID": "${s10_device_id}",
#                 "propertyIDs": [1028,1029,1030,1031,1035 ]
#             }
#         validate:
#             -   eq: [ "status_code", 200 ]
#             -   eq: [ "body.resultCode", 100 ]
#             -   eq: [ 'body.data."1028".value', "${s10_bucket}" ]
#             -   eq: [ 'body.data."1029".value', "Thing_${user_id}_${s10_device_id}/current_map" ]
#             -   eq: [ 'body.data."1030".value', "${s10_role_alias_url}" ]
#             -   eq: [ 'body.data."1031".value', "${region}" ]
#             -   eq: [ 'body.data."1035".value', 480 ]
