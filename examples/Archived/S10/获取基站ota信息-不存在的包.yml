config:
    name: "获取基站ota信息-不存在的包"
    variables:
        wonderlabs_api: https://l9ren7efdj.execute-api.us-east-1.amazonaws.com/developStage
        account_api: https://bana6ys6sk.execute-api.us-east-1.amazonaws.com/prod
        device_api: https://6oi8p0504l.execute-api.us-east-1.amazonaws.com/prod
        robot_api: https://lonvwnxxn7.execute-api.us-east-1.amazonaws.com/prod
        simulate_device_api: http://192.168.2.8:8001
        username: "<EMAIL>"
        password: "aa123456"
        client_id: "fi1ynu021pdjgpzj4jtlxwq718"
        device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
        s10_device_id: 2DBA9D991A4C
        task_id: "${get_uuid()}"
        region: "us-east-1"
        # task_id: 54865a19-9387-4350-ab69-a91d4ba77bc5

teststeps:
    -   name: 用户登录
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
    -   name: 用户获取个人信息
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/userinfo
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            user_id: body.body.userID
    -   name: 获取固件升级地址
        request:
            method: POST
            url: ${robot_api}/v1/base/ota
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "type": "waterBase",
                "version": "0"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 190 ]
            -   eq: [ "body.message", "could not get pack" ]
    -   name: 获取固件升级地址
        request:
            method: POST
            url: ${robot_api}/v1/base/ota
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "type": "chargeBase",
                "version": "0"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 190 ]
            -   eq: [ "body.message", "could not get pack" ]
