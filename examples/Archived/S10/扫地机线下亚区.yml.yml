config:
    name: "S10绑定流程-首次绑定-绑完立即解绑"
    variables:
        wonderlabs_api: https://qqmih8maac.execute-api.ap-northeast-1.amazonaws.com/developStage
        user_core_api: https://ksxph7k8gb.execute-api.ap-northeast-1.amazonaws.com/prod
        account_api: https://y5384v7heb.execute-api.us-east-1.amazonaws.com/prod
        user_group_api: https://01us6q0vf6.execute-api.ap-northeast-1.amazonaws.com/prod
        device_api: https://cwbdd760de.execute-api.ap-northeast-1.amazonaws.com/prod
        robot_api: https://9v8dtc3old.execute-api.ap-northeast-1.amazonaws.com/prod
        simulate_device_api: http://192.168.2.8:8001
        username: "<EMAIL>"
        password: "********"
        client_id: "7iq3mvuryqhw8bdihm5k9uqp13"
        device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
        region: "ap-northeast-1"
        s10_type: "WoSweeperOrigin"
        s10_product: "Floor Cleaning Robot S10"
        simulate_device_env: test
        aws_env: test
        s10_device_id: FFFFFFFFFFFB
        group_id: G01-*************-2c0c # 默认家庭
        s10_role_alias_url: https://c21h83ofzvo45x.credentials.iot.ap-northeast-1.amazonaws.com/role-aliases/SweeperOriginServiceRoleAlias/credentials
        s10_bucket: test-ap-sweeper-origin
        task_id: "${get_uuid()}"
        map_id: "${get_uuid()}"

teststeps:
