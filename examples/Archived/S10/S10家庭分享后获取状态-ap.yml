# config:
#     name: "S10家庭分享后获取状态"
#     variables:
#         wonderlabs_api: https://yivk7fys5c.execute-api.ap-northeast-1.amazonaws.com/developStage
#         user_core_api: https://s5objsz9pd.execute-api.ap-northeast-1.amazonaws.com/prod
#         account_api: https://bana6ys6sk.execute-api.us-east-1.amazonaws.com/prod
#         user_group_api: https://z3zef3pal1.execute-api.ap-northeast-1.amazonaws.com/prod
#         device_api: https://u1ae2sry2d.execute-api.ap-northeast-1.amazonaws.com/prod
#         robot_api: https://uc59vnu8l1.execute-api.ap-northeast-1.amazonaws.com/prod
#         simulate_device_api: http://192.168.2.8:8001
#         username: "<EMAIL>"
#         password: "aa123456"
#         client_id: "fi1ynu021pdjgpzj4jtlxwq718"
#         device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
#         region: "ap-northeast-1"
#         s10_type: "WoSweeperOrigin"
#         s10_product: "Floor Cleaning Robot S10"
#         simulate_device_env: prod
#         aws_env: prod
#         s10_device_id: FFFFFFFFFFFD
#         group_id: G01-*************-9088 # 默认家庭
#         s10_role_alias_url: https://c1uvqmjoxoa1ul.credentials.iot.ap-northeast-1.amazonaws.com/role-aliases/SweeperOriginServiceRoleAlias/credentials
#         s10_bucket: prod-ap-sweeper-origin
#         task_id: "${get_uuid()}"
#         map_id: "${get_uuid()}"
#         join_username: "<EMAIL>"
#         join_password: "********"

# teststeps:
#     -   name: 用户登录
#         request:
#             method: POST
#             url: ${account_api}/account/api/v1/user/login
#             headers:
#                 Content-Type: "application/json"
#             json: {
#                 "deviceInfo": {
#                     "deviceName": "iPhone",
#                     "appVersion": "7.6",
#                     "model": "iPhone 11 Pro",
#                     "deviceId": "${device_id}"
#                 },
#                 "clientId": "${client_id}",
#                 "password": "${password}",
#                 "username": "${username}",
#                 "grantType": "password"
#             }
#         validate:
#             -   eq: [ "status_code", 200 ]
#             -   eq: [ "body.statusCode", 100 ]
#         extract:
#             token: body.body.access_token
#     -   name: 用户获取个人信息
#         request:
#             method: POST
#             url: ${account_api}/account/api/v1/user/userinfo
#             headers:
#                 Content-Type: "application/json"
#                 Authorization: "${token}"
#             json: {
#             }
#         validate:
#             -   eq: [ "status_code", 200 ]
#             -   eq: [ "body.statusCode", 100 ]
#         extract:
#             user_id: body.body.userID
#     -   name: 用户获取家庭信息
#         request:
#             method: POST
#             url: ${user_core_api}/v1/group/getall
#             headers:
#                 Content-Type: "application/json"
#                 Authorization: "${token}"
#             json: {
#             }
#         validate:
#             -   eq: [ "status_code", 200 ]
#             -   eq: [ "body.resultCode", 100 ]
#         extract:
#             group_id: body.data.groups[0].groupID
#     -   name: 虚拟设备确保在线
#         request:
#             method: PUT
#             url: ${simulate_device_api}/api/simulate/device/${s10_device_id}
#             headers:
#                 Content-Type: "application/json"
#             json: {
#                 "env": "${simulate_device_env}",
#                 "region": "${region}",
#                 "status": "on",
#             }
#         validate:
#             -   eq: [ "status_code", 200 ]
#             -   eq: [ "body.success", True ]
#     -   name: 用户查询设备列表已经存在
#         request:
#             method: GET
#             url: ${wonderlabs_api}/sortdevices/v1/devices
#             headers:
#                 Content-Type: "application/json"
#                 Authorization: "${token}"
#         validate:
#             -   eq: [ "status_code", 200 ]
#             -   eq: [ "body.statusCode", 100 ]
#             -   contains: [ "body.body.deviceList[*].device_mac", "${s10_device_id}" ]
#     -   name: 管理员生成邀请码
#         request:
#             method: POST
#             url: ${wonderlabs_api}/deviceShare/v1/share/create
#             headers:
#                 Content-Type: "application/json"
#                 Authorization: "${token}"
#             json: {
#                     "alias": "",
#                     "groupID": "${group_id}",
#                     "identity": "member"
#                 }
#         validate:
#             -   eq: [ "status_code", 200 ]
#             -   eq: [ "body.statusCode", 100 ]
#         extract:
#             invite_code: body.body.inviteCode
#     -   name: 成员登录
#         request:
#             method: POST
#             url: ${account_api}/account/api/v1/user/login
#             headers:
#                 Content-Type: "application/json"
#             json: {
#                 "deviceInfo": {
#                     "deviceName": "iPhone",
#                     "appVersion": "7.6",
#                     "model": "iPhone 11 Pro",
#                     "deviceId": "${device_id}"
#                 },
#                 "clientId": "${client_id}",
#                 "password": "${join_password}",
#                 "username": "${join_username}",
#                 "grantType": "password"
#             }
#         validate:
#             -   eq: [ "status_code", 200 ]
#             -   eq: [ "body.statusCode", 100 ]
#         extract:
#             join_token: body.body.access_token
#     -   name: 确保成员不在家庭(退出家庭)
#         request:
#             method: POST
#             url: ${wonderlabs_api}/deviceShare/v1/share/exit
#             headers:
#                 Content-Type: "application/json"
#                 Authorization: "${join_token}"
#             json: {
#                     "groupID": "${group_id}",
#                 }
#         teardown_hooks:
#             - ${sleep(5)}
#     -   name: 成员使用邀请码加入家庭
#         request:
#             method: POST
#             url: ${wonderlabs_api}/deviceShare/v1/share/join
#             headers:
#                 Content-Type: "application/json"
#                 Authorization: "${join_token}"
#             json: {
#                     "inviteCode": "${invite_code}"
#                 }
#         validate:
#             -   eq: [ "status_code", 200 ]
#             -   eq: [ "body.statusCode", 100 ]

#     # -   name: 成员查询设备列表已经存在
#     #     request:
#     #         method: GET
#     #         url: ${wonderlabs_api}/sortdevices/v1/devices
#     #         headers:
#     #             Content-Type: "application/json"
#     #             Authorization: "${join_token}"
#     #     validate:
#     #         -   eq: [ "status_code", 200 ]
#     #         -   eq: [ "body.statusCode", 100 ]
#     #         -   contains: [ "body.body.deviceList[*].device_mac", "${s10_device_id}" ]
#     -   name: 成员获取扫地机默认属性
#         setup_hooks:
#             - ${sleep(5)}
#         request:
#             method: POST
#             url: ${device_api}/v1/shadow/getByIDs
#             headers:
#                 Content-Type: "application/json"
#                 Authorization: "${join_token}"
#             json: {
#                 "deviceID": "${s10_device_id}",
#                 "propertyIDs": [1028,1029,1030,1031,1035 ]
#             }
#         validate:
#             -   eq: [ "status_code", 200 ]
#             -   eq: [ "body.resultCode", 100 ]
#             -   eq: [ 'body.data."1028".value', "${s10_bucket}" ]
#             -   eq: [ 'body.data."1029".value', "Thing_${user_id}_${s10_device_id}/current_map" ]
#             -   eq: [ 'body.data."1030".value', "${s10_role_alias_url}" ]
#             -   eq: [ 'body.data."1031".value', "${region}" ]
#             -   eq: [ 'body.data."1035".value', 480 ]
#     -   name: 成员退出
#         request:
#             method: POST
#             url: ${wonderlabs_api}/deviceShare/v1/share/exit
#             headers:
#                 Content-Type: "application/json"
#                 Authorization: "${join_token}"
#             json: {
#                     "groupID": "${group_id}",
#                 }
#         validate:
#             -   eq: [ "status_code", 200 ]
#             -   eq: [ "body.statusCode", 100 ]
