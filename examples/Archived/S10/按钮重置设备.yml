config:
    name: "按钮重置设备"
    variables:
        wonderlabs_api: https://l9ren7efdj.execute-api.us-east-1.amazonaws.com/developStage
        account_api: https://bana6ys6sk.execute-api.us-east-1.amazonaws.com/prod
        device_api: https://6oi8p0504l.execute-api.us-east-1.amazonaws.com/prod
        robot_api: https://lonvwnxxn7.execute-api.us-east-1.amazonaws.com/prod
        simulate_device_api: http://***********:8001
        username: "<EMAIL>"
        password: "aa123456"
        client_id: "fi1ynu021pdjgpzj4jtlxwq718"
        device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
        s10_type: "WoSweeperOrigin"

teststeps:
    -   name: 用户登录
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
    -   name: 用户获取个人信息
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/userinfo
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            user_id: body.body.userID
    -   name: 用户获取家庭信息
        request:
            method: POST
            url: ${user_core_api}/v1/group/getall
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 100 ]
        extract:
            group_id: body.data.groups[0].groupID
    -   name: 生成一个S10虚拟设备
        request:
            method: POST
            url: ${simulate_device_api}/api/simulate/device
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "alias_type": "${s10_type}",
                "env": "${simulate_device_env}",
                "region": "${region}",
                "status": "off",
            }
        validate:
            -   eq: [ "status_code", 201 ]
            -   eq: [ "body.success", True ]
        extract:
            s10_device_id: body.data.mac
    -   name: 获取设备绑定临时token
        request:
            method: POST
            url: ${device_api}/v1/adddevice/getTempToken
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "deviceID": "${s10_device_id}",
                "deviceType": "${s10_type}",
                "groupID": "${group_id}",
                "timezone": 480
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 100 ]
        extract:
            tmp_token: body.data.token
    -   name: 获取设备iot证书
        request:
            method: POST
            url: ${device_api}/v1/adddevice/getIoTCert
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "deviceID": "${s10_device_id}",
                "token": "${tmp_token}",
                "serialNumber": "SU1${s10_device_id}" # 以前用来转换targetVersion属性的，后来没有具体作用，只是做缓存key
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 100 ]
            -   eq: [ "body.data.mqtt.clientID", "Thing_${user_id}_${s10_device_id}" ]
            #  校验iot证书权限
            -   policy: ["body.data.cert.CertificatePem","body.data.mqtt.clientID","body.data.cert.CertificateKey","${region}"]
        extract:
            iot_cert: body.data.cert.CertificatePem
            iot_key: "body.data.cert.CertificateKey"
    -   name: 用户查询设备列表已经存在
        request:
            method: GET
            url: ${wonderlabs_api}/sortdevices/v1/devices
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
            -   contains: [ "body.body.deviceList[*].device_mac", "${s10_device_id}" ]
    -   name: 虚拟设备使用用户id作为clientID连接mqtt
        request:
            method: PUT
            url: ${simulate_device_api}/api/simulate/device/${s10_device_id}
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "env": "${simulate_device_env}",
                "region": "${region}",
                "status": "on",
                "belong_user": "${user_id}",
                "cert_pem": "${iot_cert}",
                "key_pem": "${iot_key}"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.success", True ]
    -   name: 设备初始化完成
        variables:
            init_time: ${get_timestamp()}
        request:
            method: POST
            url: ${simulate_device_api}/api/simulate/command
            headers:
                Content-Type: "application/json"
            json: { "mac": "${s10_device_id}","props": [ { "initialize": true, "baseType": 1 } ] }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.success", True ]
        # 校验 手机mqtt已经收到设备初始化后的信息
    -   name: 查询设备是否收到了属性设置
        request:
            method: GET
            url: ${simulate_device_api}/api/simulate/props
            params: {
                "device_id": "${s10_device_id}",
                "props": ["1028", "1029", "1030", "1031", "1035", "1085", "1087"]
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.success", True ]
    -   name: 校验设备默认属性
        variables:
            default_name: ${get_suffix(${s10_device_id},2)}
        setup_hooks: # 等待dynamoDBStream写入默认属性
            - ${sleep(1)}
        request:
            method: POST
            url: ${device_api}/v1/shadow/getByIDs
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "deviceID": "${s10_device_id}",
                "propertyIDs": [ 113 ]
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 100 ]
            -   eq: [ 'body.data."113".value', "${group_id}" ]
    -   name: 校验初始化后服务端提供的属性
        setup_hooks: # 等待虚拟设备上报收到的属性后,且属性变更服务收到后，才能查询到
            - ${sleep(2)}
        request:
            method: POST
            url: ${device_api}/v1/shadow/getByIDs
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "deviceID": "${s10_device_id}",
                "propertyIDs": [ 1003,1008,1019,1020,1038,1040,1050,1023,1028,1029,1030,1031,1035,1086 ]
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 100 ]
            -   eq: [ 'body.data."1003".value', true ]
            -   eq: [ 'body.data."1008".value', 2 ]
            -   eq: [ 'body.data."1019".value', 0 ]
            -   eq: [ 'body.data."1020".value', 0 ]
            -   eq: [ 'body.data."1038".value', null ]
            -   eq: [ 'body.data."1040".value', 1 ]
            -   eq: [ 'body.data."1050".value', false ]
            -   eq: [ 'body.data."1023".value', null ] # 首次绑定 当前地图一定是空
            -   eq: [ 'body.data."1028".value', "${s10_bucket}" ]
            -   eq: [ 'body.data."1029".value', "Thing_${user_id}_${s10_device_id}/current_map" ]
            -   eq: [ 'body.data."1030".value', "${s10_role_alias_url}" ]
            -   eq: [ 'body.data."1031".value', "${region}" ]
            -   eq: [ 'body.data."1035".value', 480 ]
            -   eq: [ 'body.data."1086".value', "HK" ]
    -   name: 设备重置
        request:
            method: POST
            url: ${simulate_device_api}/api/simulate/command
            headers:
                Content-Type: "application/json"
            json: { "mac": "${s10_device_id}","props": [ { "unbindEvent": true } ] }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.success", True ]
        teardown_hooks:
            - ${sleep(10)}
    -   name: 用户查询设备列表不存在
        request:
            method: GET
            url: ${wonderlabs_api}/sortdevices/v1/devices
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
            -   not_contains: [ "body.body.deviceList[*].device_mac", "${s10_device_id}" ]
    -   name: 已经被解绑,非本用户设备
        request:
            method: POST
            url: ${device_api}/v1/shadow/getByIDs
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "deviceID": "${s10_device_id}",
                "propertyIDs": [ 113,131 ]
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 3401 ]
