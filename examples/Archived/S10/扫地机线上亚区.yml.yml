config:
    name: "S10绑定流程-首次绑定-绑完立即解绑"
    variables:
        wonderlabs_api: https://yivk7fys5c.execute-api.ap-northeast-1.amazonaws.com/developStage
        user_core_api: https://s5objsz9pd.execute-api.ap-northeast-1.amazonaws.com/prod
        account_api: https://bana6ys6sk.execute-api.us-east-1.amazonaws.com/prod
        user_group_api: https://z3zef3pal1.execute-api.ap-northeast-1.amazonaws.com/prod
        device_api: https://u1ae2sry2d.execute-api.ap-northeast-1.amazonaws.com/prod
        robot_api: https://uc59vnu8l1.execute-api.ap-northeast-1.amazonaws.com/prod
        simulate_device_api: http://192.168.2.8:8001
        username: "<EMAIL>"
        password: "aa123456"
        client_id: "fi1ynu021pdjgpzj4jtlxwq718"
        device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
        region: "ap-northeast-1"
        s10_type: "WoSweeperOrigin"
        s10_product: "Floor Cleaning Robot S10"
        simulate_device_env: prod
        aws_env: prod
        s10_device_id: FFFFFFFFFFFD
        group_id: G01-*************-9088 # 默认家庭
        s10_role_alias_url: https://c1uvqmjoxoa1ul.credentials.iot.ap-northeast-1.amazonaws.com/role-aliases/SweeperOriginServiceRoleAlias/credentials
        s10_bucket: prod-ap-sweeper-origin
        task_id: "${get_uuid()}"
        map_id: "${get_uuid()}"
        join_username: "<EMAIL>"
        join_password: "12345678"

teststeps:
