config:
    name: "s10语音列表"
    variables:
        wonderlabs_api: https://l9ren7efdj.execute-api.us-east-1.amazonaws.com/developStage
        account_api: https://bana6ys6sk.execute-api.us-east-1.amazonaws.com/prod
        device_api: https://6oi8p0504l.execute-api.us-east-1.amazonaws.com/prod
        robot_api: https://lonvwnxxn7.execute-api.us-east-1.amazonaws.com/prod
        simulate_device_api: http://192.168.2.8:8001
        username: "<EMAIL>"
        password: "aa123456"
        client_id: "fi1ynu021pdjgpzj4jtlxwq718"
        device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
        s10_device_id: 2DBA9D991A4C
        group_id: G01-*************-4a63 # 默认家庭

teststeps:
    -   name: 用户登录
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
    -   name: 用户获取个人信息
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/userinfo
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            user_id: body.body.userID
    -   name: 虚拟设备确保在线
        request:
            method: PUT
            url: ${simulate_device_api}/api/simulate/device/${s10_device_id}
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "env": "${simulate_device_env}",
                "region": "${region}",
                "status": "on",
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.success", True ]
    -   name: 配置虚拟设备获取当前语音列表回调
        request:
            method: POST
            url: ${simulate_device_api}/api/simulate/callback_config
            headers:
                Content-Type: "application/json"
            json: {
                "name": "startSweep",
                "mac": "${s10_device_id}",
                "function_id": 1041,
                "callback_template": {
                    "return": [ 0, "{\"current_voice\":\"china\",\"voice_list\":{\"china\":{\"status\":0,\"version\":\"0.0.0.0\"}}}" ],
                }
            }
        validate:
            -   eq: [ "status_code", 201 ]
            -   eq: [ "body.success", True ]
    -   name: 语音列表
        request:
            method: POST
            url: ${robot_api}/v1/voicePack/listV2
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "deviceID": "${s10_device_id}",
                "notify":{"type":"mqtt","url":"v1_1/${user_id}/APP_TEST_${user_id}/funcResp"}
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 200 ]
    -   name: 下载其中的语音包
    # 1026 还需要处理1026 设备端
        request:
            method: POST
            url: ${robot_api}/v1/voicePack/download
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "deviceID": "${s10_device_id}",
                "voicePackID": "china_woman"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.resultCode", 100 ]
