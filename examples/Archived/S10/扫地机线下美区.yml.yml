config:
    name: "S10绑定流程-首次绑定-绑完立即解绑"
    variables:
        wonderlabs_api: https://2sdhdhwhsd.execute-api.us-east-1.amazonaws.com/developStage
        user_core_api: https://fletznmiza.execute-api.us-east-1.amazonaws.com/prod
        account_api: https://y5384v7heb.execute-api.us-east-1.amazonaws.com/prod
        user_group_api: https://zdqln1l1ud.execute-api.us-east-1.amazonaws.com/prod
        device_api: https://6iyoi5aigc.execute-api.us-east-1.amazonaws.com/prod # https://slgcq42dfg.execute-api.us-east-1.amazonaws.com/prod
        robot_api: https://m0fcw2hdke.execute-api.us-east-1.amazonaws.com/prod
        simulate_device_api: http://192.168.2.8:8001
        username: "<EMAIL>"
        password: "********"
        client_id: "7iq3mvuryqhw8bdihm5k9uqp13"
        device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
        region: "us-east-1"
        s10_type: "WoSweeperOrigin"
        s10_product: "Floor Cleaning Robot S10"
        simulate_device_env: test
        aws_env: test
        s10_device_id: FFFFFFFFFFFC
        group_id: G01-*************-b221 # 默认家庭
        s10_role_alias_url: https://c21h83ofzvo45x.credentials.iot.us-east-1.amazonaws.com/role-aliases/SweeperOriginServiceRoleAlias/credentials
        s10_bucket: sweeper-origin-map
        task_id: "${get_uuid()}"
        map_id: "${get_uuid()}"

teststeps:
