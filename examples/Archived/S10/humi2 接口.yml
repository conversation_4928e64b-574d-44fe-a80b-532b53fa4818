# config:
#     name: "humi2接口"
#     variables:
#         wonderlabs_api: https://l9ren7efdj.execute-api.us-east-1.amazonaws.com/developStage
#         user_core_api: https://fejlvu0ghd.execute-api.us-east-1.amazonaws.com/prod
#         account_api: https://bana6ys6sk.execute-api.us-east-1.amazonaws.com/prod
#         device_api: https://6oi8p0504l.execute-api.us-east-1.amazonaws.com/prod
#         robot_api: https://lonvwnxxn7.execute-api.us-east-1.amazonaws.com/prod
#         simulate_device_api: http://192.168.2.8:8001
#         username: "<EMAIL>"
#         password: "aa123456"
#         client_id: "fi1ynu021pdjgpzj4jtlxwq718"
#         device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
#         region: "us-east-1"
#         s10_type: "WoSweeperOrigin"
#         s10_product: "Floor Cleaning Robot S10"
#         simulate_device_env: prod
#         s10_device_id: 2DBA9D991A4C
#         aws_env: prod
#         s10_bucket: prod-us-sweeper-origin
#         s10_role_alias_url: https://c1uvqmjoxoa1ul.credentials.iot.us-east-1.amazonaws.com/role-aliases/SweeperOriginServiceRoleAlias/credentials

# teststeps:
#     -   name: 用户登录
#         request:
#             method: POST
#             url: ${account_api}/account/api/v1/user/login
#             headers:
#                 Content-Type: "application/json"
#             json: {
#                 "deviceInfo": {
#                     "deviceName": "iPhone",
#                     "appVersion": "7.6",
#                     "model": "iPhone 11 Pro",
#                     "deviceId": "${device_id}"
#                 },
#                 "clientId": "${client_id}",
#                 "password": "${password}",
#                 "username": "${username}",
#                 "grantType": "password"
#             }
#         validate:
#             -   eq: [ "status_code", 200 ]
#             -   eq: [ "body.statusCode", 100 ]
#         extract:
#             token: body.body.access_token
#     -   name: 用户获取个人信息
#         request:
#             method: POST
#             url: ${account_api}/account/api/v1/user/userinfo
#             headers:
#                 Content-Type: "application/json"
#                 Authorization: "${token}"
#             json: {
#             }
#         validate:
#             -   eq: [ "status_code", 200 ]
#             -   eq: [ "body.statusCode", 100 ]
#         extract:
#             user_id: body.body.userID
#     -   name: 获取设备绑定临时token
#         request:
#             method: POST
#             url: https://device.us.api.switchbot.net/device/v1/device/applyToken
#             headers:
#                 Content-Type: "application/json"
#                 Authorization: "${token}"
#             json: {
#                 "deviceID": "${s10_device_id}",
#                 "deviceType": "${s10_type}",
#                 "groupID": "${group_id}",
#                 "timezone": 480
#             }
#         validate:
#             -   eq: [ "status_code", 200 ]
#     -   name: 获取设备绑定临时token2
#         request:
#             method: POST
#             url: https://device.us.api.switchbot.net/deviceapi/v1/device/applyToken
#             headers:
#                 Content-Type: "application/json"
#                 Authorization: "${token}"
#             json: {
#                 "deviceID": "${s10_device_id}",
#                 "deviceType": "${s10_type}",
#                 "groupID": "${group_id}",
#                 "timezone": 480
#             }
#         validate:
#             -   eq: [ "status_code", 403 ]
