config:
    name: "新增/修改红外遥控器"
    variables:
        wonderlabs_api: https://wonderlabs.us.api.woankeji.cn/wonder
        account_api: https://account.api.woankeji.cn/prod
        username: "<EMAIL>"
        password: "********"
        client_id: "7iq3mvuryqhw8bdihm5k9uqp13"
        device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"

teststeps:
    -   name: 用户登录
        request:
            method: POST
            url: ${account_api}/account/api/v1/user/login
            headers:
                Content-Type: "application/json"
            json: {
                "deviceInfo": {
                    "deviceName": "iPhone",
                    "appVersion": "7.6",
                    "model": "iPhone 11 Pro",
                    "deviceId": "${device_id}"
                },
                "clientId": "${client_id}",
                "password": "${password}",
                "username": "${username}",
                "grantType": "password"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
        extract:
            token: body.body.access_token
    -   name: 新增 hubmini
        request:
            method: POST
            url: ${wonderlabs_api}/device/v3/postdevice
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: [{
                "ble_version": 53,
                "cloudServiceAble": false,
                "device_detail": {
                    "deviceRegion": "0",
                    "device_type": "WoLinkMini",
                    "ip": "",
                    "isEncrypted": false,
                    "model": "",
                    "parent_device": "",
                    "pubtopic": "switchlink/vmBmiT6AVQCMTPqZ/app_to_link",
                    "remote": "",
                    "subtopic": "switchlink/vmBmiT6AVQCMTPqZ/link_to_app",
                    "support_cmd": [],
                    "update_time": "",
                    "version": "",
                    "wifi_mac": "E5:9B:F9:F1:49:DF"
                },
                "device_mac": "E59BF9F149DF",
                "device_name": "Hub Mini DF",
                "groupID": "G01-1706003415093-b221",
                "hardware_version": 11,
                "icon": "",
                "isMaster": true,
                "platforms": [],
                "usageTag": "switch",
                "userID": "",
                "user_name": "",
                "wifi_version": 38
                }
            ]
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
    -   name: 新增遥控器时不带 groupid
        request:
            method: POST
            url: ${wonderlabs_api}/sortdevices/v1/remotes
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "codeType": "3",
                "sn": "vmBmiT6AVQCMTPqZ",
                "dbVersion": 28,
                "type": 9,
                "keyDetail": [
                    {
                        "code": "H4sIAAAAAAAAE4VSuxHCMAz1EhRBUWbIEhSU0iB09Kk4ZmAzCkbhwBFCHyeE050vT356fn7OOB6frzP0VpfDdD8BKWLocQ9Yv0gQ60pSypD9WMqvk7m/0jH+NiZR5LVp9ecY1G28wZY+R11Rw6Twp0zdJxCut+mRUvRUmhT5R4pDh7K6JobEl7jqmRMMfXfwfTVOrPbWbGdimufGHyr+sGe3aP/ErJdS1f3lKwwdCZNFr+xKKW+dxK1wkgIAAA==",
                        "keyName": "111",
                        "keyType": "text",
                        "codeType": "3"
                    },
                    {
                        "code": "H4sIAAAAAAAAE3VRyQ0CMQxMEyvFK+UQNIAogQcS73hL4MeDEvaF6IHG+FEJAh+5QLuyFMWe8diT7Pan9+cMDnyO23F+XiFRhWspV51UkuQJUG6KYcZ9p1H4fd5nmv/jK3w3ruit62PdvzhYnlcYuICnH0fL81pF56X6LvfH/LpAssGGYWMnGzkcWKTKRCEn4agc7iEG3xyfFJEqegtyi4orV4O63ehUYexcaAcgq7Ge/hLP6/7NCyfzqz42VF3m6bytb/sxOmzLrlktkrupzheXtV/80EuELiLvwn6yerTmYIz5AiUCSGSSAgAA",
                        "keyName": "222",
                        "keyType": "text",
                        "codeType": "3"
                    }
                ],
                "priority": "0",
                "remoteName": "这是新增的遥控器",
                "unLearnKeyEnable": true,
                "createTime": "202404251507",
                "vibrateEnable": true,
                "parentHubMac": "E5:9B:F9:F1:49:DF",
                "code": "no_data",
                "environmentDataEnable": true,
                "keyMap": "noKeyMap",
                "userName": "",
                "remoteID": "02-202404251507-43900286",
                # "groupID": "G01-1706003415093-b221",
                "acType": "0",
                "serial": 0,
                "brandEn": "no_brand"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
    -   name: 修改遥控器时带 groupid
        request:
            method: PUT
            url: ${wonderlabs_api}/sortdevices/v1/remotes/02-202404251507-43900286
            headers:
                Content-Type: "application/json"
                Authorization: "${token}"
            json: {
                "codeType": "3",
                "sn": "vmBmiT6AVQCMTPqZ",
                "dbVersion": 28,
                "type": 9,
                "keyDetail": [
                    {
                        "code": "H4sIAAAAAAAAE4VSuxHCMAz1EhRBUWbIEhSU0iB09Kk4ZmAzCkbhwBFCHyeE050vT356fn7OOB6frzP0VpfDdD8BKWLocQ9Yv0gQ60pSypD9WMqvk7m/0jH+NiZR5LVp9ecY1G28wZY+R11Rw6Twp0zdJxCut+mRUvRUmhT5R4pDh7K6JobEl7jqmRMMfXfwfTVOrPbWbGdimufGHyr+sGe3aP/ErJdS1f3lKwwdCZNFr+xKKW+dxK1wkgIAAA==",
                        "keyName": "111",
                        "keyType": "text",
                        "codeType": "3"
                    },
                    {
                        "code": "H4sIAAAAAAAAE3VRyQ0CMQxMEyvFK+UQNIAogQcS73hL4MeDEvaF6IHG+FEJAh+5QLuyFMWe8diT7Pan9+cMDnyO23F+XiFRhWspV51UkuQJUG6KYcZ9p1H4fd5nmv/jK3w3ruit62PdvzhYnlcYuICnH0fL81pF56X6LvfH/LpAssGGYWMnGzkcWKTKRCEn4agc7iEG3xyfFJEqegtyi4orV4O63ehUYexcaAcgq7Ge/hLP6/7NCyfzqz42VF3m6bytb/sxOmzLrlktkrupzheXtV/80EuELiLvwn6yerTmYIz5AiUCSGSSAgAA",
                        "keyName": "333",
                        "keyType": "text",
                        "codeType": "3"
                    }
                ],
                "priority": "0",
                "remoteName": "这是新增的遥控器",
                "unLearnKeyEnable": true,
                "createTime": "202404251507",
                "vibrateEnable": true,
                "parentHubMac": "E5:9B:F9:F1:49:DF",
                "code": "no_data",
                "environmentDataEnable": true,
                "keyMap": "noKeyMap",
                "userName": "",
                "remoteID": "02-202404251507-43900286",
                "groupID": "G01-1706003415093-b221",
                "acType": "0",
                "serial": 0,
                "brandEn": "no_brand"
            }
        validate:
            -   eq: [ "status_code", 200 ]
            -   eq: [ "body.statusCode", 100 ]
