# NOTE: Generated By SwitchBotRunner v1.0.0
# FROM: simple/extract_one.yml
from switchbotrunner import S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Config, Step, RunRequest
from switchbotrunner import RunApiGatewayRequest
from switchbotrunner import RunKinesisRequest
from switchbotrunner import Run<PERSON><PERSON>sRequest
from switchbotrunner import RunLambdaRequest


class TestCase(SwitchBotRunner):

    config = Config("新增/修改红外遥控器").variables(
        **{
            "wonderlabs_api": "https://wonderlabs.us.api.woankeji.cn/wonder",
            "account_api": "https://account.api.woankeji.cn",
            "username": "<EMAIL>",
            "password": "********",
            "client_id": "7iq3mvuryqhw8bdihm5k9uqp13",
            "device_id": "F94E2221-4CED-4305-89A8-DE18EBA7DE97",
            "bot_region": "us",
        }
    )

    teststeps = [
        Step(
            RunRequest("获取消息中心请求地址")
            .post("${account_api}/admin/admin/api/v1/botregion/endpoint")
            .with_headers(**{"Content-Type": "application/json"})
            .with_json({"botRegion": "${bot_region}"})
            .extract()
            .with_jmespath("body.data[?name == 'msgCenter'].hostWithStage", "msg_api")
            .validate()
            .assert_equal("status_code", 200)
            .assert_equal("body.resultCode", 100)
            .assert_equal(
                "body.data[?name == 'msgCenter'].hostWithStage111 | [0]",
                "https://2rvladw128.execute-api.us-east-1.amazonaws.com/prod",
            )
        ),
    ]


if __name__ == "__main__":
    TestCase().test_start()
