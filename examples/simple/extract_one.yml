config:
  name: "新增/修改红外遥控器1"
  variables:
    wonderlabs_api: https://wonderlabs.us.api.woankeji.cn/wonder
    account_api: https://account.api.woankeji.cn
    username: "<EMAIL>"
    password: "********"
    client_id: "7iq3mvuryqhw8bdihm5k9uqp13"
    device_id: "F94E2221-4CED-4305-89A8-DE18EBA7DE97"
    bot_region: us
teststeps:
  - name: 获取消息中心请求地址
    request:
      method: POST
      url: '${account_api}/admin/admin/api/v1/botregion/endpoint'
      headers: { Content-Type: application/json }
      json: { botRegion: '${bot_region}' }
    validate:
      - { eq: [ status_code, 200 ] }
      - { eq: [ body.resultCode, 100 ] }
      - { eq: [ 'body.data[?name == ''msgCenter''].hostWithStage | [0]', 'https://2rvladw128.execute-api.us-east-1.amazonaws.com/prod' ]}
    extract:
      msg_api: 'body.data[?name == ''msgCenter''].hostWithStage'
  - name: request with functions
    testcase: getAllDevice.yml
