#!/bin/sh

# Exit immediately if a command exits with a non-zero status
set -o errexit

# Exit if trying to use undefined variables
set -o nounset

# Check if no parameters provided
if [ "$#" -eq 0 ]; then
    # Run without specific queue (uses default queues)
    celery -A autoswitchbot worker -l info
else
    # Use provided parameter as queue name
    QUEUE_NAME=$1
    celery -A autoswitchbot worker -l info -Q $QUEUE_NAME
fi