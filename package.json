{"name": "ant-design-pro", "version": "6.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "dev": "npm run start:dev", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "max openapi", "prepare": "husky", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env PORT=8001 REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "start:docker": "cross-env PORT=8001 REACT_APP_ENV=docker MOCK=none UMI_ENV=dev max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^5.3.7", "@ant-design/plots": "^2.2.2", "@ant-design/pro-components": "^2.7.10", "@ant-design/use-emotion-css": "1.0.4", "@icon-park/react": "^1.4.2", "@monaco-editor/react": "^4.6.0", "@umijs/route-utils": "4.0.1", "antd": "^5.18.0", "antd-style": "^3.6.2", "classnames": "^2.3.2", "dayjs": "^1.11.10", "js-yaml": "3.14.1", "lodash": "^4.17.21", "moment": "^2.29.4", "omit.js": "^2.0.2", "rc-menu": "^9.14.0", "rc-util": "^5.41.0", "react": "^18.3.1", "react-dev-inspector": "^2.0.1", "react-dom": "^18.3.1", "react-helmet-async": "1.3.0", "react-use-websocket": "^4.8.1"}, "devDependencies": {"@types/classnames": "^2.3.1", "@types/history": "^5.0.0", "@types/js-yaml": "^3.12.10", "@types/lodash": "^4.17.4", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.0", "@types/react-helmet": "^6.1.6", "@umijs/fabric": "^4.0.1", "@umijs/lint": "^4.2.10", "@umijs/max": "^4.2.10", "cross-env": "^7.0.3", "eslint": "^8.35.0", "husky": "^9.0.11", "lint-staged": "^15.2.5", "stylelint": "^16.4.0", "stylelint-prettier": "^5.0.0", "swagger-ui-dist": "^5.17.14", "ts-node": "^10.9.1", "typescript": "^5.4.5", "umi-presets-pro": "^2.0.3"}, "engines": {"node": ">=18.0.0"}}