version: "3"
services:
  pg:
    image: postgres:16-alpine
    container_name: autoswitchbot-pg
    restart: unless-stopped
    volumes:
      - "postgres-data:/var/lib/postgresql/data/"
    environment:
      POSTGRES_DB: autotest
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    container_name: autoswitchbot-redis
    restart: unless-stopped
    volumes:
      - "redis-conf:/usr/local/etc/redis"
    ports:
      - "6379:6379"

  django:
    build:
      context: ./autoswitchbot
      dockerfile: Dockerfile
    container_name: autoswitchbot-django
    environment:
      - DJANGO_ENV=docker
      - TZ=Asia/Shanghai
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    command: ["python", "manage.py", "runserver", "0.0.0.0:8000"]
    volumes:
      - ./autoswitchbot:/data/autoswitchbot:cached
    ports:
      - "8000:8000"
    depends_on:
      - pg
      - redis

  celery-worker:
    build:
      context: ./autoswitchbot
      dockerfile: Dockerfile
    container_name: autoswitchbot-celery-worker
    command: ["sh", "docker-celeryworker.sh"]
    volumes:
      - ./autoswitchbot:/data/autoswitchbot:cached
    restart: unless-stopped
    environment:
      - DJANGO_ENV=docker
      - TZ=Asia/Shanghai
    depends_on:
      - django
      - redis

  celery-beat:
    build:
      context: ./autoswitchbot
      dockerfile: Dockerfile
    container_name: autoswitchbot-celery-beat
    command: ["sh", "docker-celerybeat.sh"]
    volumes:
      - ./autoswitchbot:/data/autoswitchbot:cached
    restart: unless-stopped
    environment:
      - DJANGO_ENV=docker
      - TZ=Asia/Shanghai
    depends_on:
      - django
      - redis

  frontend:
    build:
      context: ./autoswitchbot-fe
      dockerfile: Dockerfile
    image: autoswitchbot-fe-dist
    container_name: autoswitchbot-fe
    ports:
      - '8001:80'
    restart: unless-stopped
    volumes:
      - /tmp/logs:/var/log/nginx
    environment:
      - TZ=Asia/Shanghai
    depends_on:
      - django

networks:
  default:
    name: autoswitchbot_default

volumes:
  postgres-data:
  redis-conf: 