import copy
import time
from typing import Any

from loguru import logger

from switchbotrunner.exceptions import (
    FunctionNotFound,
    ValidationFailure,
    VariableNotFound,
)
from switchbotrunner.lambda_client import Boto3Session
from switchbotrunner.lambda_response import KinesisResponseObject
from switchbotrunner.models import (
    IStep,
    Name,
    ProfileEnum,
    StepR<PERSON>ult,
    TKinesisDataStream,
    TStep,
)
from switchbotrunner.parser import parse_variables_mapping
from switchbotrunner.runner import ALLURE, SwitchBotRunner
from switchbotrunner.step_request import (
    StepRequestExtraction,
    StepRequestValidation,
    call_hooks,
    pretty_format,
)


def run_step_kinesis_request(runner: SwitchBotRunner, step: TStep) -> StepResult:
    """run teststep: kinesis_request"""
    step_result = StepResult(
        name=step.name,
        step_type="kinesis",
        success=False,
    )
    start_time = time.time()

    # parse
    functions = runner.parser.functions_mapping
    try:
        step_variables = runner.merge_step_variables(step.variables)  # FunctionNotFound
        # parse variables
        step_variables = parse_variables_mapping(step_variables, functions)

        request_dict = step.kinesis_request.dict()
        parsed_request_dict = runner.parser.parse_data(
            request_dict, step_variables
        )  # VariableNotFound, FunctionNotFound
    except (VariableNotFound, FunctionNotFound) as e:
        step_result.fail_reason = f"{e.__class__.__name__}: {str(e)}"
        e.step_result = step_result
        raise
    parsed_request_dict["sequenceNumber"] = (
        f"{runner.case_id}-{str(int(time.time() * 1000))[-6:]}"
    )

    step_variables["request"] = parsed_request_dict

    # setup hooks
    if step.setup_hooks:
        call_hooks(runner, step.setup_hooks, step_variables, "setup kinesis_request")

    # prepare arguments
    config = runner.get_config()
    profile_name = config.variables.get("aws_env", ProfileEnum.TEST.value)
    region_name = parsed_request_dict.pop("region_name")
    function_name = parsed_request_dict.pop("function_name")
    version = parsed_request_dict.pop("version")
    data = parsed_request_dict["data"]

    # log request
    request_print = "====== request details ======\n"
    request_print += f"profile_name: {profile_name}\n"
    request_print += f"region: {region_name}\n"
    request_print += f"function: {function_name}\n"
    request_print += f"version: {version}\n"
    for k, v in parsed_request_dict.items():
        request_print += f"{k}: {pretty_format(v)}\n"

    logger.debug(request_print)
    if ALLURE is not None:
        ALLURE.attach(
            request_print,
            name="request details",
            attachment_type=ALLURE.attachment_type.TEXT,
        )
    request_kwargs = copy.deepcopy(parsed_request_dict)
    request_kwargs.pop("data")
    runner.boto3_session = Boto3Session(profile_name)
    resp = runner.boto3_session.kinesis_request(
        region_name, function_name, data, version=version, **request_kwargs
    )

    # log response
    response_print = "====== response details ======\n"
    response_print += f"status_code: {resp.status_code}\n"
    response_print += f"log_result: {resp.log_result}\n"
    response_print += f"body: {pretty_format(resp.payload)}\n"
    logger.debug(response_print)
    if ALLURE is not None:
        ALLURE.attach(
            response_print,
            name="response details",
            attachment_type=ALLURE.attachment_type.TEXT,
        )
    resp_obj = KinesisResponseObject(resp, runner.parser, profile_name)
    step_variables["response"] = resp_obj

    # teardown hooks
    if step.teardown_hooks:
        call_hooks(runner, step.teardown_hooks, step_variables, "teardown request")

    # extract
    extractors = step.extract
    extract_mapping = resp_obj.extract(extractors, step_variables)
    step_result.export_vars = extract_mapping

    variables_mapping = step_variables
    variables_mapping.update(extract_mapping)

    # validate
    validators = step.validators
    try:
        resp_obj.validate(validators, variables_mapping)
        step_result.success = True
    except ValidationFailure as e:
        e.step_result = step_result
        raise
    finally:
        session_data = runner.session.data
        session_data.success = step_result.success
        session_data.validators = resp_obj.validation_results

        # save step data
        step_result.data = session_data
        step_result.start_at = start_time
        step_result.elapsed = time.time() - start_time

    return step_result


class StepKinesisRequestValidation(StepRequestValidation):
    def __init__(self, step: TStep):
        self.__step = step
        super().__init__(step)

    def run(self, runner: SwitchBotRunner):
        return run_step_kinesis_request(runner, self.__step)

    def assert_logs(
        self,
        function_name: str,
        expected_value: Any,
        timestamp: str = "",
        region: str = "us-east-1",
    ) -> "StepKinesisRequestValidation":
        self.__step.validators.append(
            {"logs": [function_name, expected_value, timestamp, region]}
        )
        return self

    def assert_not_logs(
        self,
        function_name: str,
        expected_value: Any,
        timestamp: str = "",
        region: str = "us-east-1",
    ) -> "StepKinesisRequestValidation":
        self.__step.validators.append(
            {"not_logs": [function_name, expected_value, timestamp, region]}
        )
        return self


class StepKinesisRequestExtraction(StepRequestExtraction):
    def __init__(self, step: TStep):
        self.__step = step
        super().__init__(step)

    def validate_log(self) -> StepKinesisRequestValidation:
        return StepKinesisRequestValidation(self.__step)

    def run(self, runner: SwitchBotRunner):
        return run_step_kinesis_request(runner, self.__step)

    def validate(self) -> StepKinesisRequestValidation:
        return StepKinesisRequestValidation(self.__step)


class KinesisRequestWithOptionalArgs(IStep):
    def __init__(self, step: TStep):
        self.__step = step

    def with_version(self, version) -> "KinesisRequestWithOptionalArgs":
        self.__step.kinesis_request.version = version
        return self

    def with_encoded_data(self, encoded_data: bool) -> "KinesisRequestWithOptionalArgs":
        self.__step.kinesis_request.encoded_data = encoded_data
        return self

    def with_data_from_hex(
        self, data_from_hex: bool
    ) -> "KinesisRequestWithOptionalArgs":
        self.__step.kinesis_request.data_from_hex = data_from_hex
        return self

    # def set_timeout(self, timeout: float) -> "KinesisRequestWithOptionalArgs":
    #     self.__step.kinesis_request.timeout = timeout
    #     return self

    def teardown_hook(
        self, hook: str, assign_var_name: str = None
    ) -> "KinesisRequestWithOptionalArgs":
        if assign_var_name:
            self.__step.teardown_hooks.append({assign_var_name: hook})
        else:
            self.__step.teardown_hooks.append(hook)

        return self

    def extract(self) -> StepKinesisRequestExtraction:
        return StepKinesisRequestExtraction(self.__step)

    def validate_log(self) -> StepKinesisRequestValidation:
        return StepKinesisRequestValidation(self.__step)

    def struct(self) -> TStep:
        return self.__step

    def name(self) -> str:
        return self.__step.name

    def type(self) -> str:
        return f"kinesis-request-{self.__step.kinesis_request.function_name}"

    def run(self, runner: SwitchBotRunner):
        return run_step_kinesis_request(runner, self.__step)


class RunKinesisRequest:
    def __init__(self, name: str):
        self.__step = TStep(name=name)

    def with_variables(self, **variables) -> "RunKinesisRequest":
        self.__step.variables.update(variables)
        return self

    def with_retry(self, retry_times, retry_interval) -> "RunKinesisRequest":
        self.__step.retry_times = retry_times
        self.__step.retry_interval = retry_interval
        return self

    def setup_hook(self, hook: str, assign_var_name: str = None) -> "RunKinesisRequest":
        if assign_var_name:
            self.__step.setup_hooks.append({assign_var_name: hook})
        else:
            self.__step.setup_hooks.append(hook)

        return self

    def request(
        self, region: str, function: Name, data: dict
    ) -> KinesisRequestWithOptionalArgs:
        self.__step.kinesis_request = TKinesisDataStream(
            region=region, function=function, data=data
        )
        return KinesisRequestWithOptionalArgs(self.__step)
