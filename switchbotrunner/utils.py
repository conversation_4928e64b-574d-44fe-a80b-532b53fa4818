import asyncio
import base64
import builtins
import collections
import copy
import functools
import itertools
import json
import os
import os.path
import platform
import signal
import sys
import time
from collections.abc import Coroutine
from datetime import datetime
from multiprocessing import Queue
from typing import Any

from aiobotocore.config import AioConfig
from aiobotocore.session import AioSession
from botocore.config import Config
from botocore.exceptions import ClientError, HTTPClientError
from cryptography import x509
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric.ec import (
    EllipticCurvePrivateKey,
    EllipticCurvePublicKey,
)
from dateutil.tz import tzlocal
from loguru import logger

from switchbotrunner import __version__, exceptions
from switchbotrunner.models import VariablesMapping

""" run httpbin as test service
https://github.com/postmanlabs/httpbin

$ docker pull kennethreitz/httpbin
$ docker run -p 80:80 kennethreitz/httpbin
"""
HTTP_BIN_URL = "http://127.0.0.1:80"


def get_platform():
    return {
        "httprunner_version": __version__,
        "python_version": f"{platform.python_implementation()} {platform.python_version()}",
        "platform": platform.platform(),
    }


def set_os_environ(variables_mapping):
    """set variables mapping to os.environ"""
    for variable in variables_mapping:
        os.environ[variable] = variables_mapping[variable]
        logger.debug(f"Set OS environment variable: {variable}")


def unset_os_environ(variables_mapping):
    """unset variables mapping to os.environ"""
    for variable in variables_mapping:
        os.environ.pop(variable)
        logger.debug(f"Unset OS environment variable: {variable}")


def get_os_environ(variable_name):
    """get value of environment variable.

    Args:
        variable_name(str): variable name

    Returns:
        value of environment variable.

    Raises:
        exceptions.EnvNotFound: If environment variable not found.

    """
    try:
        return os.environ[variable_name]
    except KeyError:
        raise exceptions.EnvNotFound(variable_name)


def lower_dict_keys(origin_dict):
    """convert keys in dict to lower case

    Args:
        origin_dict (dict): mapping data structure

    Returns:
        dict: mapping with all keys lowered.

    Examples:
        >>> origin_dict = {
            "Name": "",
            "Request": "",
            "URL": "",
            "METHOD": "",
            "Headers": "",
            "Data": ""
        }
        >>> lower_dict_keys(origin_dict)
            {
                "name": "",
                "request": "",
                "url": "",
                "method": "",
                "headers": "",
                "data": ""
            }

    """
    if not origin_dict or not isinstance(origin_dict, dict):
        return origin_dict

    return {key.lower(): value for key, value in origin_dict.items()}


def print_info(info_mapping):
    """print info in mapping.

    Args:
        info_mapping (dict): input(variables) or output mapping.

    Examples:
        >>> info_mapping = {
                "var_a": "hello",
                "var_b": "world"
            }
        >>> info_mapping = {
                "status_code": 500
            }
        >>> print_info(info_mapping)
        ==================== Output ====================
        Key              :  Value
        ---------------- :  ----------------------------
        var_a            :  hello
        var_b            :  world
        ------------------------------------------------

    """
    if not info_mapping:
        return

    content_format = "{:<16} : {:<}\n"
    content = "\n==================== Output ====================\n"
    content += content_format.format("Variable", "Value")
    content += content_format.format("-" * 16, "-" * 29)

    for key, value in info_mapping.items():
        if isinstance(value, tuple | collections.deque):
            continue
        elif isinstance(value, dict | list):
            value = json.dumps(value)
        elif value is None:
            value = "None"

        content += content_format.format(key, value)

    content += "-" * 48 + "\n"
    logger.info(content)


def omit_long_data(body, omit_len=512):
    """omit too long str/bytes"""
    if not isinstance(body, str | bytes):
        return body

    body_len = len(body)
    if body_len <= omit_len:
        return body

    omitted_body = body[0:omit_len]

    appendix_str = f" ... OMITTED {body_len - omit_len} CHARACTORS ..."
    if isinstance(body, bytes):
        appendix_str = appendix_str.encode("utf-8")

    return omitted_body + appendix_str


def sort_dict_by_custom_order(raw_dict: dict, custom_order: list):
    def get_index_from_list(lst: list, item: Any):
        try:
            return lst.index(item)
        except ValueError:
            # item is not in lst
            return len(lst) + 1

    return dict(
        sorted(raw_dict.items(), key=lambda i: get_index_from_list(custom_order, i[0]))
    )


class ExtendJSONEncoder(json.JSONEncoder):
    """especially used to safely dump json data with python object,
    such as MultipartEncoder"""

    def default(self, obj):
        try:
            return super().default(obj)
        except (UnicodeDecodeError, TypeError):
            return repr(obj)


def merge_variables(
    variables: VariablesMapping, variables_to_be_overridden: VariablesMapping
) -> VariablesMapping:
    """merge two variables mapping, the first variables have higher priority"""
    step_new_variables = {}
    for key, value in variables.items():
        if f"${key}" == value or "${" + key + "}" == value:
            # e.g. {"base_url": "$base_url"}
            # or {"base_url": "${base_url}"}
            continue

        step_new_variables[key] = value

    merged_variables = copy.copy(variables_to_be_overridden)
    merged_variables.update(step_new_variables)
    return merged_variables


def is_support_multiprocessing() -> bool:
    try:
        Queue()
        return True
    except (ImportError, OSError):
        # system that does not support semaphores
        # (dependency of multiprocessing), like Android termux
        return False


def gen_cartesian_product(*args: list[dict]) -> list[dict]:
    """generate cartesian product for lists

    Args:
        args (list of list): lists to be generated with cartesian product

    Returns:
        list: cartesian product in list

    Examples:

        >>> arg1 = [{"a": 1}, {"a": 2}]
        >>> arg2 = [{"x": 111, "y": 112}, {"x": 121, "y": 122}]
        >>> args = [arg1, arg2]
        >>> gen_cartesian_product(*args)
        >>> # same as below
        >>> gen_cartesian_product(arg1, arg2)
            [
                {'a': 1, 'x': 111, 'y': 112},
                {'a': 1, 'x': 121, 'y': 122},
                {'a': 2, 'x': 111, 'y': 112},
                {'a': 2, 'x': 121, 'y': 122}
            ]

    """
    if not args:
        return []
    elif len(args) == 1:
        return args[0]

    product_list = []
    for product_item_tuple in itertools.product(*args):
        product_item_dict = {}
        for item in product_item_tuple:
            product_item_dict.update(item)

        product_list.append(product_item_dict)

    return product_list


LOGGER_FORMAT = (
    "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green>"
    + " | <level>{level}</level> | <level>{message}</level>"
)


def init_logger(level: str):
    level = level.upper()
    if level not in ["TRACE", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
        level = "INFO"  # default

    # set log level to INFO
    logger.remove()
    logger.add(sys.stdout, format=LOGGER_FORMAT, level=level)


class TimeoutError(Exception):
    pass


def timeout(seconds):
    def decorator(func):
        def handler(signum, frame):
            raise TimeoutError("Function execution exceeded the timeout period.")

        def wrapper(*args, **kwargs):
            # 设置超时信号处理器
            signal.signal(signal.SIGALRM, handler)
            signal.alarm(seconds)  # 设置超时时间

            try:
                result = func(*args, **kwargs)
                signal.alarm(0)  # 取消超时信号
                return result
            except HTTPClientError:
                # 中断信号后导致boto exception
                return False
            except TimeoutError:
                # 在函数超时时进行处理
                # 可以选择抛出异常、返回默认值等
                return False

        return wrapper

    return decorator


def async_timeout(seconds):
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 创建一个事件循环
            # loop = asyncio.get_running_loop()
            # 使用 asyncio.wait_for 方法设置超时时间
            try:
                result = await asyncio.wait_for(func(*args, **kwargs), timeout=seconds)
                return result
            except builtins.TimeoutError:
                # 没有日志，没有错误
                return "", None

        return wrapper

    return decorator


def encoded_data(content: dict | str, **kwargs):
    data_field = content.get("data")  # kds/sqs 原消息中的data字段
    if isinstance(content, str):
        return content
    else:
        if kwargs.get("encoded_data") and data_field:
            if isinstance(data_field, dict):
                data_field = json.dumps(data_field)
            content["data"] = base64.b64encode(data_field.encode("utf-8")).decode(
                "utf-8"
            )
        return json.dumps(content)


def data_from_hex(content: dict | str, **kwargs):
    data_field = content.get("data")  # kds/sqs 原消息中的data字段
    if isinstance(content, str):
        return content
    else:
        if kwargs.get("data_from_hex") and data_field:
            data_bytes = bytes.fromhex(data_field)
            content["data"] = base64.b64encode(data_bytes).decode("utf-8")
        return json.dumps(content)


async def async_assert_function(function, *args):
    # sync assert function to async
    try:
        # 断言成功默认应该返回None
        return function(*args)
    except AssertionError as e:
        return e


async def get_task_results(tasks: list[Coroutine]):
    return await asyncio.gather(*tasks)


MAX_POOL_CONNECTIONS = 10


@async_timeout(1 * 60)
async def async_filter_log_events(
    function_name: str, filter_pattern=None, start_time=None, region_name="us-east-1"
) -> tuple[str, Exception | None]:
    log_group_name = f"/aws/lambda/{function_name}"
    filter_pattern = (
        str(filter_pattern) if not isinstance(filter_pattern, str) else filter_pattern
    )
    start_time = start_time or int((time.time() - 5 * 60) * 1000)
    aio_config = AioConfig(max_pool_connections=MAX_POOL_CONNECTIONS)
    aio_session = AioSession()
    aio_session.set_default_client_config(aio_config)
    client_config = Config(region_name=region_name)
    async with aio_session.create_client("logs", config=client_config) as logs_client:
        log_events = []
        while not log_events:
            try:
                forward_token = None
                while True:
                    kwargs = {
                        "logGroupName": log_group_name,
                        "filterPattern": filter_pattern,
                        "startTime": int(start_time),
                        "endTime": int((time.time()) * 1000),
                    }
                    if forward_token:
                        kwargs["nextToken"] = forward_token

                    await asyncio.sleep(1)
                    # logger.info(kwargs)
                    response = await logs_client.filter_log_events(**kwargs)

                    log_page_events = response.get("events", [])
                    log_events.extend(log_page_events)

                    next_forward_token = response.get("nextToken")
                    # logger.info("{}\n{}\n{}", log_group_name, bool(next_forward_token), log_page_events)
                    if next_forward_token is None:
                        break
                    if forward_token == next_forward_token:
                        break
                    if log_events:
                        break
                    forward_token = next_forward_token
            except ClientError as err:
                new_message = f"{log_group_name}: {err}"
                return "", Exception(new_message)
        log_data = "\n".join([event["message"] for event in log_events])
        logger.debug("{} {}\n{}", region_name, log_group_name, log_data)
        return log_data, None


async def async_validate_iot_cert(
    cert_pem: str,
    expect_client_id: str,
    key_pem: str,
    region_name: str = "us-east-1",
    profile_name: str = "test",
) -> tuple[bool, Exception | None]:
    # 解析证书文本，获取sha2指纹，这是证书id
    cert_bytes = cert_pem.encode()
    key_bytes = key_pem.encode()
    # 加载证书
    cert = x509.load_pem_x509_certificate(cert_bytes, default_backend())
    public_key = cert.public_key()
    key = serialization.load_pem_private_key(key_bytes, None, default_backend())
    # 检查证书是否为ECDSA类型
    if isinstance(public_key, EllipticCurvePublicKey) and isinstance(
        key, EllipticCurvePrivateKey
    ):
        # 计算SHA256指纹
        fingerprint = cert.fingerprint(hashes.SHA256())
        # 将指纹转换为十六进制格式
        certificate_id = fingerprint.hex().lower()
        logger.info(f"certificate_id: {certificate_id}")
        # 获取公钥
        private_key_public_key = key.public_key()
        if private_key_public_key.public_numbers() != public_key.public_numbers():
            # 私钥与公钥不匹配
            return False, Exception("私钥与公钥不匹配")
        aio_config = AioConfig(max_pool_connections=MAX_POOL_CONNECTIONS)
        aio_session = AioSession(profile=profile_name)
        aio_session.set_default_client_config(aio_config)
        client_config = Config(region_name=region_name)
        async with aio_session.create_client("iot", config=client_config) as client:
            try:
                response = await client.describe_certificate(
                    certificateId=certificate_id
                )
            except Exception:
                return False, Exception("证书不存在")
            cert_info = response["certificateDescription"]
            # 判断激活状态
            if cert_info.get("status") != "ACTIVE":
                return False, Exception("证书未激活")
            # 判断当前在有效期，且有效期时长
            validity = cert_info.get("validity", {})
            # 获取当前时间
            now = datetime.now(tzlocal())
            # 检查当前时间是否在有效期范围内
            is_within_validity = validity["notBefore"] <= now <= validity["notAfter"]
            if not is_within_validity:
                return False, Exception("证书未在有效期内")
            certificateArn = cert_info.get("certificateArn")
            response = await client.list_attached_policies(target=certificateArn)
            policy_names = [policy["policyName"] for policy in response["policies"]]
            if not policy_names:
                return False, Exception("证书未绑定任何策略")
            policy_name = policy_names[0]
            response = await client.get_policy(policyName=policy_name)
            policy = response["policyDocument"]
            policy_json = json.loads(policy)
            # print(policy_json)
            # 'Version': '2012-10-17', 'Statement': [{'Effect': 'Allow', 'Action': ['iot:Connect'], 'Resource': ['arn:aws:iot:*:*:client/Thing_bfa28ca0-f50b-4662-9306-aac1cf442dbc_2DBA9D991A4C']}, {'Effect': 'Allow', 'Action': ['iot:Subscribe'], 'Resource': ['*']}, {'Effect': 'Allow', 'Action': ['iot:Receive'], 'Resource': ['*']}, {'Effect': 'Allow', 'Action': ['iot:Publish'], 'Resource': ['*']}, {'Effect': 'Allow', 'Action': ['iot:AssumeRoleWithCertificate'], 'Resource': ['arn:aws:iot:us-east-1:443283509441:rolealias/SweeperOriginServiceRoleAlias']}]}
            for state in policy_json.get("Statement", []):
                if (
                    state.get("Action") == ["iot:Connect"]
                    and state.get("Effect") == "Allow"
                    and state.get("Resource")
                    == [f"arn:aws:iot:*:*:client/{expect_client_id}"]
                ):
                    return True, None
            return False, Exception("指定client_id的连接策略")
    else:
        print("The certificate is not an ECDSA type.")

    return True, None
