from switchbotrunner import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from switchbotrunner.models import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TRequest, TStep
from switchbotrunner.step_api_gateway_request import ApiGatewayRequestWithOptionalArgs
from switchbotrunner.step_kinesis_request import KinesisRequestWithOptionalArgs
from switchbotrunner.step_lambda_request import LambdaRequestWithOptionalArgs
from switchbotrunner.step_request import (
    RequestWithOptionalArgs,
    StepRequestExtraction,
    StepRequestValidation,
)
from switchbotrunner.step_sqs_request import SqsRequestWithOptionalArgs
from switchbotrunner.step_testcase import StepRefCase


class Step:
    def __init__(
        self,
        step: StepRequestValidation
        | StepRequestExtraction
        | RequestWithOptionalArgs
        | ApiGatewayRequestWithOptionalArgs
        | LambdaRequestWithOptionalArgs
        | KinesisRequestWithOptionalArgs
        | SqsRequestWithOptionalArgs
        | LambdaRequestWithOptionalArgs
        | StepRefCase,
    ):
        self.__step = step

    @property
    def request(self) -> TRequest:
        return self.__step.struct().request

    @property
    def testcase(self) -> TestCase:
        return self.__step.struct().testcase

    @property
    def retry_times(self) -> int:
        return self.__step.struct().retry_times

    @property
    def retry_interval(self) -> int:
        return self.__step.struct().retry_interval

    def struct(self) -> TStep:
        return self.__step.struct()

    def name(self) -> str:
        return self.__step.name()

    def type(self) -> str:
        return self.__step.type()

    def run(self, runner: SwitchBotRunner) -> StepResult:
        return self.__step.run(runner)
