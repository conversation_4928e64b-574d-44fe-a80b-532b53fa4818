import unittest

import pytest  # noqa

from switchbotrunner.builtin import open_api_sign, plus


class TestFunctions(unittest.TestCase):
    def test_plus(self):
        assert plus(1, 2) == 3

    def test_open_api_sign(self):
        assert (
            open_api_sign(
                'd8851bbbc2c199512be1ee55f12a448930505fc6fd20195607f7c8bb649a373f1e619e9e92240b17f793b31b0a3de7b6',
                '67b4d79245a052cca971182d7b4b9f00',
                1744017412937,
                '1744017412937',
            )
            == 'KSSvP6V35GECgLa3DXmSF0gaj96qovSBsswXOAjLdrA='
        )
