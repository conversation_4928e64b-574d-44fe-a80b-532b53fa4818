"""
Built-in functions used in YAML/JSON testcases.
"""

import base64
import datetime
import hashlib
import hmac
import random
import string
import time
import uuid as _uuid

# from switchbotrunner.exceptions import ParamsError


def gen_random_string(str_len: int, /) -> str:
    """generate random string with specified length"""
    return ''.join(
        random.choice(string.ascii_letters + string.digits) for _ in range(str_len)
    )


def get_timestamp(str_len: int = 13, /) -> str:
    """get timestamp string, length can only between 0 and 19"""
    return str(time.time_ns())[:str_len]
    # raise ParamsError('timestamp length can only between 0 and 19.')


def get_current_date(fmt: str = '%Y-%m-%d', /) -> str:
    """get current date, default format is %Y-%m-%d"""
    return datetime.datetime.now().strftime(fmt)


def sleep(n_secs: int | float, /) -> None:
    """sleep n seconds"""
    time.sleep(n_secs)


def get_uuid() -> str:
    return str(_uuid.uuid4())


def seconds(*, offset: int = 0) -> int:
    return int(time.time()) + offset


def timestamp(*, offsetdays: int = 0) -> int:
    """毫秒时间戳 int，可以指定偏移天数"""
    return int((time.time() + offsetdays * 24 * 3600) * 1000)


def get_suffix(text: str, count: int, /) -> str:
    """获取字符串的后缀

    ${get_suffix("2DBA9D991A4C", 2)} => 4C
    """
    length = len(text)
    if count > length:
        count = length
    return text[-count:]


def plus(a: int | float, b: int | float, /) -> int | float:
    return a + b


def open_api_sign(token: str, secret: str, t: int, nonce: str, /) -> str:
    """OpenAPI v1.1签名算法"""

    # headers = {}
    # nonce = _uuid.uuid4()
    # t = time.time_ns() // 100000
    string_to_sign = f'{token}{t}{nonce}'

    string_to_sign = string_to_sign.encode('utf-8')
    secret_bytes = secret.encode('utf-8')

    sign = base64.b64encode(
        hmac.new(secret_bytes, msg=string_to_sign, digestmod=hashlib.sha256).digest()
    ).decode('utf-8')

    # headers['Authorization'] = token
    # headers['charset'] = 'utf8'
    # headers['t'] = str(t)
    # headers['sign'] = str(sign, 'utf-8')
    # headers['nonce'] = str(nonce)
    return sign


if __name__ == '__main__':
    print(get_suffix('test', 2))
