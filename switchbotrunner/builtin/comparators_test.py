import re
import unittest

import pytest

from switchbotrunner.builtin import length_match


class TestFunctions(unittest.TestCase):
    def test_length_match(self):
        assert length_match(["1"], ["1"]) is None

    def test_length_match_error_check_value(self):
        with pytest.raises(
            AssertionError,
            match=re.escape(
                "check_value should be a sequence type (list/tuple/dict/str/bytes)"
            ),
        ):
            length_match(None, ["1"])

    def test_length_match_error_except_value(self):
        with pytest.raises(
            AssertionError,
            match=re.escape(
                "expect_value should be a sequence type (list/tuple/dict/str/bytes)"
            ),
        ):
            length_match(["1"], None)
