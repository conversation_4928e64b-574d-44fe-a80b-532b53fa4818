from switchbotrunner.models import StepResult

""" failure type exceptions
    these exceptions will mark test as failure
"""


class MyBaseFailure(Exception):
    pass


class ParseTestsFailure(MyBaseFailure):
    pass


class ExtractFailure(MyBaseFailure):
    pass


class SetupHooksFailure(MyBaseFailure):
    pass


class TeardownHooksFailure(MyBaseFailure):
    pass


""" error type exceptions
    these exceptions will mark test as error
"""


class MyBaseError(Exception):
    pass


class FileFormatError(MyBaseError):
    pass


class TestCaseFormatError(FileFormatError):
    pass


class TestSuiteFormatError(FileFormatError):
    pass


class ParamsError(MyBaseError):
    pass


class NotFoundError(MyBaseError):
    pass


class FileNotFound(FileNotFoundError, NotFoundError):
    pass


class EnvNotFound(NotFoundError):
    pass


class CSVNotFound(NotFoundError):
    pass


class ApiNotFound(NotFoundError):
    pass


class TestcaseNotFound(NotFoundError):
    pass


class SummaryEmpty(MyBaseError):
    """test result summary data is empty"""


class SqlMethodNotSupport(MyBaseError):
    pass


# 以下为新增/重写的代码
class FunctionError(Exception):
    pass


class ValidationFailure(MyBaseFailure):
    # 重写ValidationFailure，增加step_result属性，在失败时候能够传递到summary中
    step_result: StepResult

    def __init__(self, *args: object) -> None:
        super().__init__(*args)


class VariableNotFound:
    step_result: StepResult


class FunctionNotFound:
    step_result: StepResult
