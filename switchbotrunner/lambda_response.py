import async<PERSON>
from typing import Any

import jmespath
from jmespath.exceptions import JMESPath<PERSON>rror
from loguru import logger

from switchbotrunner.exceptions import Params<PERSON>rror, ValidationFailure
from switchbotrunner.models import Validators, VariablesMapping
from switchbotrunner.parser import Parser, parse_string_value
from switchbotrunner.response import (
    ResponseObjectBase,
    assert_for_generic_condition,
    log_uniform_validator,
    uniform_validator,
)
from switchbotrunner.utils import (
    async_assert_function,
    async_filter_log_events,
    get_task_results,
)


class LambdaResponseObject(ResponseObjectBase):
    def __init__(self, resp_obj, parser: Parser, profile_name: str = "test"):
        super().__init__(resp_obj, parser)
        self.profile_name = profile_name

    FORMAT_KEYS = [""]

    def __getattr__(self, key):
        if key in self.FORMAT_KEYS:
            try:
                value = self.resp_obj.json()
            except ValueError:
                value = self.resp_obj.content
        else:
            try:
                value = getattr(self.resp_obj, key)
            except AttributeError:
                err_msg = f"ResponseObject does not have attribute: {key}"
                logger.error(err_msg)
                raise ParamsError(err_msg)

        self.__dict__[key] = value
        return value

    def _search_jmespath(self, expr: str) -> Any:
        resp_obj_meta = {
            "status_code": self.status_code,
            "function_error": self.function_error,
            "executed_version": self.executed_version,
            "payload": self.payload,
            "response_meta_data": self.response_meta_data,
            "request": self.request,
        }
        if not expr.startswith(tuple(resp_obj_meta.keys())):
            if hasattr(self.resp_obj, expr):
                return getattr(self.resp_obj, expr)
            else:
                return expr
        try:
            check_value = jmespath.search(expr, resp_obj_meta)
        except JMESPathError as ex:
            logger.error(
                f"failed to search with jmespath\n"
                f"expression: {expr}\n"
                f"data: {resp_obj_meta}\n"
                f"exception: {ex}"
            )
            raise

        return check_value


class ApiGatewayResponseObject(LambdaResponseObject):
    FORMAT_KEYS = [""]

    def __getattr__(self, key):
        return super().__getattr__(key)


class KinesisResponseObject(LambdaResponseObject):
    FORMAT_KEYS = ["data", "payload"]

    def __getattr__(self, key):
        return super().__getattr__(key)

    def validate(
        self,
        validators: Validators,
        variables_mapping: VariablesMapping = None,
    ):
        variables_mapping = variables_mapping or {}

        self.validation_results = {}
        if not validators:
            return

        validate_pass = True
        failures = []

        validate_msgs = []
        validator_dicts = []
        validator_tasks = []  # async tasks
        validator_methods = []

        for v in validators:
            if "validate_extractor" not in self.validation_results:
                self.validation_results["validate_extractor"] = []

            u_validator = log_uniform_validator(v)
            # comparator
            assert_method = u_validator["assert"]

            if assert_method not in ("logs", "not_logs"):
                u_validator = uniform_validator(v)

            # check item
            check_item = u_validator["check"]
            if "$" in check_item:
                # check_item is variable or function
                check_item = self.parser.parse_data(check_item, variables_mapping)
                check_item = parse_string_value(check_item)

            check_value = check_item

            # expect item
            expect_item = u_validator["expect"]
            # parse expected value with config/teststep/extracted variables
            expect_value = self.parser.parse_data(expect_item, variables_mapping)

            if assert_method not in ("logs", "not_logs"):
                assert_func = self.parser.get_mapping_function(assert_method)

                # expect item
                expect_item = u_validator["expect"]
                # parse expected value with config/teststep/extracted variables
                expect_value = self.parser.parse_data(expect_item, variables_mapping)

                # message
                message = u_validator["message"]
                # parse message with config/teststep/extracted variables
                message = self.parser.parse_data(message, variables_mapping)

                validate_msg = f"assert {check_item} {assert_method} {expect_value}({type(expect_value).__name__})"
                validate_msgs.append(validate_msg)

                validator_dict = {
                    "comparator": assert_method,
                    "check": check_item,
                    "check_value": check_value,
                    "expect": expect_item,
                    "expect_value": expect_value,
                    "message": message,
                }
                validator_dicts.append(validator_dict)

                # 非log相关的断言也放在异步任务里并行断言
                validator_tasks.append(
                    async_assert_function(
                        assert_func, check_value, expect_value, message
                    )
                )
                validator_methods.append(assert_method)
            else:
                # condition
                condition = u_validator["condition"]
                # parse condition with config/teststep/extracted variables
                condition = self.parser.parse_data(condition, variables_mapping)

                ## region
                region = u_validator["region"]
                # parse region with config/teststep/extracted variables
                region = self.parser.parse_data(region, variables_mapping)

                validate_msg = f"assert {region} {check_item} {assert_method} {expect_value}({type(expect_value).__name__})"
                validate_msgs.append(validate_msg)

                validator_dict = {
                    "comparator": assert_method,
                    "check": check_item,
                    "check_value": check_value,
                    "expect": expect_item,
                    "expect_value": expect_value,
                    "condition": condition,
                    "region": region,
                }
                validator_dicts.append(validator_dict)

                # async validate tasks
                validator_tasks.append(
                    async_filter_log_events(
                        function_name=check_value,
                        filter_pattern=expect_value,
                        start_time=condition,
                        region_name=region,
                    )
                )
                validator_methods.append(assert_method)

        results = asyncio.run(get_task_results(tasks=validator_tasks))
        for index, result in enumerate(results):
            validate_msg = validate_msgs[index]
            validator_dict = validator_dicts[index]
            assert_method = validator_methods[index]
            if assert_method == "logs":
                has_log, error = result
                condition = not error and bool(has_log)
                if not assert_for_log_condition(
                    condition, error, validator_dict, validate_msg, failures
                ):
                    validate_pass = False
            elif assert_method == "not_logs":
                # 没有错误，并且没有日志
                has_log, error = result
                condition = not error and not bool(has_log)
                if not assert_for_log_condition(
                    condition, error, validator_dict, validate_msg, failures
                ):
                    validate_pass = False
            else:
                # 非log相关的断言
                if not assert_for_generic_condition(
                    result, validator_dict, validate_msg, failures
                ):
                    validate_pass = False

            self.validation_results["validate_extractor"].append(validator_dict)

            if not validate_pass:
                failures_string = "\n".join(list(failures))
                raise ValidationFailure(failures_string)

    def _search_jmespath(self, expr: str) -> Any:
        resp_obj_meta = {
            "status_code": self.status_code,
            "function_error": self.function_error,
            "executed_version": self.executed_version,
            "payload": self.payload,
            "response_meta_data": self.response_meta_data,
            "request": self.request,
        }
        if not expr.startswith(tuple(resp_obj_meta.keys())):
            if hasattr(self.resp_obj, expr):
                return getattr(self.resp_obj, expr)
            else:
                return expr

        try:
            check_value = jmespath.search(expr, resp_obj_meta)
        except JMESPathError as ex:
            logger.error(
                f"failed to search with jmespath\n"
                f"expression: {expr}\n"
                f"data: {resp_obj_meta}\n"
                f"exception: {ex}"
            )
            raise

        return check_value


class SqsResponseObject(KinesisResponseObject):
    pass


def assert_for_log_condition(
    condition: bool,
    error: Exception,
    validator_dict,
    validate_msg,
    failures,
) -> bool:
    if condition:
        validate_msg += "\t==> pass"
        logger.info(validate_msg)
        validator_dict["check_result"] = "pass"
        return True
    else:
        validator_dict["check_result"] = "fail"
        validate_msg += "\t==> fail"
        validate_msg += (
            f"\n"
            f"check_item: {validator_dict['check']}\n"
            f"check_value: {validator_dict['check_value']}({type(validator_dict['check_value']).__name__})\n"
            f"assert_method: {validator_dict['comparator']}\n"
            f"expect_value: {validator_dict['expect_value']}({type(validator_dict['expect_value']).__name__})\n"
            f"region: {validator_dict['region']}"
        )
        message = "" if error is None else str(error)
        if message:
            validate_msg += f"\nmessage: {message}"
        logger.error(validate_msg)
        failures.append(validate_msg)
        return False
