import time

from loguru import logger

from switchbotrunner.exceptions import (
    FunctionNotFound,
    ValidationFailure,
    VariableNotFound,
)
from switchbotrunner.lambda_client import Boto3Session
from switchbotrunner.lambda_response import ApiGatewayResponseObject
from switchbotrunner.models import (
    IStep,
    Name,
    Path,
    ProfileEnum,
    StepR<PERSON>ult,
    TAPIGatewayAWSProxy,
    TStep,
)
from switchbotrunner.parser import parse_variables_mapping
from switchbotrunner.runner import ALLUR<PERSON>, SwitchBotRunner
from switchbotrunner.step_request import (
    StepRequestExtraction,
    StepRequestValidation,
    call_hooks,
    pretty_format,
)


def run_step_api_gateway_request(runner: Switch<PERSON>otRunner, step: TStep) -> StepResult:
    """run teststep: api_gateway_request"""
    step_result = StepResult(
        name=step.name,
        step_type="api_gateway",
        success=False,
    )
    start_time = time.time()

    # parse
    functions = runner.parser.functions_mapping
    try:
        step_variables = runner.merge_step_variables(step.variables)  # FunctionNotFound
        # parse variables
        step_variables = parse_variables_mapping(step_variables, functions)

        request_dict = step.api_gateway_request.dict()
        parsed_request_dict = runner.parser.parse_data(
            request_dict, step_variables
        )  # VariableNotFound, FunctionNotFound
    except (VariableNotFound, FunctionNotFound) as e:
        step_result.fail_reason = f"{e.__class__.__name__}: {str(e)}"
        e.step_result = step_result
        raise
    request_headers = parsed_request_dict.pop("headers", {})
    # omit pseudo header names for HTTP/1, e.g. :authority, :method, :path, :scheme
    request_headers = {
        key: request_headers[key] for key in request_headers if not key.startswith(":")
    }
    request_headers["x-amzn-RequestId"] = (
        f"{runner.case_id}-{str(int(time.time() * 1000))[-6:]}"
    )
    parsed_request_dict["headers"] = request_headers

    step_variables["request"] = parsed_request_dict

    # setup hooks
    if step.setup_hooks:
        call_hooks(
            runner, step.setup_hooks, step_variables, "setup api_gateway_request"
        )

    # prepare arguments
    config = runner.get_config()
    profile_name = config.variables.get("aws_env", ProfileEnum.TEST.value)
    region_name = parsed_request_dict.pop("region_name")
    function_name = parsed_request_dict.pop("function_name")
    version = parsed_request_dict.pop("version")
    method = parsed_request_dict.pop("method")
    path = parsed_request_dict.pop("path")
    event = parsed_request_dict.get("event")
    parsed_request_dict["json"] = parsed_request_dict.pop("req_json", {})

    # log request
    request_print = "====== request details ======\n"
    request_print += f"profile_name: {profile_name}\n"
    request_print += f"region: {region_name}\n"
    request_print += f"function: {function_name}\n"
    request_print += f"version: {version}\n"
    request_print += f"method: {method}\n"
    request_print += f"path: {path}\n"
    request_print += f"event: {event}\n"
    for k, v in parsed_request_dict.items():
        request_print += f"{k}: {pretty_format(v)}\n"

    logger.debug(request_print)
    if ALLURE is not None:
        ALLURE.attach(
            request_print,
            name="request details",
            attachment_type=ALLURE.attachment_type.TEXT,
        )
    runner.boto3_session = Boto3Session(profile_name)
    resp = runner.boto3_session.api_gateway_request(
        region_name, function_name, method, path, version=version, **parsed_request_dict
    )

    # log response
    response_print = "====== response details ======\n"
    response_print += f"status_code: {resp.status_code}\n"
    response_print += f"log_result: {resp.log_result}\n"
    response_print += f"body: {pretty_format(resp.payload)}\n"
    logger.debug(response_print)
    if ALLURE is not None:
        ALLURE.attach(
            response_print,
            name="response details",
            attachment_type=ALLURE.attachment_type.TEXT,
        )
    resp_obj = ApiGatewayResponseObject(resp, runner.parser)
    step_variables["response"] = resp_obj

    # teardown hooks
    if step.teardown_hooks:
        call_hooks(runner, step.teardown_hooks, step_variables, "teardown request")

    # extract
    extractors = step.extract
    extract_mapping = resp_obj.extract(extractors, step_variables)
    step_result.export_vars = extract_mapping

    variables_mapping = step_variables
    variables_mapping.update(extract_mapping)

    # validate
    validators = step.validators
    try:
        resp_obj.validate(validators, variables_mapping)
        step_result.success = True
    except ValidationFailure as e:
        e.step_result = step_result
        raise
    finally:
        session_data = runner.session.data
        session_data.success = step_result.success
        session_data.validators = resp_obj.validation_results

        # save step data
        step_result.data = session_data
        step_result.start_at = start_time
        step_result.elapsed = time.time() - start_time

    return step_result


class StepApiGatewayRequestValidation(StepRequestValidation):
    def __init__(self, step: TStep):
        self.__step = step
        super().__init__(step)

    def run(self, runner: SwitchBotRunner):
        return run_step_api_gateway_request(runner, self.__step)


class StepApiGatewayRequestExtraction(StepRequestExtraction):
    def __init__(self, step: TStep):
        self.__step = step
        super().__init__(step)

    def run(self, runner: SwitchBotRunner):
        return run_step_api_gateway_request(runner, self.__step)

    def validate(self) -> StepApiGatewayRequestValidation:
        return StepApiGatewayRequestValidation(self.__step)


class ApiGatewayRequestWithOptionalArgs(IStep):
    def __init__(self, step: TStep):
        self.__step = step

    def with_version(self, version) -> "ApiGatewayRequestWithOptionalArgs":
        self.__step.api_gateway_request.version = version
        return self

    def with_path_params(self, **path_params) -> "ApiGatewayRequestWithOptionalArgs":
        self.__step.api_gateway_request.path_params.update(path_params)
        return self

    def with_params(self, **params) -> "ApiGatewayRequestWithOptionalArgs":
        self.__step.api_gateway_request.params.update(params)
        return self

    def with_headers(self, **headers) -> "ApiGatewayRequestWithOptionalArgs":
        self.__step.api_gateway_request.headers.update(headers)
        return self

    def with_json(self, req_json) -> "ApiGatewayRequestWithOptionalArgs":
        self.__step.api_gateway_request.req_json = req_json
        return self

    def with_data(self, data) -> "ApiGatewayRequestWithOptionalArgs":
        self.__step.api_gateway_request.data = data
        return self

    def with_event(self, event) -> "ApiGatewayRequestWithOptionalArgs":
        self.__step.api_gateway_request.event = event
        return self

    # def set_timeout(self, timeout: float) -> "ApiGatewayRequestWithOptionalArgs":
    #     self.__step.api_gateway_request.timeout = timeout
    #     return self

    def teardown_hook(
        self, hook: str, assign_var_name: str = None
    ) -> "ApiGatewayRequestWithOptionalArgs":
        if assign_var_name:
            self.__step.teardown_hooks.append({assign_var_name: hook})
        else:
            self.__step.teardown_hooks.append(hook)

        return self

    def extract(self) -> StepApiGatewayRequestExtraction:
        return StepApiGatewayRequestExtraction(self.__step)

    def validate(self) -> StepApiGatewayRequestValidation:
        return StepApiGatewayRequestValidation(self.__step)

    def struct(self) -> TStep:
        return self.__step

    def name(self) -> str:
        return self.__step.name

    def type(self) -> str:
        return f"api-gateway-request-{self.__step.api_gateway_request.function_name}"

    def run(self, runner: SwitchBotRunner):
        return run_step_api_gateway_request(runner, self.__step)


class RunApiGatewayRequest:
    def __init__(self, name: str):
        self.__step = TStep(name=name)

    def with_variables(self, **variables) -> "RunApiGatewayRequest":
        self.__step.variables.update(variables)
        return self

    def with_retry(self, retry_times, retry_interval) -> "RunApiGatewayRequest":
        self.__step.retry_times = retry_times
        self.__step.retry_interval = retry_interval
        return self

    def setup_hook(
        self, hook: str, assign_var_name: str = None
    ) -> "RunApiGatewayRequest":
        if assign_var_name:
            self.__step.setup_hooks.append({assign_var_name: hook})
        else:
            self.__step.setup_hooks.append(hook)

        return self

    def request(
        self, region: str, function: Name, method: str, path: Path
    ) -> ApiGatewayRequestWithOptionalArgs:
        self.__step.api_gateway_request = TAPIGatewayAWSProxy(
            region=region, function=function, method=method, path=path
        )
        return ApiGatewayRequestWithOptionalArgs(self.__step)
