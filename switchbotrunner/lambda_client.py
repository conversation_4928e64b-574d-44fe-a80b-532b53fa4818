import base64
import json
import time
from json import JSONDecodeError

import boto3
from botocore.config import Config
from botocore.exceptions import (
    ClientError,
    InvalidRegionError,
    ParamValidationError,
    PartialCredentialsError,
)
from botocore.response import StreamingBody
from loguru import logger

from switchbotrunner.exceptions import FunctionError
from switchbotrunner.models import (
    Boto3SessionData,
    LambdaReqRespData,
    LambdaRequestData,
    LambdaResponseData,
    ProfileEnum,
)
from switchbotrunner.utils import data_from_hex, encoded_data

# urllib3.disable_warnings(InsecureRequestWarning)


class LambdaResponse(LambdaResponseData):
    # payload: StreamingBody

    def raise_for_error(self):
        if self.function_error:
            raise FunctionError(self.function_error)


def log_print(req_or_resp, r_type):
    msg = f"\n================== {r_type} details ==================\n"
    for key, value in req_or_resp.dict().items():
        if isinstance(value, dict) or isinstance(value, list):
            value = json.dumps(value, indent=4, ensure_ascii=False)

        msg += f"{key:<8} : {value}\n"
    logger.debug(msg)


def get_req_resp_record(resp_obj: LambdaResponse) -> LambdaReqRespData:
    """get request and response info from Response() object."""
    # record actual request info
    request_body = resp_obj.request.body
    if request_body is not None:
        try:
            request_body = json.loads(request_body)
        except json.JSONDecodeError:
            # str: a=1&b=2
            pass
        except UnicodeDecodeError:
            # bytes/bytearray: request body in protobuf
            pass
        except TypeError:
            # neither str nor bytes/bytearray, e.g. <MultipartEncoder>
            pass

    request_data = LambdaRequestData(
        method=resp_obj.request.method,
        url=resp_obj.request.url,
        body=request_body,
    )

    # log request details in debug mode
    # log_print(request_data, "request")

    # record response info
    # resp_headers = dict(resp_obj.headers)
    # lower_resp_headers = lower_dict_keys(resp_headers)
    # content_type = lower_resp_headers.get("content-type", "")

    response_body = resp_obj.content

    response_data = LambdaResponseData(
        status_code=resp_obj.status_code,
        # encoding=resp_obj.encoding,
        # headers=resp_headers,
        # content_type=content_type,
        body=response_body,
    )

    # log response details in debug mode
    # log_print(response_data, "response")

    req_resp_data = LambdaReqRespData(request=request_data, response=response_data)
    return req_resp_data


def decode_log_result(text: str) -> str:
    if not text:
        return text
    return "\n" + base64.b64decode(text).decode("utf-8").strip()


class Boto3Session(boto3.session.Session):
    def __init__(self, profile_name=ProfileEnum("test")):
        super().__init__(profile_name=profile_name)
        self.data = Boto3SessionData()

    def update_last_req_resp_record(self, resp_obj):
        """
        update request and response info from Response() object.
        """
        # TODO: fix
        self.data.req_resps.pop()
        self.data.req_resps.append(get_req_resp_record(resp_obj))

    def kinesis_request(
        self, region_name, function_name, data: dict, **kwargs
    ) -> LambdaResponse:
        sequence_number = kwargs.get("sequenceNumber")
        if kwargs.get("data_from_hex"):
            formated_data = data_from_hex(data, **kwargs)
        else:
            formated_data = encoded_data(data, **kwargs)

        payload = {
            "Records": [
                {
                    "kinesis": {
                        # "partitionKey": "partitionKey-03",
                        "partitionKey": "${newuuid()}",
                        "kinesisSchemaVersion": "1.0",
                        "data": base64.b64encode(formated_data.encode("utf-8")).decode(
                            "utf-8"
                        ),
                        "sequenceNumber": sequence_number,
                        "approximateArrivalTimestamp": int(time.time()),
                    },
                    "eventSource": "aws:kinesis",
                    # "eventID": "shardId-000000000000:49545115243490985018280067714973144582180062593244200961",
                    "invokeIdentityArn": "arn:aws:iam::SWITCH-BOT-RUNNER",
                    "eventVersion": "1.0",
                    "eventName": "aws:kinesis:record",
                    "eventSourceARN": "arn:aws:kinesis:SWITCH-BOT-RUNNER",
                    "awsRegion": region_name,
                }
            ]
        }
        return self.request(region_name, function_name, payload, data, **kwargs)

    def api_gateway_request(
        self, region_name, function_name, method, path, **kwargs
    ) -> LambdaResponse:
        """
        :param region_name:
        :param function_name:
        :param method:
        :param path:
        :param path_params: (optional)
        :param params: (optional)
        :param headers: (optional)
        :param json: (optional)
        :param data: (optional)
        :param event: (optional)
        :return:
        """
        path_params = kwargs.get("path_params", {})
        params = kwargs.get("params", {})
        headers = kwargs.get("headers", {})
        body = json.dumps(kwargs.get("json", {}))
        data = kwargs.get("data")

        payload = {
            "body": body,
            # "resource": "/{proxy+}",  # must-have for go rpc server
            # "resource": path,  # must-have for go rpc server
            "path": path,
            "httpMethod": method,
            "queryStringParameters": params,
            "pathParameters": path_params,
            # "stageVariables": {
            #     "baz": "qux"
            # },
            "headers": {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Encoding": "gzip, deflate, sdch",
                "Accept-Language": "en-US,en;q=0.8",
                "Cache-Control": "max-age=0",
                "CloudFront-Forwarded-Proto": "https",
                "CloudFront-Is-Desktop-Viewer": "true",
                "CloudFront-Is-Mobile-Viewer": "false",
                "CloudFront-Is-SmartTV-Viewer": "false",
                "CloudFront-Is-Tablet-Viewer": "false",
                "CloudFront-Viewer-Country": "US",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": "Custom User Agent String",
                "Via": "1.1 08f323deadbeefa7af34d5feb414ce27.cloudfront.net (CloudFront)",
                "X-Amz-Cf-Id": "cDehVQoZnx43VYQb9j2-nvCh-9z396Uhbp027Y2JvkCPNLmGJHqlaA==",
                "X-Forwarded-For": "127.0.0.1, *********",
                "X-Forwarded-Port": "443",
                "X-Forwarded-Proto": "https",
            },
            # "requestContext": {
            #     "accountId": "************",
            #     "resourceId": "123456",
            #     "stage": "prod",
            #     "requestId": "c6af9ac6-7b61-11e6-9a41-93e8deadbeef",
            #     "path": "/prod/path/to/resource",
            #     "resourcePath": "/{proxy+}",
            #     "httpMethod": "POST",
            #     "protocol": "HTTP/1.1"
            # }
        }
        if headers:
            payload["headers"].update(headers)
            if headers.get("x-amzn-RequestId"):
                request_context = payload.setdefault("requestContext", {})
                request_context["requestId"] = headers.get("x-amzn-RequestId")
        if data:
            payload["data"] = data
        else:
            # api gateway request, origin data is body
            data = body
            if isinstance(data, str):
                try:
                    data = json.loads(data)
                except JSONDecodeError:
                    pass
        if "data" in kwargs.keys():
            kwargs.pop("data")
        # addon event data for lambda event, only for not needed data
        event_dict = kwargs.get("event", {})
        for k, v in event_dict.items():
            # if k not in payload:
            payload[k] = v
        return self.request(region_name, function_name, payload, data, **kwargs)

    def lambda_request(
        self, region_name, function_name, event, **kwargs
    ) -> LambdaResponse:
        """
        :param region_name:
        :param function_name:
        :param event:
        :return:
        """
        payload = event
        return self.request(region_name, function_name, payload, payload, **kwargs)

    def request(
        self, region_name, function_name, payload, data, **kwargs
    ) -> LambdaResponse:
        """
        :param region_name:
        :param function_name:
        :param payload:
        :param data: origin data for the request
        :param version: (Optional)
        :return:
        """
        self.data = Boto3SessionData()

        # timeout default to 120 seconds
        # kwargs.setdefault("timeout", 120)

        # set stream to True, in order to get client/server IP/Port
        # kwargs["stream"] = True

        start_timestamp = time.time()

        version = kwargs.pop("version", "$LATEST")
        request = LambdaRequestData(
            region=region_name, function=function_name, version=version, payload=payload
        )
        log_print(request, "request")

        response_dict = self._send_request_safe_mode(
            region_name, function_name, version, json.dumps(payload)
        )
        response_time_ms = round((time.time() - start_timestamp) * 1000, 2)

        resp_payload: StreamingBody = response_dict.get("Payload")
        resp_payload_data = resp_payload.read().decode("utf-8") if resp_payload else ""

        response_meta_data = response_dict.get("ResponseMetadata", {})
        headers = response_meta_data.get("HTTPHeaders", {})
        if "x-amz-log-result" in headers:
            headers.pop("x-amz-log-result")

        response = LambdaResponse(
            status_code=response_dict.get("StatusCode"),
            payload=resp_payload_data,
            log_result=decode_log_result(response_dict.get("LogResult", "")),
            function_error=response_dict.get("FunctionError", ""),
            executed_version=response_dict.get("ExecutedVersion"),
            response_meta_data=response_meta_data,
            request=data,  # add origin request data for extract
        )

        # get length of the response content
        content_size = int(resp_payload._content_length or 0)

        # record the consumed time
        self.data.stat.response_time_ms = response_time_ms
        # self.data.stat.elapsed_ms = response.elapsed.microseconds / 1000.0
        self.data.stat.content_size = content_size

        log_print(response, "response")

        # record request and response
        self.data.req_resps = [LambdaReqRespData(request=request, response=response)]

        # try:
        #     response.raise_for_status()
        # except RequestException as ex:
        #     logger.error(f"{str(ex)}")
        # else:
        logger.info(
            f"status_code: {response.status_code}, "
            f"response_time(ms): {response_time_ms} ms, "
            f"response_length: {content_size} bytes"
        )

        return response

    def sqs_request(
        self, region_name, function_name, body: dict | str, **kwargs
    ) -> LambdaResponse:
        messageId = kwargs.get("messageId")
        if kwargs.get("data_from_hex"):
            formated_body = data_from_hex(body, **kwargs)
        else:
            formated_body = encoded_data(body, **kwargs)
        payload = {
            "Records": [
                {
                    "messageId": messageId,
                    "receiptHandle": "MessageReceiptHandle",
                    "body": formated_body,  # 目前只考虑了dict格式，实际可以传入str
                    "attributes": {
                        "ApproximateReceiveCount": "1",
                        "SentTimestamp": "1523232000000",
                        "SenderId": "************",
                        "ApproximateFirstReceiveTimestamp": int(time.time()),
                    },
                    "messageAttributes": {},
                    # "md5OfBody": "7b270e59b47ff90a553787216d55d91d",
                    "eventSource": "aws:sqs",
                    "eventSourceARN": f"arn:{{partition}}:sqs:{region_name}:************:SWITCH-BOT-RUNNER",
                    "awsRegion": region_name,
                }
            ]
        }
        return self.request(region_name, function_name, payload, body, **kwargs)

    def _send_request_safe_mode(
        self, region_name, function_name, version, payload
    ) -> dict:
        try:
            client = self.client(
                "lambda",
                config=Config(
                    region_name=region_name,
                    tcp_keepalive=True,
                ),
            )
            return client.invoke(
                FunctionName=function_name,
                Payload=payload,
                LogType="Tail",
                Qualifier=version,
            )
        except (InvalidRegionError, PartialCredentialsError, ParamValidationError):
            raise
        except ClientError as ex:
            logger.error("{} {}", self.profile_name, ex)
            raise
            # return {
            #     'StatusCode': 0,
            #     'FunctionError': str(ex),
            #     'LogResult': '',
            #     'Payload': StreamingBody(BytesIO(), 0),
            #     'ExecutedVersion': version
            # }
