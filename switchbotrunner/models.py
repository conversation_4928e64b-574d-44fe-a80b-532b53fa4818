import os
from collections.abc import Callable
from enum import Enum
from typing import Any

from pydantic import BaseModel, Field, HttpUrl

Name = str
Url = str
BaseUrl = HttpUrl | str
VariablesMapping = dict[str, Any]
FunctionsMapping = dict[str, Callable]
Headers = dict[str, str]
Cookies = dict[str, str]
Verify = bool
Hooks = list[str | dict[str, str]]
Export = list[str]
Validators = list[dict]
Env = dict[str, Any]


class MethodEnum(str, Enum):
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    HEAD = "HEAD"
    OPTIONS = "OPTIONS"
    PATCH = "PATCH"


class ProtoType(Enum):
    Binary = 1
    CyBinary = 2
    Compact = 3
    Json = 4


class TransType(Enum):
    Buffered = 1
    CyBuffered = 2
    Framed = 3
    CyFramed = 4


Path = str
Token = str


class ProfileEnum(str, Enum):
    TEST = "test"
    PROD = "prod"


class RegionEnum(str, Enum):
    US = "us-east-1"
    AP = "ap-northeast-1"
    EU = "eu-central-1"


# configs for thrift rpc
class TConfigThrift(BaseModel):
    psm: str = None
    env: str = None
    cluster: str = None
    target: str = None
    include_dirs: list[str] = None
    thrift_client: Any = None
    timeout: int = 10
    idl_path: str = None
    method: str = None
    ip: str = "127.0.0.1"
    port: int = 9000
    service_name: str = None
    proto_type: ProtoType = ProtoType.Binary
    trans_type: TransType = TransType.Buffered


# configs for db
class TConfigDB(BaseModel):
    psm: str = None
    user: str = None
    password: str = None
    ip: str = None
    port: int = 3306
    database: str = None


class TransportEnum(str, Enum):
    BUFFERED = "buffered"
    FRAMED = "framed"


class TThriftRequest(BaseModel):
    """rpc request model"""

    method: str = ""
    params: dict = {}
    thrift_client: Any = None
    idl_path: str = ""  # idl local path
    timeout: int = 10  # sec
    transport: TransportEnum = TransportEnum.BUFFERED
    include_dirs: list[str | None] = []  # param of thriftpy2.load
    target: str = ""  # tcp://{ip}:{port} or sd://psm?cluster=xx&env=xx
    env: str = "prod"
    cluster: str = "default"
    psm: str = ""
    service_name: str = None
    ip: str = None
    port: int = None
    proto_type: ProtoType = None
    trans_type: TransType = None


class SqlMethodEnum(str, Enum):
    FETCHONE = "FETCHONE"
    FETCHMANY = "FETCHMANY"
    FETCHALL = "FETCHALL"
    INSERT = "INSERT"
    UPDATE = "UPDATE"
    DELETE = "DELETE"


class TSqlRequest(BaseModel):
    """sql request model"""

    db_config: TConfigDB = TConfigDB()
    method: SqlMethodEnum = None
    sql: str = None
    size: int = 0  # limit nums of sql result


class TConfig(BaseModel):
    name: Name
    verify: Verify = False
    base_url: BaseUrl = ""
    # Text: prepare variables in debugtalk.py, ${gen_variables()}
    variables: VariablesMapping | str = {}
    parameters: VariablesMapping | str = {}
    # setup_hooks: Hooks = []
    # teardown_hooks: Hooks = []
    export: Export = []
    path: str = None
    # configs for other protocols
    thrift: TConfigThrift = None
    db: TConfigDB = TConfigDB()
    # aws_env: ProfileEnum = Field(ProfileEnum.TEST, alias="profile")


class TRequest(BaseModel):
    """requests.Request model"""

    method: MethodEnum
    url: Url
    # params: Dict[Text, Text] = {}
    params: dict[str, str | list[str]] = {}
    headers: Headers = {}
    req_json: dict | list | str = Field(None, alias="json")
    data: str | dict[str, Any] = None
    cookies: Cookies = {}
    timeout: float = 120
    allow_redirects: bool = True
    verify: Verify = False
    upload: dict = {}  # used for upload files


class ILambda(BaseModel):
    region_name: RegionEnum = Field(RegionEnum.US, alias="region")
    function_name: Name = Field(None, alias="function")  # lambda function name
    version: str = "$LATEST"  # lambda function version


class TAPIGatewayAWSProxy(ILambda):
    # requestContext.requestId
    method: MethodEnum  # httpMethod
    path: Path
    path_params: dict[str, str] = {}
    params: dict[str, str | list[str]] = {}
    headers: Headers = {}
    req_json: dict | list | str = Field(None, alias="json")  # body
    data: str | dict[str, Any] | None = None
    event: dict[str, Any] = {}  # extend data for lambda event


class TAPIGatewayAuthorizer(ILambda):
    token: Token  # jwt token
    # methodArn: Path


class TSqs(ILambda):
    body: str | dict[str, Any] | None = None
    encoded_data: bool = True
    data_from_hex: bool = False


class TKinesisDataStream(ILambda):
    data: dict[str, Any] | None = None
    encoded_data: bool = True
    data_from_hex: bool = False


class TLambda(ILambda):
    event: dict[str, Any] = {}  # data for lambda event


class TStep(BaseModel):
    name: Name
    request: TRequest | None = None
    testcase: str | Callable | None = None
    variables: VariablesMapping = {}
    setup_hooks: Hooks = []
    teardown_hooks: Hooks = []
    # used to extract request's response field
    extract: VariablesMapping = {}
    # used to export session variables from referenced testcase
    export: Export = []
    validators: Validators = Field([], alias="validate")
    validate_script: list[str] = []
    retry_times: int = 0
    retry_interval: int = 0  # sec
    thrift_request: TThriftRequest | None = None
    sql_request: TSqlRequest | None = None

    # add custom model
    api_gateway_request: TAPIGatewayAWSProxy | None = None
    authorizer_request: TAPIGatewayAuthorizer | None = None
    sqs_request: TSqs | None = None
    kinesis_request: TKinesisDataStream | None = None
    lambda_request: TLambda | None = None


class TestCase(BaseModel):
    config: TConfig
    teststeps: list[TStep]


class ProjectMeta(BaseModel):
    debugtalk_py: str = ""  # debugtalk.py file content
    debugtalk_path: str = ""  # debugtalk.py file path
    dot_env_path: str = ""  # .env file path
    functions: FunctionsMapping = {}  # functions defined in debugtalk.py
    env: Env = {}
    RootDir: str = (
        os.getcwd()
    )  # project root directory (ensure absolute), the path debugtalk.py located


class TestsMapping(BaseModel):
    project_meta: ProjectMeta
    testcases: list[TestCase]


class TestCaseTime(BaseModel):
    start_at: float = 0
    start_at_iso_format: str = ""
    duration: float = 0


class TestCaseInOut(BaseModel):
    config_vars: VariablesMapping = {}
    export_vars: dict = {}


class RequestStat(BaseModel):
    content_size: float = 0
    response_time_ms: float = 0
    elapsed_ms: float = 0


class AddressData(BaseModel):
    client_ip: str = "N/A"
    client_port: int = 0
    server_ip: str = "N/A"
    server_port: int = 0


class RequestData(BaseModel):
    method: MethodEnum = MethodEnum.GET
    url: Url
    headers: Headers = {}
    cookies: Cookies = {}
    body: str | bytes | list | dict | None = {}


class ResponseData(BaseModel):
    status_code: int
    headers: dict
    cookies: Cookies
    encoding: str | None = None
    content_type: str
    body: str | bytes | list | dict | None


class ReqRespData(BaseModel):
    request: RequestData
    response: ResponseData


class SessionData(BaseModel):
    """request session data, including request, response, validators and stat data"""

    success: bool = False
    # in most cases, req_resps only contains one request & response
    # while when 30X redirect occurs, req_resps will contain multiple request & response
    req_resps: list[ReqRespData] = []
    stat: RequestStat = RequestStat()
    address: AddressData = AddressData()
    validators: dict = {}


class StepResult(BaseModel):
    """teststep data, each step maybe corresponding to one request or one testcase"""

    name: str = ""  # teststep name
    step_type: str = ""  # teststep type, request or testcase
    success: bool = False
    data: SessionData | list["StepResult"] = None
    elapsed: float = 0.0  # teststep elapsed time
    content_size: float = 0  # response content size
    export_vars: VariablesMapping = {}
    attachment: str = ""  # teststep attachment
    # 下面为新增的
    start_at: float = 0  # teststep start time
    fail_reason: str | None = None


StepResult.update_forward_refs()


class IStep:
    def name(self) -> str:
        raise NotImplementedError

    def type(self) -> str:
        raise NotImplementedError

    def struct(self) -> TStep:
        raise NotImplementedError

    def run(self, runner) -> StepResult:
        # runner: HttpRunner
        raise NotImplementedError


class TestCaseSummary(BaseModel):
    name: str
    success: bool
    case_id: str
    time: TestCaseTime
    in_out: TestCaseInOut = {}
    log: str = ""
    step_results: list[StepResult] = []


class PlatformInfo(BaseModel):
    httprunner_version: str
    python_version: str
    platform: str


class Stat(BaseModel):
    total: int = 0
    success: int = 0
    fail: int = 0


class TestSuiteSummary(BaseModel):
    success: bool = False
    stat: Stat = Stat()
    time: TestCaseTime = TestCaseTime()
    platform: PlatformInfo
    testcases: list[TestCaseSummary]


class LambdaRequestData(BaseModel):
    region: RegionEnum
    function: str
    version: str
    payload: str | bytes | list | dict | None = {}


class LambdaResponseData(BaseModel):
    status_code: int
    function_error: str | None
    log_result: str
    executed_version: str
    payload: str | bytes | list | dict | None
    response_meta_data: dict | None
    request: dict[str, Any] | None


class LambdaReqRespData(BaseModel):
    request: LambdaRequestData
    response: LambdaResponseData


class Boto3SessionData(BaseModel):
    success: bool = False
    req_resps: list[LambdaReqRespData] = []
    stat: RequestStat = RequestStat()
    validators: dict = {}
