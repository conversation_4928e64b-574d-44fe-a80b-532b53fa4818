import time
from contextlib import contextmanager
from typing import Optional

from loguru import logger
from prometheus_client import Collector<PERSON><PERSON><PERSON><PERSON>, <PERSON>au<PERSON>, push_to_gateway

RUNNING = 0
COMPLETED = 1


class TestMetrics:
    _instance: Optional["TestMetrics"] = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, pushgateway_url: str = "http://localhost:9091"):
        if not hasattr(self, "initialized"):
            self.pushgateway_url = pushgateway_url
            self.registry = CollectorRegistry()

            # 测试结果指标
            self.test_result = Gauge(
                "switchbot_test_result",
                "Test execution result (1=success, 0=failure)",
                ["test_suite"],
                registry=self.registry,
            )

            # 测试持续时间指标
            self.test_duration = Gauge(
                "switchbot_test_duration_seconds",
                "Test execution duration in seconds",
                ["test_suite"],
                registry=self.registry,
            )

            # 测试用例数量指标
            self.test_case_count = Gauge(
                "switchbot_test_case_count",
                "Number of test cases",
                ["test_suite"],
                registry=self.registry,
            )

            # 测试开始时间
            self.test_start_time = Gauge(
                "switchbot_test_start_timestamp",
                "Test start timestamp",
                ["test_suite"],
                registry=self.registry,
            )

            # 测试结束时间
            self.test_end_time = Gauge(
                "switchbot_test_end_timestamp",
                "Test end timestamp",
                ["test_suite"],
                registry=self.registry,
            )

            # 测试运行状态
            self.test_status = Gauge(
                "switchbot_test_status",
                "Test execution status (0=running, 1=completed)",
                ["test_suite"],
                registry=self.registry,
            )

            # 步骤执行时间
            self.step_duration = Gauge(
                "switchbot_step_duration_seconds",
                "Step execution duration in seconds",
                ["test_suite", "step_name"],
                registry=self.registry,
            )

            self.step_result = Gauge(
                "switchbot_step_result",
                "Step success (1) or failure (0)",
                ["test_suite", "step_name"],
                registry=self.registry,
            )

            # 步骤重试次数
            self.step_retry_count = Gauge(
                "switchbot_step_retry_count",
                "Number of retries for each step",
                ["test_suite", "step_name"],
                registry=self.registry,
            )

            self.current_suite = None
            self.case_id = None
            self.start_time = None
            self.step_times = {}
            self.initialized = True

    @contextmanager
    def test_execution_context(self, test_suite: str, case_id: str):
        """测试执行上下文管理器 退出时推送到 Pushgateway"""
        self.current_suite = test_suite
        self.case_id = case_id
        self.start_time = time.time()
        self.step_times = {}
        self.test_start_time.labels(test_suite=test_suite).set(self.start_time)
        self.test_status.labels(test_suite=test_suite).set(RUNNING)

        try:
            yield self
        finally:
            end_time = time.time()
            self.test_end_time.labels(test_suite=test_suite).set(end_time)
            self.test_status.labels(test_suite=test_suite).set(COMPLETED)
            duration = end_time - self.start_time
            self.test_duration.labels(test_suite=test_suite).set(duration)

            try:
                push_to_gateway(
                    self.pushgateway_url,
                    job="switchbot_runner",
                    registry=self.registry,
                    timeout=3,
                )
                logger.info(f"Successfully pushed metrics to {self.pushgateway_url}")
            except Exception as e:
                logger.error(
                    f"Failed to push metrics to {self.pushgateway_url}: {str(e)}"
                )

    def record_step_execution(self, step_name: str, duration: float):
        """记录单个步骤的执行时间"""
        if self.current_suite:
            # logger.info("record_step_execution: {} {}", step_name, duration)
            self.step_times[step_name] = duration
            self.step_duration.labels(
                test_suite=self.current_suite, step_name=step_name
            ).set(duration)

    def record_step_result(self, step_name: str, success: bool):
        if self.current_suite:
            self.step_result.labels(
                test_suite=self.current_suite, step_name=step_name
            ).set(1 if success else 0)

    def record_test_result(self, success: bool, case_count: int):
        """记录测试结果"""
        if not self.current_suite:
            logger.warning("No active test suite context")
            return

        self.test_result.labels(test_suite=self.current_suite).set(1 if success else 0)
        self.test_case_count.labels(test_suite=self.current_suite).set(case_count)

    def record_step_retry(self, step_name: str, retry_count: int):
        """记录步骤重试次数"""
        if self.current_suite:
            self.step_retry_count.labels(
                test_suite=self.current_suite, step_name=step_name
            ).set(retry_count)
