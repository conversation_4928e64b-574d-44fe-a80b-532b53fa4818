import unittest

from examples.aws_lambda.request_methods.api_gateway_request_with_functions_test import (
    TestCaseApiGatewayRequestWithFunctions,
)


class TestRunRequest(unittest.TestCase):
    def test_run_request(self):
        runner = TestCaseApiGatewayRequestWithFunctions().test_start()
        summary = runner.get_summary()
        self.assertTrue(summary.success)
        self.assertEqual(summary.name, "api-gateway request methods testcase with functions")
        self.assertEqual(len(summary.step_results), 1)
        self.assertEqual(summary.step_results[0].name, "get with params")
