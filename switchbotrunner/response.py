import asyncio
from typing import Any

import jmespath
from jmespath.exceptions import JMESPathError
from loguru import logger

from switchbotrunner import exceptions
from switchbotrunner.exceptions import ParamsError, ValidationFailure
from switchbotrunner.models import Validators, VariablesMapping
from switchbotrunner.parser import Parser, parse_string_value
from switchbotrunner.utils import (
    async_assert_function,
    async_validate_iot_cert,
    get_task_results,
)


def get_uniform_comparator(comparator: str):
    """convert comparator alias to uniform name"""
    if comparator in ["eq", "equals", "equal"]:
        return "equal"
    elif comparator in ["lt", "less_than"]:
        return "less_than"
    elif comparator in ["le", "less_or_equals"]:
        return "less_or_equals"
    elif comparator in ["gt", "greater_than"]:
        return "greater_than"
    elif comparator in ["ge", "greater_or_equals"]:
        return "greater_or_equals"
    elif comparator in ["ne", "not_equal"]:
        return "not_equal"
    elif comparator in ["str_eq", "string_equals"]:
        return "string_equals"
    elif comparator in ["len_eq", "length_equal"]:
        return "length_equal"
    elif comparator in [
        "len_gt",
        "length_greater_than",
    ]:
        return "length_greater_than"
    elif comparator in [
        "len_ge",
        "length_greater_or_equals",
    ]:
        return "length_greater_or_equals"
    elif comparator in ["len_lt", "length_less_than"]:
        return "length_less_than"
    elif comparator in [
        "len_le",
        "length_less_or_equals",
    ]:
        return "length_less_or_equals"
    else:
        return comparator


def uniform_validator(validator):
    """unify validator

    Args:
        validator (dict): validator maybe in two formats:

            format1: this is kept for compatibility with the previous versions.
                {"check": "status_code", "comparator": "eq", "expect": 201, "message": "test"}
                {"check": "status_code", "assert": "eq", "expect": 201, "msg": "test"}
            format2: recommended new version, {assert: [check_item, expected_value, msg]}
                {'eq': ['status_code', 201, "test"]}

    Returns
        dict: validator info

            {
                "check": "status_code",
                "expect": 201,
                "assert": "equal",
                "message": "test
            }

    """
    if not isinstance(validator, dict):
        raise ParamsError(f"invalid validator: {validator}")

    if "check" in validator and "expect" in validator:
        # format1
        check_item = validator["check"]
        expect_value = validator["expect"]

        if "assert" in validator:
            comparator = validator.get("assert")
        else:
            comparator = validator.get("comparator", "eq")

        if "msg" in validator:
            message = validator.get("msg")
        else:
            message = validator.get("message", "")

    elif len(validator) == 1:
        # format2
        comparator = list(validator.keys())[0]
        compare_values = validator[comparator]

        if not isinstance(compare_values, list) or len(compare_values) not in [2, 3]:
            raise ParamsError(f"invalid validator: {validator}")

        check_item = compare_values[0]
        expect_value = compare_values[1]
        if len(compare_values) == 3:
            message = compare_values[2]
        else:
            # len(compare_values) == 2
            message = ""

    else:
        raise ParamsError(f"invalid validator: {validator}")

    # uniform comparator, e.g. lt => less_than, eq => equals
    assert_method = get_uniform_comparator(comparator)

    return {
        "check": check_item,
        "expect": expect_value,
        "assert": assert_method,
        "message": message,
    }


class ResponseObjectBase:
    def __init__(self, resp_obj, parser: Parser):
        """initialize with a response object

        Args:
            resp_obj (instance): requests.Response instance

        """
        self.resp_obj = resp_obj
        self.parser = parser
        self.validation_results: dict = {}

    def extract(
        self,
        extractors: dict[str, str],
        variables_mapping: VariablesMapping = None,
    ) -> dict[str, Any]:
        if not extractors:
            return {}

        extract_mapping = {}
        for key, field in extractors.items():
            if "$" in field:
                # field contains variable or function
                field = self.parser.parse_data(field, variables_mapping)
            field_value = self._search_jmespath(field)
            extract_mapping[key] = field_value

        logger.info(f"extract mapping: {extract_mapping}")
        return extract_mapping

    def _search_jmespath(self, expr: str) -> Any:
        try:
            check_value = jmespath.search(expr, self.resp_obj)
        except JMESPathError as ex:
            logger.error(
                f"failed to search with jmespath\n"
                f"expression: {expr}\n"
                f"data: {self.resp_obj}\n"
                f"exception: {ex}"
            )
            raise
        return check_value

    def validate(
        self,
        validators: Validators,
        variables_mapping: VariablesMapping = None,
    ):
        variables_mapping = variables_mapping or {}

        self.validation_results = {}
        if not validators:
            return

        validate_pass = True
        failures = []

        for v in validators:
            if "validate_extractor" not in self.validation_results:
                self.validation_results["validate_extractor"] = []

            u_validator = uniform_validator(v)

            # check item
            check_item = u_validator["check"]
            if "$" in check_item:
                # check_item is variable or function
                check_item = self.parser.parse_data(check_item, variables_mapping)
                check_item = parse_string_value(check_item)

            if check_item and isinstance(check_item, str):
                check_value = self._search_jmespath(check_item)
            else:
                # variable or function evaluation result is "" or not text
                check_value = check_item

            # comparator
            assert_method = u_validator["assert"]
            assert_func = self.parser.get_mapping_function(assert_method)

            # expect item
            expect_item = u_validator["expect"]
            # parse expected value with config/teststep/extracted variables
            expect_value = self.parser.parse_data(expect_item, variables_mapping)

            # message
            message = u_validator["message"]
            # parse message with config/teststep/extracted variables
            message = self.parser.parse_data(message, variables_mapping)

            validate_msg = f"assert {check_item} {assert_method} {expect_value}({type(expect_value).__name__})"

            validator_dict = {
                "comparator": assert_method,
                "check": check_item,
                "check_value": check_value,
                "expect": expect_item,
                "expect_value": expect_value,
                "message": message,
            }

            try:
                assert_func(check_value, expect_value, message)
                validate_msg += "\t==> pass"
                logger.info(validate_msg)
                validator_dict["check_result"] = "pass"
            except AssertionError as ex:
                validate_pass = False
                validator_dict["check_result"] = "fail"
                validate_msg += "\t==> fail"
                validate_msg += (
                    f"\n"
                    f"check_item: {check_item}\n"
                    f"check_value: {check_value}({type(check_value).__name__})\n"
                    f"assert_method: {assert_method}\n"
                    f"expect_value: {expect_value}({type(expect_value).__name__})"
                )
                message = str(ex)
                if message:
                    validate_msg += f"\nmessage: {message}"

                logger.error(validate_msg)
                failures.append(validate_msg)

            self.validation_results["validate_extractor"].append(validator_dict)

        if not validate_pass:
            failures_string = "\n".join(list(failures))
            raise ValidationFailure(failures_string)


class ResponseObject(ResponseObjectBase):
    def __getattr__(self, key):
        if key in ["json", "content", "body"]:
            try:
                value = self.resp_obj.json()
            except ValueError:
                value = self.resp_obj.content
        elif key == "cookies":
            value = self.resp_obj.cookies.get_dict()
        else:
            try:
                value = getattr(self.resp_obj, key)
            except AttributeError:
                err_msg = f"ResponseObject does not have attribute: {key}"
                logger.error(err_msg)
                raise exceptions.ParamsError(err_msg)

        self.__dict__[key] = value
        return value

    def _search_jmespath(self, expr: str) -> Any:
        resp_obj_meta = {
            "status_code": self.status_code,
            "headers": self.headers,
            "cookies": self.cookies,
            "body": self.body,
        }
        if not expr.startswith(tuple(resp_obj_meta.keys())):
            if hasattr(self.resp_obj, expr):
                return getattr(self.resp_obj, expr)
            else:
                return expr

        try:
            check_value = jmespath.search(expr, resp_obj_meta)
        except JMESPathError as ex:
            logger.error(
                f"failed to search with jmespath\n"
                f"expression: {expr}\n"
                f"data: {resp_obj_meta}\n"
                f"exception: {ex}"
            )
            raise

        return check_value


class ThriftResponseObject(ResponseObjectBase):
    pass


class SqlResponseObject(ResponseObjectBase):
    pass


def log_uniform_validator(validator):
    """unify validator

    Args:
        validator (dict): validator maybe in two formats:

            format1: this is kept for compatibility with the previous versions.
                {"check": "status_code", "comparator": "eq", "expect": 201, "message": "test"}
                {"check": "status_code", "assert": "eq", "expect": 201, "msg": "test"}
            format2: recommended new version, {assert: [check_item, expected_value, msg]}
                {'eq': ['status_code', 201, "test"]}

    Returns
        dict: validator info

            {
                "check": "status_code",
                "expect": 201,
                "assert": "equal",
                "message": "test
            }

    """
    if not isinstance(validator, dict):
        raise ParamsError(f"invalid validator: {validator}")

    if "check" in validator and "expect" in validator:
        # format1
        check_item = validator["check"]
        expect_value = validator["expect"]

        if "assert" in validator:
            comparator = validator.get("assert")
        else:
            comparator = validator.get("comparator", "eq")

        if "condition" in validator:
            condition = validator.get("condition")
        else:
            condition = validator.get("condition", "")

        if "region" in validator:
            region = validator.get("region")
        else:
            region = validator.get("region", "")

    elif len(validator) == 1:
        # format2
        comparator = list(validator.keys())[0]
        compare_values = validator[comparator]

        if not isinstance(compare_values, list) or len(compare_values) not in [2, 3, 4]:
            raise ParamsError(f"invalid validator: {validator}")

        check_item = compare_values[0]
        expect_value = compare_values[1]
        if len(compare_values) == 3:
            condition = compare_values[2]
        else:
            condition = ""
        if len(compare_values) == 4:
            condition = compare_values[2]
            region = compare_values[3]
        else:
            region = ""

    else:
        raise ParamsError(f"invalid validator: {validator}")

    # uniform comparator, e.g. lt => less_than, eq => equals
    assert_method = get_uniform_comparator(comparator)

    return {
        "check": check_item,
        "expect": expect_value,
        "assert": assert_method,
        "condition": condition,
        "region": region,
    }


class RequestResponseObject(ResponseObject):
    UNORMAL_FUNC = ("cert_policy",)

    def __init__(self, resp_obj, parser: Parser, profile_name: str = "test"):
        super().__init__(resp_obj, parser)
        self.profile_name = profile_name

    def validate(
        self,
        validators: Validators,
        variables_mapping: VariablesMapping = None,
    ):
        variables_mapping = variables_mapping or {}

        self.validation_results = {}
        if not validators:
            return

        validate_pass = True
        failures = []

        validate_msgs = []
        validator_dicts = []
        validator_tasks = []  # async tasks
        validator_methods = []

        for v in validators:
            if "validate_extractor" not in self.validation_results:
                self.validation_results["validate_extractor"] = []

            u_validator = log_uniform_validator(v)
            # comparator
            assert_method = u_validator["assert"]

            if assert_method not in self.UNORMAL_FUNC:
                u_validator = uniform_validator(v)

            # check item
            check_item = u_validator["check"]
            if "$" in check_item:
                # check_item is variable or function
                check_item = self.parser.parse_data(check_item, variables_mapping)
                check_item = parse_string_value(check_item)

            if check_item and isinstance(check_item, str):
                check_value = self._search_jmespath(check_item)
            else:
                # variable or function evaluation result is "" or not text
                check_value = check_item

            # expect item
            expect_item = u_validator["expect"]
            # parse expected value with config/teststep/extracted variables
            expect_value = self.parser.parse_data(expect_item, variables_mapping)

            if assert_method not in self.UNORMAL_FUNC:
                assert_func = self.parser.get_mapping_function(assert_method)

                # expect item
                expect_item = u_validator["expect"]
                # parse expected value with config/teststep/extracted variables
                expect_value = self.parser.parse_data(expect_item, variables_mapping)

                # message
                message = u_validator["message"]
                # parse message with config/teststep/extracted variables
                message = self.parser.parse_data(message, variables_mapping)

                validate_msg = f"assert {check_item} {assert_method} {expect_value}({type(expect_value).__name__})"
                validate_msgs.append(validate_msg)

                validator_dict = {
                    "comparator": assert_method,
                    "check": check_item,
                    "check_value": check_value,
                    "expect": expect_item,
                    "expect_value": expect_value,
                    "message": message,
                }
                validator_dicts.append(validator_dict)

                # 非log相关的断言也放在异步任务里并行断言
                validator_tasks.append(
                    async_assert_function(
                        assert_func, check_value, expect_value, message
                    )
                )
                validator_methods.append(assert_method)
            else:
                # condition
                condition = self.parse_value_from_context(
                    u_validator, variables_mapping, "condition"
                )
                # expect_value
                expect_value = self.parse_value_from_context(
                    u_validator, variables_mapping, "expect"
                )

                ## region
                region = u_validator["region"]
                # parse region with config/teststep/extracted variables
                region = self.parser.parse_data(region, variables_mapping)

                validate_msg = f"assert {region} {check_item} {assert_method} {expect_value}({type(expect_value).__name__})"
                validate_msgs.append(validate_msg)

                validator_dict = {
                    "comparator": assert_method,
                    "check": check_item,
                    "check_value": check_value,
                    "expect": expect_item,
                    "expect_value": expect_value,
                    "condition": condition,
                    "region": region,
                }
                validator_dicts.append(validator_dict)

                # async validate tasks
                validator_tasks.append(
                    async_validate_iot_cert(
                        cert_pem=check_value,
                        expect_client_id=expect_value,
                        key_pem=condition,
                        region_name=region,
                        profile_name=self.profile_name,
                    )
                )
                validator_methods.append(assert_method)

        results = asyncio.run(get_task_results(tasks=validator_tasks))
        for index, result in enumerate(results):
            validate_msg = validate_msgs[index]
            validator_dict = validator_dicts[index]
            assert_method = validator_methods[index]
            if assert_method == "cert_policy":
                has_log, error = result
                condition = not error and bool(has_log)
                if not assert_for_cert_condition(
                    condition, error, validator_dict, validate_msg, failures
                ):
                    validate_pass = False
            elif assert_method == "not_cert_policy":
                # 没有错误，并且没有日志
                has_log, error = result
                condition = not error and not bool(has_log)
                if not assert_for_cert_condition(
                    condition, error, validator_dict, validate_msg, failures
                ):
                    validate_pass = False
            else:
                # 非log相关的断言
                if not assert_for_generic_condition(
                    result, validator_dict, validate_msg, failures
                ):
                    validate_pass = False

            self.validation_results["validate_extractor"].append(validator_dict)

            if not validate_pass:
                failures_string = "\n".join(list(failures))
                raise ValidationFailure(failures_string)

    def parse_value_from_context(self, u_validator, variables_mapping, key):
        """value 从变量和响应的jmespath中提取出来"""
        # item
        item = u_validator[key]
        if "$" in item:
            # item is variable or function
            item = self.parser.parse_data(item, variables_mapping)
            item = parse_string_value(item)

        if item and isinstance(item, str):
            value = self._search_jmespath(item)
        else:
            # variable or function evaluation result is "" or not text
            value = item
        return value


def assert_for_generic_condition(
    ex: Exception | None,
    validator_dict,
    validate_msg,
    failures,
):
    if not ex:
        validate_msg += "\t==> pass"
        logger.info(validate_msg)
        validator_dict["check_result"] = "pass"
        return True
    else:
        validator_dict["check_result"] = "fail"
        validate_msg += "\t==> fail"
        validate_msg += (
            f"\n"
            f"check_item: {validator_dict['check']}\n"
            f"check_value: {validator_dict['check_value']}({type(validator_dict['check_value']).__name__})\n"
            f"assert_method: {validator_dict['comparator']}\n"
            f"expect_value: {validator_dict['expect_value']}({type(validator_dict['expect_value']).__name__})"
        )
        message = str(ex)
        if message:
            validate_msg += f"\nmessage: {message}"
        logger.error(validate_msg)
        failures.append(validate_msg)
        return False


def assert_for_cert_condition(
    condition: bool,
    error: Exception,
    validator_dict,
    validate_msg,
    failures,
) -> bool:
    if condition:
        validate_msg += "\t==> pass"
        logger.info(validate_msg)
        validator_dict["check_result"] = "pass"
        return True
    else:
        validator_dict["check_result"] = "fail"
        validate_msg += "\t==> fail"
        validate_msg += (
            f"\n"
            f"check_item: {validator_dict['check']}\n"
            f"check_value: {validator_dict['check_value']}({type(validator_dict['check_value']).__name__})\n"
            f"assert_method: {validator_dict['comparator']}\n"
            f"expect_value: {validator_dict['expect_value']}({type(validator_dict['expect_value']).__name__})\n"
            f"region: {validator_dict['region']}"
        )
        message = "" if error is None else str(error)
        if message:
            validate_msg += f"\nmessage: {message}"
        logger.error(validate_msg)
        failures.append(validate_msg)
        return False
