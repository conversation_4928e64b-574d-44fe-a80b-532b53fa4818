import argparse
import os
import sys

import pytest
from loguru import logger
from pytest import ExitCode

from switchbotrunner import __description__, __version__
from switchbotrunner.compat import ensure_cli_args
from switchbotrunner.make import init_make_parser, main_make
from switchbotrunner.utils import init_logger


def init_parser_run(subparsers):
    sub_parser_run = subparsers.add_parser(
        "run", help="Make SwitchBotRunner testcases and run with pytest."
    )
    # 保留参数定义,用于传递到SessionRunner
    sub_parser_run.add_argument(
        "--pushgateway",
        help="Prometheus Pushgateway URL",
        default="http://localhost:9091",
    )
    return sub_parser_run


def main_run(extra_args) -> int:
    # 执行测试用例
    remaining_args = ensure_cli_args(extra_args)

    tests_path_list = []
    extra_args_new = []
    for item in remaining_args:
        if not os.path.exists(item):
            extra_args_new.append(item)
        else:
            tests_path_list.append(item)

    if len(tests_path_list) == 0:
        logger.error(f"No valid testcase path in cli arguments: {remaining_args}")
        sys.exit(ExitCode.INTERNAL_ERROR)

    testcase_path_list = main_make(tests_path_list)
    if not testcase_path_list:
        logger.error("No valid testcases found, exit 3.")
        sys.exit(ExitCode.INTERNAL_ERROR)

    if "--tb=short" not in extra_args_new:
        extra_args_new.append("--tb=short")

    extra_args_new.extend(testcase_path_list)
    logger.info(
        f"start to run tests with pytest. SwitchBotRunner version: {__version__}"
    )

    # 执行测试并获取结果
    return pytest.main(extra_args_new)


def main():
    """API test: parse command line options and run commands."""
    parser = argparse.ArgumentParser(description=__description__)
    parser.add_argument(
        "-V", "--version", dest="version", action="store_true", help="show version"
    )

    subparsers = parser.add_subparsers(help="sub-command help")
    init_parser_run(subparsers)
    sub_parser_make = init_make_parser(subparsers)

    if len(sys.argv) == 1:
        # switchbotrunner
        parser.print_help()
        sys.exit(0)
    elif len(sys.argv) == 2:
        # print help for sub-commands
        if sys.argv[1] in ["-V", "--version"]:
            # switchbotrunner -V
            print(f"{__version__}")
        elif sys.argv[1] in ["-h", "--help"]:
            # switchbotrunner -h
            parser.print_help()
        elif sys.argv[1] == "run":
            # switchbotrunner run
            pytest.main(["-h"])
        elif sys.argv[1] == "make":
            # switchbotrunner make
            sub_parser_make.print_help()
        sys.exit(0)
    elif (
        len(sys.argv) == 3 and sys.argv[1] == "run" and sys.argv[2] in ["-h", "--help"]
    ):
        # switchbotrunner run -h
        pytest.main(["-h"])
        sys.exit(0)

    extra_args = []
    if len(sys.argv) >= 2 and sys.argv[1] in ["run"]:
        args, extra_args = parser.parse_known_args()
    else:
        args = parser.parse_args()

    if args.version:
        print(f"{__version__}")
        sys.exit(0)

    # set log level
    try:
        index = extra_args.index("--log-level")
        if index < len(extra_args) - 1:
            level = extra_args[index + 1]
        else:
            # not specify log level value
            level = "INFO"  # default
    except ValueError:
        level = "INFO"  # default

    init_logger(level)

    if sys.argv[1] == "run":
        sys.exit(main_run(extra_args))
    elif sys.argv[1] == "make":
        main_make(args.testcase_path)


def main_run_alias():
    """command alias
    srun = switchbotrunner run
    """
    if len(sys.argv) == 2:
        if sys.argv[1] in ["-V", "--version"]:
            # srun -V
            sys.argv = ["switchbotrunner", "-V"]
        elif sys.argv[1] in ["-h", "--help"]:
            pytest.main(["-h"])
            sys.exit(0)
        else:
            # srun /path/to/testcase
            sys.argv.insert(1, "run")
    else:
        sys.argv.insert(1, "run")

    main()


def main_make_alias():
    """command alias
    smake = switchbotrunner make
    """
    sys.argv.insert(1, "make")
    main()


if __name__ == "__main__":
    main()
