import argparse
import os
import time
import uuid
from datetime import UTC, datetime

from switchbotrunner.lambda_client import Boto3Session

try:
    import allure

    ALLURE = allure
except ModuleNotFoundError:
    ALLURE = None

from loguru import logger

from switchbotrunner.client import HttpSession
from switchbotrunner.config import Config
from switchbotrunner.exceptions import (
    FunctionNotFound,
    ParamsError,
    ValidationFailure,
    VariableNotFound,
)
from switchbotrunner.loader import load_project_meta
from switchbotrunner.metrics import TestMetrics
from switchbotrunner.models import (
    ProjectMeta,
    StepResult,
    TConfig,
    TestCaseInOut,
    TestCaseSummary,
    TestCaseTime,
    TStep,
    VariablesMapping,
)
from switchbotrunner.parser import Parser
from switchbotrunner.utils import LOGGER_FORMAT, merge_variables


class SessionRunner:
    config: Config
    teststeps: list[TStep]  # list of Step

    parser: Parser = None
    session: HttpSession = None
    boto3_session: Boto3Session = None
    case_id: str = ""
    root_dir: str = ""

    __config: TConfig
    __project_meta: ProjectMeta = None
    __export: list[str] = []
    __step_results: list[StepResult] = []
    __session_variables: VariablesMapping = {}
    __is_referenced: bool = False
    # time
    __start_at: float = 0
    __duration: float = 0
    # log
    __log_path: str = ""

    def __init(self):
        self.__config = self.config.struct()
        self.__session_variables = self.__session_variables or {}
        self.__start_at = 0
        self.__duration = 0
        self.__is_referenced = self.__is_referenced or False

        self.__project_meta = self.__project_meta or load_project_meta(
            self.__config.path
        )
        self.case_id = self.case_id or str(uuid.uuid4())
        self.root_dir = self.root_dir or self.__project_meta.RootDir
        self.__log_path = os.path.join(self.root_dir, "logs", f"{self.case_id}.run.log")

        self.__step_results = self.__step_results or []
        self.session = self.session or HttpSession()
        self.boto3_session = self.boto3_session
        self.parser = self.parser or Parser(self.__project_meta.functions)

    def with_session(self, session: HttpSession) -> "SessionRunner":
        self.session = session
        return self

    def with_boto3_session(self, boto3_session: Boto3Session) -> "SessionRunner":
        self.boto3_session = boto3_session or Boto3Session()
        return self

    def get_config(self) -> TConfig:
        return self.__config

    def set_referenced(self) -> "SessionRunner":
        self.__is_referenced = True
        return self

    def with_case_id(self, case_id: str) -> "SessionRunner":
        self.case_id = case_id
        return self

    def with_variables(self, variables: VariablesMapping) -> "SessionRunner":
        self.__session_variables = variables
        return self

    def with_export(self, export: list[str]) -> "SessionRunner":
        self.__export = export
        return self

    def __parse_config(self, param: dict = None) -> None:
        # parse config variables
        self.__config.variables.update(self.__session_variables)
        if param:
            self.__config.variables.update(param)
        self.__config.variables = self.parser.parse_variables(self.__config.variables)

        # parse config name
        self.__config.name = self.parser.parse_data(
            self.__config.name, self.__config.variables
        )

        # parse config base url
        self.__config.base_url = self.parser.parse_data(
            self.__config.base_url, self.__config.variables
        )

    def get_export_variables(self) -> dict:
        # override testcase export vars with step export
        export_var_names = self.__export or self.__config.export
        export_vars_mapping = {}
        for var_name in export_var_names:
            if var_name not in self.__session_variables:
                raise ParamsError(
                    f"failed to export variable {var_name} from session variables {self.__session_variables}"
                )

            export_vars_mapping[var_name] = self.__session_variables[var_name]

        return export_vars_mapping

    def get_summary(self) -> TestCaseSummary:
        """get testcase result summary"""
        start_at_timestamp = self.__start_at
        start_at_iso_format = datetime.fromtimestamp(
            start_at_timestamp, UTC
        ).isoformat()

        summary_success = True
        for step_result in self.__step_results:
            if not step_result.success:
                summary_success = False
                break

        return TestCaseSummary(
            name=self.__config.name,
            success=summary_success,
            case_id=self.case_id,
            time=TestCaseTime(
                start_at=self.__start_at,
                start_at_iso_format=start_at_iso_format,
                duration=self.__duration,
            ),
            in_out=TestCaseInOut(
                config_vars=self.__config.variables,
                export_vars=self.get_export_variables(),
            ),
            log=self.__log_path,
            step_results=self.__step_results,
        )

    def merge_step_variables(self, variables: VariablesMapping) -> VariablesMapping:
        # override variables
        # step variables > extracted variables from previous steps
        variables = merge_variables(variables, self.__session_variables)
        # step variables > testcase config variables
        variables = merge_variables(variables, self.__config.variables)

        # parse variables
        return self.parser.parse_variables(variables)

    def __run_step(self, step: TStep) -> StepResult:
        """运行单个测试步骤"""
        logger.info(f"run step begin: {step.name()} >>>>>>")
        step_start_time = time.time()
        success = True
        retry_count = 0
        # run step
        for i in range(step.retry_times + 1):
            try:
                if ALLURE is not None:
                    with ALLURE.step(f"step: {step.name()}"):
                        step_result: StepResult = step.run(self)
                else:
                    step_result: StepResult = step.run(self)
                break
            except ValidationFailure as e:
                retry_count = i + 1
                if i == step.retry_times:
                    # 从ValidationFailure中，把失败的步骤加到summary信息
                    self.__session_variables.update(e.step_result.export_vars)
                    self.__step_results.append(e.step_result)
                    success = False
                    raise
                else:
                    logger.warning(
                        f"run step {step.name()} validation failed,wait {step.retry_interval} sec and try again"
                    )
                    time.sleep(step.retry_interval)
                    logger.info(
                        f"run step retry ({i + 1}/{step.retry_times} time): {step.name()} >>>>>>"
                    )
            except (VariableNotFound, FunctionNotFound) as e:
                self.__step_results.append(e.step_result)
                success = False
                raise
            finally:
                # 记录步骤执行时间和重试次数
                step_duration = time.time() - step_start_time
                self.metrics.record_step_execution(step.name(), step_duration)
                self.metrics.record_step_result(step.name(), success)
                if retry_count:
                    self.metrics.record_step_retry(step.name(), retry_count)

        # save extracted variables to session variables
        self.__session_variables.update(step_result.export_vars)
        self.__step_results.append(step_result)
        logger.info(f"run step end: {step.name()} <<<<<<\n")
        return step_result

    def test_start(
        self,
        param: dict = None,
        test_suite_name: str = "",
        # ) -> "SessionRunner":
    ):
        """main entrance, discovered by pytest"""
        print("\n")
        self.__init()
        self.__parse_config(param)

        if ALLURE is not None and not self.__is_referenced:
            # update allure report meta
            ALLURE.dynamic.title(self.__config.name)
            ALLURE.dynamic.description(f"TestCase ID: {self.case_id}")

        # 获取命令行参数中的pushgateway url
        parser = argparse.ArgumentParser()
        parser.add_argument("--pushgateway", default="http://localhost:9091")
        args, _ = parser.parse_known_args()
        logger.info(args)

        logger.info(
            f"Start to run testcase: {self.__config.name}, TestCase ID: {self.case_id}"
        )

        logger.add(self.__log_path, format=LOGGER_FORMAT, level="DEBUG")
        self.__start_at = time.time()
        self.test_suite_name = test_suite_name or self.config.name

        # 初始化指标收集并记录开始
        self.metrics = TestMetrics(args.pushgateway)

        with self.metrics.test_execution_context(self.test_suite_name, self.case_id):
            logger.info(f"Start to run test_suite: {self.test_suite_name}")

            step_result = StepResult(success=True, export_vars={})
            try:
                # 执行测试步骤
                for step in self.teststeps:
                    # 执行步骤
                    step_result = self.__run_step(step)
            finally:
                logger.info(f"generate testcase log: {self.__log_path}")

                # 记录整体执行结果
                case_count = len(self.teststeps)
                self.metrics.record_test_result(
                    success=step_result.success, case_count=case_count
                )

        self.__duration = time.time() - self.__start_at
        # return self


class SwitchBotRunner(SessionRunner):
    # split SessionRunner to keep consistent with golang version
    pass
