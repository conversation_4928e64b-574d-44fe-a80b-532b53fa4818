import copy
import inspect

from switchbotrunner.models import TConfig, VariablesMapping


class Config:
    def __init__(self, name: str, case_id: str | None = None) -> None:
        caller_frame = inspect.stack()[1]
        self.__name: str = name
        self.__base_url: str = ""
        self.__case_id: str | None = case_id
        self.__variables: VariablesMapping = {}
        self.__config = TConfig(name=name, path=caller_frame.filename)

    @property
    def name(self) -> str:
        return self.__config.name

    @property
    def path(self) -> str:
        return self.__config.path

    def variables(self, **variables) -> "Config":
        self.__variables.update(variables)
        return self

    def base_url(self, base_url: str) -> "Config":
        self.__base_url = base_url
        return self

    def verify(self, verify: bool) -> "Config":
        self.__config.verify = verify
        return self

    def export(self, *export_var_name: str) -> "Config":
        self.__config.export.extend(export_var_name)
        self.__config.export = list(set(self.__config.export))
        return self

    def struct(self) -> TConfig:
        self.__init()
        return self.__config

    def __init(self) -> None:
        self.__config.name = self.__name
        self.__config.base_url = self.__base_url
        self.__config.variables = copy.copy(self.__variables)
