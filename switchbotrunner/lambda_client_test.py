import unittest

from switchbotrunner.lambda_client import Boto3Session


class TestBoto3Session(unittest.TestCase):
    def setUp(self):
        self.boto3_session = Boto3Session()

    def test_request_http(self):
        email = "<EMAIL>"
        password = "********"
        self.boto3_session.api_gateway_request(
            "us-east-1",
            "test_lambda_account_api",
            "POST",
            "/account/api/v1/user/login",
            json={
                "clientId": "7iq3mvuryqhw8bdihm5k9uqp13",
                "grantType": "password",
                "username": email,
                "password": password,
                "deviceInfo": {
                    "deviceId": "andorid123",
                    "model": "devin",
                    "deviceName": "androidtest",
                },
            },
        )
        # address = self.boto3_session.data.address
        # self.assertGreater(len(address.server_ip), 0)
        # self.assertEqual(address.server_port, 80)
        # self.assertGreater(len(address.client_ip), 0)
        # self.assertGreater(address.client_port, 10000)
