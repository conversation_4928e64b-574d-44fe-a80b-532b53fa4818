{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Blackbox Exporter (HTTP prober) - Blackbox exporter HTTP prober dashboard", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 13659, "graphTooltip": 0, "id": 28, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "left", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "SSL Cert <PERSON>piry (days)"}, "properties": [{"id": "decimals", "value": 0}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "rgba(0, 0, 0, 0)", "value": null}, {"color": "red", "value": 0}, {"color": "orange", "value": 1}, {"color": "yellow", "value": 7}, {"color": "green", "value": 24}]}}, {"id": "custom.cellOptions", "value": {"mode": "basic", "type": "gauge"}}, {"id": "min", "value": 0}, {"id": "max", "value": 365}, {"id": "custom.filterable", "value": false}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Status"}, "properties": [{"id": "mappings", "value": [{"options": {"0": {"text": "DOWN"}, "1": {"text": "UP"}}, "type": "value"}]}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "color-background"}}, {"id": "custom.width", "value": 76}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Code"}, "properties": [{"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "rgba(0, 0, 0, 0)", "value": null}, {"color": "green", "value": 200}, {"color": "yellow", "value": 300}, {"color": "red", "value": 500}]}}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "color-background"}}, {"id": "mappings", "value": [{"options": {"0": {"text": ""}}, "type": "value"}]}, {"id": "custom.width", "value": 78}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "SSL"}, "properties": [{"id": "mappings", "value": [{"options": {"0": {"text": "NO"}, "1": {"text": "OK"}}, "type": "value"}]}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "rgba(3, 3, 3, 0)", "value": null}, {"color": "red", "value": 0}, {"color": "green", "value": 1}]}}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "color-background"}}, {"id": "custom.width", "value": 77}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Probe Duration (s)"}, "properties": [{"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.8}, {"color": "red", "value": 2}]}}, {"id": "custom.cellOptions", "value": {"mode": "basic", "type": "gauge"}}, {"id": "custom.filterable", "value": false}, {"id": "decimals", "value": 2}, {"id": "max", "value": 3}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "DNS Lookup Duration (s)"}, "properties": [{"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.1}, {"color": "red", "value": 0.2}]}}, {"id": "max", "value": 0.3}, {"id": "custom.cellOptions", "value": {"mode": "basic", "type": "gauge"}}, {"id": "custom.filterable", "value": false}, {"id": "decimals", "value": 3}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Instance"}, "properties": [{"id": "links", "value": [{"targetBlank": true, "title": "${__data.fields.Instance}", "url": "${__data.fields.Instance}"}]}, {"id": "custom.width", "value": 276}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "TLS Version"}, "properties": [{"id": "custom.width", "value": 117}]}]}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 0}, "id": 2, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "SSL Cert <PERSON>piry (days)"}]}, "pluginVersion": "10.1.5", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "probe_success{job=~\"$job\", instance=~\"$instance\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "probe_http_ssl{job=~\"$job\", instance=~\"$instance\"} > 0", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "(probe_ssl_earliest_cert_expiry{job=~\"$job\", instance=~\"$instance\"} - time()) / 3600 / 24", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "probe_http_status_code{job=~\"$job\", instance=~\"$instance\"} > 0", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "avg_over_time(probe_duration_seconds{job=~\"$job\", instance=~\"$instance\"}[1m])", "format": "table", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "probe_tls_version_info{job=~\"$job\", instance=~\"$instance\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "F"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "avg_over_time(probe_dns_lookup_time_seconds{job=~\"$job\", instance=~\"$instance\"}[1m])", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "G"}], "title": "HTTP Probe Overview", "transformations": [{"id": "seriesToColumns", "options": {"byField": "instance", "mode": "outer"}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Time 1": true, "Time 2": true, "Time 3": true, "Time 4": true, "Time 5": true, "Time 6": true, "Time 7": true, "Time 8": true, "Value": false, "Value #A": false, "Value #B": false, "Value #F": true, "__name__": true, "__name__ 1": true, "__name__ 2": true, "__name__ 3": true, "__name__ 4": true, "__name__ 5": true, "__name__ 6": true, "__name__ 7": true, "job": true, "job 1": true, "job 2": true, "job 3": true, "job 4": true, "job 5": true, "job 6": true, "job 7": true, "job 8": true, "phase": true, "type": true, "type 1": true, "type 2": true, "type 3": true, "type 4": true, "type 5": true, "type 6": true, "type 7": true, "type 8": true, "version": false}, "indexByName": {"Time 1": 9, "Time 2": 13, "Time 3": 17, "Time 4": 20, "Time 5": 24, "Time 6": 28, "Time 7": 32, "Value #A": 1, "Value #B": 3, "Value #C": 5, "Value #D": 2, "Value #E": 6, "Value #F": 8, "Value #G": 7, "__name__ 1": 10, "__name__ 2": 14, "__name__ 3": 21, "__name__ 4": 25, "__name__ 5": 29, "instance": 0, "job 1": 11, "job 2": 15, "job 3": 18, "job 4": 22, "job 5": 26, "job 6": 30, "type 1": 12, "type 2": 16, "type 3": 19, "type 4": 23, "type 5": 27, "type 6": 31, "version": 4}, "renameByName": {"Value": "Up", "Value #A": "Status", "Value #B": "SSL", "Value #C": "SSL Cert <PERSON>piry (days)", "Value #D": "Code", "Value #E": "Probe Duration (s)", "Value #F": "", "Value #G": "DNS Lookup Duration (s)", "Value #H": "Probe IP", "instance": "Instance", "type 6": "", "version": "TLS Version"}}}], "transparent": true, "type": "table"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 9}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "sum(probe_success{job=~\"$job\", instance=~\"$instance\"}) by (instance)", "instant": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{ instance }}", "refId": "A"}], "title": "HTTP Probe Status", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "Returns how long the probe took to complete in seconds", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 18}, "id": 13, "options": {"legend": {"calcs": ["mean", "max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "sum(probe_http_duration_seconds{job=~\"$job\", instance=~\"$instance\"}) by (instance)", "instant": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{ instance }}", "refId": "A"}], "title": "HTTP Probe Duration", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 27}, "id": 8, "panels": [], "repeat": "instance", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "refId": "A"}], "title": "$instance", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "Duration of http request by phase, summed over all redirects", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 0, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 28}, "id": 6, "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "probe_http_duration_seconds{job=~\"$job\", instance=~\"$instance\"}", "interval": "", "intervalFactor": 2, "legendFormat": "{{ phase }}", "refId": "A"}], "title": "HTTP Probe Phases Duration", "transformations": [], "type": "timeseries"}], "refresh": "10s", "schemaVersion": 38, "style": "dark", "tags": ["blackbox", "prometheus"], "templating": {"list": [{"current": {"selected": false, "text": "Prometheus", "value": "eb3fc48f-8e40-436c-b84e-9e7db28622d5"}, "hide": 0, "includeAll": false, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": ".+", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(probe_success, job)", "hide": 0, "includeAll": true, "label": "Job", "multi": false, "name": "job", "options": [], "query": "label_values(probe_success, job)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".+", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(probe_success{job=~\"$job\"}, instance)", "hide": 0, "includeAll": true, "label": "Instance", "multi": false, "name": "instance", "options": [], "query": "label_values(probe_success{job=~\"$job\"}, instance)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "API prober", "uid": "api_prober", "version": 8, "weekStart": ""}