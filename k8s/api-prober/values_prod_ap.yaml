replicas: 1
resources:
  limits:
    memory: 50Mi
  requests:
    memory: 50Mi
config:
  modules:
    broker_base_auth_internal:
      prober: http
      timeout: 5s
      http:
        method: POST
        basic_auth:
          username: "3afkbd8mab36sfw"
          password: "Vcil8paC7O82Xd1Pe2J5jm7XGAgvWTtRFt8BAE8DOLhf"
        headers:
          Content-Type: application/json
        body: '{"payload_encoding":"plain","topic":"api/example/topic","qos":0,"clientid":"string","payload":"hello emqx api"}'
    iot_broker_fe:
      prober: http
      timeout: 5s
      http:
        method: POST
        headers:
          Content-Type: application/json
        # <EMAIL>
        body: '{"ServiceName":"autotest","UserID":"917fc9f0-26d1-44a6-9c08-272406f4814c"}'
