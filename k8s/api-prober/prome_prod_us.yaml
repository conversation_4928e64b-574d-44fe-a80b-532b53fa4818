prometheus:
  customJobs:
    - job_name: 'blue-broker-check'
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      metrics_path: /probe
      params:
        module: [broker_base_auth_internal]
        target: [http://core-dashboard-blue.internal.iot.prod.us.switchbot.work/api/v5/publish]
      relabel_configs:
      - source_labels: [__param_target]
        target_label: instance
      - source_labels: [__meta_kubernetes_namespace]
        action: keep
        regex: monitor
      - source_labels: [__meta_kubernetes_service_port_number]
        action: keep
        regex: "9115"
      - source_labels: [__meta_kubernetes_service_label_app_kubernetes_io_name]
        action: keep
        regex: "prometheus-blackbox-exporter"
      kubernetes_sd_configs:
      - role: service
    - job_name: 'green-broker-check'
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      metrics_path: /probe
      params:
        module: [broker_base_auth_internal]
        target: [http://core-dashboard-green.internal.iot.prod.us.switchbot.work/api/v5/publish]
      relabel_configs:
      - source_labels: [__param_target]
        target_label: instance
      - source_labels: [__meta_kubernetes_namespace]
        action: keep
        regex: monitor
      - source_labels: [__meta_kubernetes_service_port_number]
        action: keep
        regex: "9115"
      - source_labels: [__meta_kubernetes_service_label_app_kubernetes_io_name]
        action: keep
        regex: "prometheus-blackbox-exporter"
      kubernetes_sd_configs:
      - role: service
    - job_name: 'iot-broker-fe-check'
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      metrics_path: /probe
      params:
        module: [iot_broker_fe]
        target: [http://iot-broker-fe-0946d5b5a3ca80c4.elb.us-east-1.amazonaws.com:8000/v1/emqx/usercert/get]
        # http://iot-broker-fe-92a733ee231d97ea.elb.ap-northeast-1.amazonaws.com:8000
        # http://iot-broker-fe-ea80c945cdb98fed.elb.eu-central-1.amazonaws.com:8000
      relabel_configs:
      - source_labels: [__param_target]
        target_label: instance
      - source_labels: [__meta_kubernetes_namespace]
        action: keep
        regex: monitor
      - source_labels: [__meta_kubernetes_service_port_number]
        action: keep
        regex: "9115"
      - source_labels: [__meta_kubernetes_service_label_app_kubernetes_io_name]
        action: keep
        regex: "prometheus-blackbox-exporter"
      kubernetes_sd_configs:
      - role: service