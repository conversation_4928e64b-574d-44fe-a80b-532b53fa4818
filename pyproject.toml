[build-system]
requires = ["setuptools==75.1.0"]
build-backend = "setuptools.build_meta"

[project]
name = "switchbotrunner"
version = "v1.0.0"
description = "One-step solution for switchbot testing."
authors = []
requires-python = ">=3.13"
readme = "README.md"
license = {text = "Apache-2.0"}
keywords = [
    "HTTP",
    "apitest",
    "lambdatest",
]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Topic :: Software Development :: Testing",
    "Topic :: Software Development :: Quality Assurance",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Operating System :: MacOS",
    "Operating System :: POSIX :: Linux",
    "Operating System :: Microsoft :: Windows",
    "Programming Language :: Python :: 3.13",
]
dependencies = [
    "pydantic>=1.10.16,<2",
    "loguru>=0.4.1,<0.5",
    "jmespath>=0.9.5,<0.10",
    "black>=22.3.0,<23",
    "pytest>=8.3.4,<9",
    "pytest-html>=3.1.1,<4",
    "Brotli>=1.1.0,<2",
    "jinja2>=3.0.3,<4",
    "toml>=0.10.2,<0.11",
    "pyyaml>=6.0.1,<7",
    "requests>=2.31.0,<3",
    "urllib3~=1.26",
    "aiobotocore[boto3]>=2.12.3,<3",
    "pytest-xdist>=3.6.1,<4",
    "cryptography>=42.0.7,<43",
    "prometheus-client>=0.21.1,<0.22",
    "setuptools==75.1.0",
]

[project.optional-dependencies]
allure = ["allure-pytest>=2.8.16,<3"]
upload = [
    "requests-toolbelt>=0.10.1,<0.11",
    "filetype>=1.0.7,<2",
]

[project.urls]
Homepage = "https://gitee.com/switchbot/switchbot-runner"
Repository = "https://gitee.com/switchbot/switchbot-runner"
Documentation = "https://gitee.com/switchbot/switchbot-runner/wikis"

[project.scripts]
switchbotrunner = "switchbotrunner.cli:main"
srun = "switchbotrunner.cli:main_run_alias"
smake = "switchbotrunner.cli:main_make_alias"

[tool.setuptools]
include-package-data = true
package-dir = {"switchbotrunner" = "switchbotrunner"}


[dependency-groups]
dev = [
    "pre-commit>=3.7.1,<4",
    "ruff>=0.5.0,<0.6",
]

[tool.uv]
package = true
default-groups = ["dev"]

[[tool.uv.index]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"

[tool.hatch.build.targets.sdist]
include = ["docs/CHANGELOG.md"]

[tool.hatch.build.targets.wheel]
include = ["docs/CHANGELOG.md"]

[tool.ruff]
target-version = "py312"
exclude = ["examples"]


[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "W191",  # indentation contains tabs
    "B904",  # Allow raising exceptions without from e, for HTTPException
]

[tool.ruff.format]
quote-style = "single"

[tool.ruff.lint.pyupgrade]
# Preserve types, even if a file imports `from __future__ import annotations`.
keep-runtime-typing = true
