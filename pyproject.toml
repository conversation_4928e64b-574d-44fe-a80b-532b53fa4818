[project]
name = "autoswitchbot"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
authors = [
    {name = "huyuwei", email = "<EMAIL>"},
]
requires-python = ">=3.12"
dependencies = [
    "aioredis>=2.0.1",
    "boto3>=1.36.6",
    "celery>=5.4.0",
    "channels>=4.2.0",
    "channels-redis>=4.2.1",
    "dj-rest-auth>=7.0.1",
    "django>=5.1.5",
    "django-allauth>=0.57.0,<0.58.0",
    "django-celery-beat>=2.7.0",
    "django-filter>=24.3",
    "djangorestframework>=3.15.2",
    "drf-spectacular>=0.28.0",
    "dynaconf>=3.2.7",
    "gitpython>=3.1.44",
    "paho-mqtt>=2.1.0",
    "prometheus-client>=0.21.1",
    "psycopg[binary,pool]>=3.2.4",
    "pydantic>=2.10.6",
    "pytest>=8.3.4",
    "redis>=5.2.1",
    "requests>=2.32.3",
    "tenacity>=9.0.0",
]

[dependency-groups]
dev = [
    "pre-commit>=4.1.0",
    "ruff >=0.7.4, <1.0.0",
]
prod = [
    "uvicorn[standard]>=0.34.0",
]

[tool.uv]
default-groups = ["dev"]

[[tool.uv.index]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"
