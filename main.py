import os
import sys
from typing import Any

import uvicorn

LOGGING_CONFIG: dict[str, Any] = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "()": "uvicorn.logging.DefaultFormatter",
            "fmt": "%(levelprefix)s %(message)s",
            "use_colors": None,
        },
        "access": {
            "()": "uvicorn.logging.AccessFormatter",
            "fmt": '%(levelprefix)s %(client_addr)s - "%(request_line)s" %(status_code)s',  # noqa: E501
        },
    },
    "handlers": {
        "default": {
            "formatter": "default",
            "class": "logging.StreamHandler",
            "stream": "ext://sys.stderr",
        },
        "access": {
            "formatter": "access",
            "class": "logging.StreamHandler",
            "stream": "ext://sys.stdout",
        },
        "file": {
            "formatter": "default",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "logs/access.log",
            "maxBytes": 10485760,  # 10 MB
            "backupCount": 5,
        },
    },
    "loggers": {
        "uvicorn": {
            "handlers": ["default", "file"],
            "level": "INFO",
            "propagate": False,
        },
        "uvicorn.error": {"level": "INFO"},
        "uvicorn.access": {
            "handlers": ["access", "file"],
            "level": "INFO",
            "propagate": False,
        },
    },
}

if __name__ == "__main__":
    debug_mode = "--debug" in sys.argv
    os.makedirs("logs", exist_ok=True)
    if not os.path.exists("logs/access.log"):
        open("logs/access.log", "a").close()
    workers = int(os.getenv("UVICORN_WORKERS", "2"))
    uvicorn.run(
        "autoswitchbot.asgi:application",
        host="0.0.0.0",
        port=8000,
        log_config=LOGGING_CONFIG,
        workers=workers,
        timeout_keep_alive=60,
        reload=debug_mode,
        use_colors=False,
    )
