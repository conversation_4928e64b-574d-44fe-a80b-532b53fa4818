#!/bin/bash
# 检查是否有传入tag，如果没有，则使用当前的年月日时分秒作为tag
if [ -z "$1" ]; then
  TAG=$(date +%Y.%m.%d.%H%M)
else
  TAG=$1
fi
echo "build image with tag: $TAG"

# aws ecr create-repository --repository-name switchbot-test/autoswitchbot-fe --image-tag-mutability IMMUTABLE --region ap-east-1 --profile prod

aws ecr get-login-password --region ap-east-1 --profile prod | docker login --username AWS --password-stdin 443283509441.dkr.ecr.ap-east-1.amazonaws.com

docker buildx create --name aws --use

docker buildx build --platform linux/arm64 -t 443283509441.dkr.ecr.ap-east-1.amazonaws.com/switchbot-test/autoswitchbot-fe:$TAG --push .

# docker manifest inspect 443283509441.dkr.ecr.ap-east-1.amazonaws.com/switchbot-test/autoswitchbot-fe:$TAG
