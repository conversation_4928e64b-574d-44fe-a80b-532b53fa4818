import json
from app.testcase.models import TestCaseStep


# test_case_steps = TestCaseStep.objects.filter(content__contains='/api/v1/user/register')
# for step in test_case_steps:
#     print(step.content)
#     content = step.content
#     content = content.replace('/api/v1/user/register', '/api/v2/user/register')
#     step.content=content
#     step.save()
#     print('Updated')


test_case_steps = TestCaseStep.objects.filter(content__contains='/account/api/v1/verify/code')
for step in test_case_steps:
    print(step.content)
    content = step.content
    content = content.replace('/account/api/v1/verify/code', '/account/api/v2/verification/code/send')
    step.content=content
    step.save()
    print('Updated')

test_case_steps = TestCaseStep.objects.filter(content__contains='${account_api}/api/v1/verify/code')
for step in test_case_steps:
    print(step.content)
    content = step.content
    content = content.replace('${account_api}/api/v1/verify/code', '${account_api}/api/v2/verification/code/send')
    step.content=content
    step.save()
    print('Updated')


test_case_steps = TestCaseStep.objects.filter(content__contains='/api/v1/verify/code')
assert len(test_case_steps) == 0

test_case_steps = TestCaseStep.objects.filter(content__contains='/account/api/v1/verify/code')
assert len(test_case_steps) == 0


# test_case_steps = TestCaseStep.objects.filter(content__contains='/api/v2/verification/code/send')
# for step in test_case_steps:
#     data = json.loads(step.content)
#     request = data['request']
#     if request.get('json'):
#         body = request['json']
#         if body.get('mfaToken'):
#             continue
#         if body=={'clientId': '${client_id}', 'method': 'email', 'lang': 'cn', 'ops': 'register'}:
#             print(body)
#             request['json'] = {'clientId': '${client_id}', 'method': 'email', 'lang': 'cn', 'ops': 'register', 'proof': {'dest': '${new_email}'},}
#             step.content = json.dumps(data)
#             step.save()
#             print('Updated')

test_case_steps = TestCaseStep.objects.filter(content__contains='/verify_code')
for step in test_case_steps:
    data = json.loads(step.content)
    request = data['request']
    if request.get('json'):
        body = request['json']
        if not body.get('ops'):
            print(body)
            body['ops'] = 'register'
            print(body)
            request['json'] = body
            step.content = json.dumps(data)
            step.save()
            print('Updated')