from datetime import timedelta

from django.db import connection, transaction
from django.utils import timezone

from app.testcase.models import TestReport

# 207877 | 7006 MB
months_ago = timezone.now() - timedelta(days=30)


def batch_delete(queryset, batch_size=1000):
    total = 0
    while True:
        # 分批获取并删除
        batch = list(queryset[:batch_size].values_list("id", flat=True))
        if not batch:
            break
        # 手动提交事务（关闭自动提交）
        with transaction.atomic():
            TestReport.objects.filter(id__in=batch).delete()
            total += len(batch)
            print(f"已删除 {total} 条记录")
    print("删除完成")


# Get date 1 months ago
months_ago = timezone.now() - timedelta(days=30)

# Count reports older than 6 months
old_reports = TestReport.objects.filter(created_at__lt=months_ago)

print(f"将删除 {old_reports.count()} 条旧报告")

batch_delete(old_reports)


def vacuum_table(model):
    table_name = model._meta.db_table
    with connection.cursor() as cursor:
        cursor.execute(f"VACUUM (VERBOSE, ANALYZE) {table_name};")
    print(f"表 {table_name} 空间已回收")


vacuum_table(TestReport)


def vacuum_full_table(model):
    table_name = model._meta.db_table
    with connection.cursor() as cursor:
        cursor.execute(f"VACUUM FULL {table_name};")
    print(f"表 {table_name} 已彻底释放空间")


vacuum_full_table(TestReport)
