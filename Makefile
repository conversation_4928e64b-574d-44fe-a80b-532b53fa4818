# make build TAG=
# 设置变量
# Set TAG to current timestamp if not provided
TAG ?= $(shell date '+%Y.%m.%d.%H%M')
REPOSITORY_NAME := switchbot-test/switchbot-runner
REGION := ap-east-1
PROFILE := prod
ACCOUNT_ID := ************

.PHONY: build
create-ecr-repo:
	@echo "Checking if the ECR repository '${REPOSITORY_NAME}' exists..."
	@{ \
		REPO_EXISTS=$$(aws ecr describe-repositories --repository-names "$(REPOSITORY_NAME)" --region "$(REGION)" --profile "$(PROFILE)" 2>&1); \
		STATUS=$$?; \
		if [ $$STATUS -ne 0 ]; then \
			if echo $$REPO_EXISTS | grep -q "RepositoryNotFoundException"; then \
				echo "Repository does not exist. Creating repository..."; \
				aws ecr create-repository \
					--repository-name "$(REPOSITORY_NAME)" \
					--image-tag-mutability IMMUTABLE \
					--region "$(REGION)" \
					--profile "$(PROFILE)"; \
			else \
				echo "An error occurred: $$REPO_EXISTS"; \
				exit 1; \
			fi; \
		else \
			echo "Repository '${REPOSITORY_NAME}' already exists. Skipping creation."; \
		fi; \
	}

build: create-ecr-repo
	echo "$(TAG)"
	aws ecr get-login-password --region "$(REGION)" --profile "$(PROFILE)" | docker login --username AWS --password-stdin "$(ACCOUNT_ID).dkr.ecr.$(REGION).amazonaws.com" && \
	docker buildx build --platform linux/arm64 -t "$(ACCOUNT_ID).dkr.ecr.$(REGION).amazonaws.com/$(REPOSITORY_NAME):$(TAG)" --provenance=false --push .
