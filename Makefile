# .PHONY: dev build up down logs destory migrate prod cache web
.PHONY: build build-pg

# Default target
all: build

dev:
	python3 manage.py runserver

local:
	docker-compose -f docker-compose.yml up -d --build --remove-orphans

up:
	# docker network create autoswitchbot_network
	docker-compose -f docker-compose.yml up -d

down:
	docker-compose -f docker-compose.yml down

logs:
	docker-compose -f docker-compose.yml logs --tail=100 -f

down-v:
	docker-compose -f docker-compose.yml down -v

migrate:
	docker exec -it autoswitchbot-django python3 manage.py migrate

cache:
	docker exec -it autoswitchbot-django python3 manage.py invalidate_cachalot

# make build TAG=
# make build-pg TAG=
# 设置变量
# Set TAG to current timestamp if not provided
TAG ?= $(shell date '+%Y.%m.%d.%H%M')
# Service repository variables
SERVICE_REPO := switchbot-test/autoswitchbot
# PG repository variables
PG_REPO := switchbot-test/pg
REGION := ap-east-1
PROFILE := prod
ACCOUNT_ID := ************

create-ecr-repo:
	@echo "Checking if the ECR repository '${REPOSITORY_NAME}' exists..."
	@{ \
		REPO_EXISTS=$$(aws ecr describe-repositories --repository-names "$(REPOSITORY_NAME)" --region "$(REGION)" --profile "$(PROFILE)" 2>&1); \
		STATUS=$$?; \
		if [ $$STATUS -ne 0 ]; then \
			if echo $$REPO_EXISTS | grep -q "RepositoryNotFoundException"; then \
				echo "Repository does not exist. Creating repository..."; \
				aws ecr create-repository \
					--repository-name "$(REPOSITORY_NAME)" \
					--image-tag-mutability IMMUTABLE \
					--region "$(REGION)" \
					--profile "$(PROFILE)"; \
			else \
				echo "An error occurred: $$REPO_EXISTS"; \
				exit 1; \
			fi; \
		else \
			echo "Repository '${REPOSITORY_NAME}' already exists. Skipping creation."; \
		fi; \
	}

# Build service image
build:
	@echo "Building autoswitchbot service image with tag: $(TAG)"
	$(eval REPOSITORY_NAME := $(SERVICE_REPO))
	@$(MAKE) create-ecr-repo REPOSITORY_NAME=$(SERVICE_REPO)
	aws ecr get-login-password --region "$(REGION)" --profile "$(PROFILE)" | docker login --username AWS --password-stdin "$(ACCOUNT_ID).dkr.ecr.$(REGION).amazonaws.com" && \
	docker buildx build --platform linux/arm64 -t "$(ACCOUNT_ID).dkr.ecr.$(REGION).amazonaws.com/$(SERVICE_REPO):$(TAG)" --provenance=false --push -f Dockerfile .

# Build PostgreSQL image
build-pg:
	@echo "Building PostgreSQL image with tag: $(TAG)"
	$(eval REPOSITORY_NAME := $(PG_REPO))
	@$(MAKE) create-ecr-repo REPOSITORY_NAME=$(PG_REPO)
	aws ecr get-login-password --region "$(REGION)" --profile "$(PROFILE)" | docker login --username AWS --password-stdin "$(ACCOUNT_ID).dkr.ecr.$(REGION).amazonaws.com" && \
	docker buildx build --platform linux/arm64 -t "$(ACCOUNT_ID).dkr.ecr.$(REGION).amazonaws.com/$(PG_REPO):$(TAG)" --provenance=false --push -f pg/Dockerfile .
