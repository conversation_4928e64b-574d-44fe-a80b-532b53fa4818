import logging

import django_filters
from django_filters import rest_framework as filters

from app.configuration.models import Tag

from .models import TestCase, TestCaseDirectory, TestReport

logger = logging.getLogger(__name__)


def get_directory_keys(directory):
    directories = [directory.key]
    children = TestCaseDirectory.objects.filter(parent=directory)
    for child in children:
        directories.extend(get_directory_keys(child))
    return directories


class TestCaseFilter(django_filters.FilterSet):
    directory = filters.CharFilter(method="filter_directory")
    tags = filters.CharFilter(method="filter_tags")

    class Meta:
        model = TestCase
        fields = ["name", "project", "priority", "directory", "tags"]

    def filter_directory(self, queryset, name, value):
        try:
            directory = TestCaseDirectory.objects.get(key=value)
            directory_ids = [key for key in get_directory_keys(directory)]
            return queryset.filter(directory__key__in=directory_ids)
        except TestCaseDirectory.DoesNotExist:
            return queryset.none()

    def filter_tags(self, queryset, name, value):
        try:
            tag = Tag.objects.get(name=value)
            return queryset.filter(tags=tag)
        except Tag.DoesNotExist:
            return queryset.none()


class TestReportFilter(django_filters.FilterSet):
    class Meta:
        model = TestReport
        fields = {
            "name": ["exact"],
            "env": ["exact", "icontains"],
        }
