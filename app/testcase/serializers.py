import json
import logging
import time
from functools import reduce
from operator import or_
from typing import List

import yaml
from django.core.files.uploadedfile import UploadedFile
from django.db import IntegrityError, transaction
from django.db.models import Q
from rest_framework import serializers

from app.configuration.models import Environment, EnvironmentVariable, Variable
from app.project.models import Project
from app.serializers import BaseModelSerializer, CustomListSerializer
from app.testcase.tasks import run_test_cases

from .models import (
    RunType,
    TestCase,
    TestCaseDirectory,
    TestCaseStep,
    TestCaseStepMapping,
    TestPlan,
    TestPlanTestCase,
    TestReport,
)

logger = logging.getLogger(__name__)


class TestCaseDirectoryUpdateSerializer(BaseModelSerializer):
    # create or update
    project_id = serializers.CharField()
    key = serializers.CharField(required=False, allow_null=True)
    parent_id = serializers.CharField(required=False, allow_null=True)

    class Meta:
        model = TestCaseDirectory
        fields = ("project_id", "title", "key", "parent_id")


class TestCaseDirectorySerializer(BaseModelSerializer):
    project = serializers.CharField(write_only=True)
    key = serializers.Char<PERSON>ield()
    children = serializers.SerializerMethodField()

    class Meta:
        model = TestCaseDirectory
        fields = ("project", "key", "title", "children")

    def get_children(self, obj):
        # 递归获取子目录
        children = obj.children.all()
        return TestCaseDirectorySerializer(children, many=True).data


class TestCaseSerializer(BaseModelSerializer):
    class Meta:
        model = TestCase
        exclude = ("is_deleted",)
        list_serializer_class = CustomListSerializer


class TestCaseUploadSerializer(serializers.Serializer):
    file = serializers.ListField(
        child=serializers.FileField(allow_empty_file=False), allow_empty=False
    )
    project_id = serializers.CharField()
    directory = serializers.CharField()
    env = serializers.PrimaryKeyRelatedField(queryset=Environment.objects.all())

    def create(self, validated_data):
        logger.info(validated_data)
        files: List[UploadedFile] = validated_data["file"]
        project_id = validated_data["project_id"]
        directory_id = validated_data.get("directory")
        env = validated_data.get("env")
        request = self.context["request"]
        test_cases = []
        for file in files:
            # 解析文件内容并保存到模型
            byte_content: bytes = file.read()
            string_content = byte_content.decode("utf-8")
            try:
                data = yaml.safe_load(string_content)
                # logger.info(json.dumps(data, indent=2, ensure_ascii=False))
                if not data:
                    logger.warn(f"yaml文件内容为空，跳过")
                    continue
                logger.info(data)
                case_name = data["config"]["name"]
                variables = data["config"].get("variables", {})
                for variable_name, variable_value in variables.items():
                    variable, _ = Variable.objects.get_or_create(key=variable_name)
                    EnvironmentVariable.objects.get_or_create(
                        env=env, variable=variable, value=variable_value
                    )
                # 新增用例
                test_case = TestCaseCreateSerializer(
                    data={
                        "name": case_name,
                        "desc": "",
                        "project_id": project_id,
                        "directory_id": directory_id,
                    },
                    context={"request": request},
                )
                if not test_case.is_valid():
                    raise serializers.ValidationError(test_case.errors)
                try:
                    test_case_instance = test_case.save()
                    test_cases.append(test_case_instance)
                except IntegrityError:
                    logger.warn(f"测试用例{case_name}已存在，跳过")
                    continue
                # 遍历测试步骤并创建测试步骤实例和映射
                for index, step in enumerate(data["teststeps"]):
                    # 创建测试步骤实例
                    name = step.pop("name")
                    # logger.info(f"{index} {name}\n{step}")
                    test_step = TestCaseStepSerializer(
                        data={
                            "name": name,
                            "content": json.dumps(step, ensure_ascii=False),
                        },
                        context={"request": request},
                    )
                    # test_step_instance = TestCaseStep.objects.create(
                    #     name=name, content=json.dumps(step, ensure_ascii=False)
                    # )
                    if not test_step.is_valid():
                        raise serializers.ValidationError(test_step.errors)
                    test_step_instance = test_step.save()

                    # 创建测试用例步骤映射实例
                    TestCaseStepMapping.objects.create(
                        test_case=test_case_instance,
                        step=test_step_instance,
                        step_seq=index,
                    )
                logger.info("测试用例和步骤已成功保存到数据库。")
            except yaml.YAMLError as exc:
                logger.error(exc)
        return test_cases

    def update(self, instance, validated_data):
        logger.info(validated_data)
        files: List[UploadedFile] = validated_data["file"]
        project_id = validated_data["project_id"]
        directory_id = validated_data.get("directory")
        env = validated_data.get("env")
        # user = self.context["request"].user
        for file in files:
            # 解析文件内容并保存到模型
            byte_content: bytes = file.read()
            string_content = byte_content.decode("utf-8")
            try:
                data = yaml.safe_load(string_content)
                # logger.info(json.dumps(data, indent=2, ensure_ascii=False))
                if not data:
                    logger.warn(f"yaml文件内容为空，跳过")
                    continue
                logger.info(data)
                case_name = data["config"]["name"]
                variables = data["config"].get("variables", {})
                for variable_name, variable_value in variables.items():
                    variable, _ = Variable.objects.get_or_create(key=variable_name)
                    EnvironmentVariable.objects.get_or_create(
                        env=env, variable=variable, value=variable_value
                    )
                # 更新用例
                # "project", "name" 同个项目只有一个同名用例
                try:
                    test_case_instance = TestCase.objects.get(
                        name=case_name, project_id=project_id
                    )
                except TestCase.DoesNotExist:
                    test_case_instance = TestCase.objects.create(
                        name=case_name,
                        project_id=project_id,
                        directory_id=directory_id,
                    )
                    logger.warn(f"测试用例{case_name}不存在，新增后再更新")
                # 遍历测试步骤并创建测试步骤实例和映射
                TestCaseStepMapping.update_test_case(
                    test_case_instance, data["teststeps"]
                )

                logger.info("测试用例和步骤已成功更新到数据库。")
            except yaml.YAMLError as exc:
                logger.error(exc)
        return instance


class TestCaseStepSerializer(BaseModelSerializer):
    class Meta:
        model = TestCaseStep
        fields = "__all__"


class TestCaseCreateSerializer(BaseModelSerializer):
    name = serializers.CharField()
    desc = serializers.CharField(allow_blank=True)
    project_id = serializers.CharField()
    # directory_id = serializers.CharField(required=False, allow_null=True)
    directory_id = serializers.CharField()
    # teststeps = TestCaseStepSerializer(many=True)

    class Meta:
        model = TestCase
        fields = ("name", "desc", "project_id", "directory_id")


class RunTestCasesSerializer(serializers.Serializer):
    id = serializers.ListField(
        child=serializers.IntegerField(), allow_empty=False, write_only=True
    )
    env = serializers.PrimaryKeyRelatedField(queryset=Environment.objects.all())
    is_debug = serializers.BooleanField(required=False)  # default is False

    def create(self, validated_data):
        test_case_ids = validated_data["id"]
        env_id = validated_data["env"]
        # 查询运行环境，所有的变量名和值
        env_name = f"{env_id.key}({env_id.region})"  # env object
        env_variables_dict = EnvironmentVariable.get_env_variables(env_id)
        # 所有要运行的测试用例
        test_cases = TestCase.objects.filter(id__in=test_case_ids).values("id", "name")
        # 组装 JSON 数据
        test_cases_json = TestCase.get_test_case_jsons(test_cases, env_variables_dict)
        timestamp = time.strftime("%Y%m%d%H%M%S", time.localtime())
        if len(test_cases) < 1:
            report_name = f"没有测试用例-{timestamp}"
        elif len(test_cases) > 1:
            report_name = f"批量用例-{timestamp}"
        else:
            report_name = test_cases[0]["name"]
        result = run_test_cases.delay(
            test_cases_json,
            env_name=env_name,
            report_name=report_name,
            run_type=(
                RunType.DEBUG if validated_data.get("is_debug", False) else RunType.CASE
            ),
            is_debug=validated_data.get("is_debug", False),
        )
        return result


class RunTestCasesByProjectSerializer(serializers.Serializer):
    project = serializers.PrimaryKeyRelatedField(queryset=Project.objects.all())
    env = serializers.PrimaryKeyRelatedField(queryset=Environment.objects.all())

    def create(self, validated_data):
        project_id = validated_data["project"]
        env_id = validated_data["env"]
        # 查询运行环境，所有的变量名和值
        project_name = project_id.name  # project object
        env_name = f"{env_id.key}({env_id.region})"  # env object
        env_variables_dict = EnvironmentVariable.get_env_variables(env_id)
        # 所有要运行的测试用例
        test_cases = TestCase.objects.filter(project_id=project_id).values("id", "name")
        # 组装 JSON 数据
        test_cases_json = TestCase.get_test_case_jsons(test_cases, env_variables_dict)
        result = run_test_cases.delay(
            test_cases_json,
            env_name=env_name,
            report_name=f"{project_name}-项目全量用例",
            run_type=RunType.PROJECT,
        )
        return result


class RunTestCasesByDirSerializer(serializers.Serializer):
    dir = serializers.PrimaryKeyRelatedField(queryset=TestCaseDirectory.objects.all())
    env = serializers.PrimaryKeyRelatedField(queryset=Environment.objects.all())

    def create(self, validated_data):
        tc_dir_id = validated_data["dir"]
        env_id = validated_data["env"]
        # 查询运行环境，所有的变量名和值
        dir_name = tc_dir_id.name  # project object
        env_name = f"{env_id.key}({env_id.region})"  # env object
        env_variables_dict = EnvironmentVariable.get_env_variables(env_id)
        # 所有要运行的测试用例
        test_cases = TestCase.objects.filter(directory_id=tc_dir_id).values(
            "id", "name"
        )
        # 组装 JSON 数据
        test_cases_json = TestCase.get_test_case_jsons(test_cases, env_variables_dict)
        result = run_test_cases.delay(
            test_cases_json,
            env_name=env_name,
            report_name=f"{dir_name}-目录全量用例",
            run_type=RunType.DIRECTORY,
        )
        return result


class TestReportSerializer(BaseModelSerializer):
    class Meta:
        model = TestReport
        exclude = ("summary",)
        list_serializer_class = CustomListSerializer


class TestReportDetailSerializer(BaseModelSerializer):
    class Meta:
        model = TestReport
        fields = ("id", "env", "summary")


class TestCaseDataSerializer(serializers.Serializer):
    data = serializers.CharField(write_only=True)

    def to_representation(self, instance):
        return TestCase.get_edit_test_case_json(instance)

    def update(self, instance, validated_data):
        case_data = yaml.safe_load(validated_data["data"])
        TestCaseStepMapping.update_test_case(
            instance, teststeps=case_data.get("teststeps", [])
        )
        return instance


class TestCaseExportSerializer(serializers.Serializer):
    id = serializers.ListField(
        child=serializers.IntegerField(), allow_empty=False, write_only=True
    )

    def create(self, validated_data):
        test_case_ids = validated_data["id"]
        # 所有要导出的测试用例
        test_cases = TestCase.objects.filter(id__in=test_case_ids).values("id", "name")
        # 组装 JSON 数据
        return TestCase.get_test_case_jsons(test_cases, None, None)


class DevopsRunSerializer(serializers.Serializer):
    env = serializers.CharField(required=True)  # env key
    region = serializers.CharField(required=True)  # env region
    label = serializers.ListField(
        child=serializers.CharField(), allow_empty=False, write_only=True
    )

    def create(self, validated_data):
        try:
            env = Environment.objects.get(
                key=validated_data["env"], region=validated_data["region"]
            )

            env_name = f"{env.key}({env.region})"  # env object
            env_variables_dict = EnvironmentVariable.get_env_variables(env)
            # 所有要运行的测试用例
            query = reduce(or_, (Q(tags__name=tag) for tag in validated_data["label"]))
            # 这里需要去重
            test_cases = TestCase.objects.filter(query).distinct().values("id", "name")
            # 组装 JSON 数据
            test_cases_json = TestCase.get_test_case_jsons(
                test_cases, env_variables_dict
            )
            timestamp = time.strftime("%Y%m%d%H%M%S", time.localtime())
            result = run_test_cases.delay(
                test_cases_json,
                env_name=env_name,
                report_name=f"冒烟测试用例-{timestamp}",
                run_type=RunType.PLAN,
            )
            return result
        except Environment.DoesNotExist:
            raise serializers.ValidationError(
                {"env": f"{validated_data['env']}({validated_data['region']})不存在"}
            )


class TestPlanSerializer(BaseModelSerializer):
    test_case_ids = serializers.ListField(child=serializers.CharField(), required=False)

    class Meta:
        model = TestPlan
        exclude = ("is_deleted",)
        list_serializer_class = CustomListSerializer

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        test_case_ids = list(
            TestPlanTestCase.objects.filter(test_plan_id=instance.id).values_list(
                "test_case_id", flat=True
            )
        )
        representation["test_case_ids"] = [str(case_id) for case_id in test_case_ids]
        return representation

    def create(self, validated_data):
        test_case_ids = validated_data.pop("test_case_ids", [])
        with transaction.atomic():
            test_plan = TestPlan.objects.create(**validated_data)
            self.update_test_cases(test_plan, test_case_ids)
        return test_plan

    def update(self, instance, validated_data):
        test_case_ids = validated_data.pop("test_case_ids", [])
        instance.name = validated_data.get("name", instance.name)
        instance.notify_enable = validated_data.get(
            "notify_enable", instance.notify_enable
        )
        instance.save()
        with transaction.atomic():
            self.update_test_cases(instance, test_case_ids)
        return instance

    def update_test_cases(self, test_plan: TestPlan, test_case_ids):
        # 批量插入新的TestPlanTestCase实例，忽略插入错误
        TestPlanTestCase.objects.bulk_create(
            [
                TestPlanTestCase(test_plan_id=test_plan.pk, test_case_id=tc_id)
                for tc_id in test_case_ids
            ],
            ignore_conflicts=True,  # 忽略因唯一性约束导致的插入冲突
        )
        # 删除不在test_case_ids列表中的TestPlanTestCase实例
        TestPlanTestCase.objects.filter(test_plan_id=test_plan.pk).exclude(
            test_case_id__in=test_case_ids
        ).delete()


class RunTestPlanSerializer(serializers.Serializer):
    id = serializers.ListField(
        child=serializers.IntegerField(), allow_empty=False, write_only=True
    )
    env = serializers.PrimaryKeyRelatedField(queryset=Environment.objects.all())
    is_debug = serializers.BooleanField(required=False)  # default is False

    def get_plan_list(self, test_plan_ids: List[str | int]):
        test_plans = TestPlan.objects.filter(id__in=test_plan_ids).values(
            "id", "name", "notify_enable"
        )
        test_plan_test_cases = TestPlanTestCase.objects.filter(
            test_plan_id__in=test_plan_ids
        ).values("test_plan_id", "test_case_id")
        test_case_ids_map = {}
        for item in test_plan_test_cases:
            test_plan_id = item["test_plan_id"]
            if test_plan_id not in test_case_ids_map:
                test_case_ids_map[test_plan_id] = []
            test_case_ids_map[test_plan_id].append(item["test_case_id"])
        # 组合数据
        result = []
        for plan in test_plans:
            plan_id = plan["id"]
            plan_name = plan["name"]
            plan_notify_enable = plan["notify_enable"]
            test_case_ids = test_case_ids_map.get(plan_id, [])
            result.append(
                {
                    "test_plan_id": plan_id,
                    "name": plan_name,
                    "notify_enable": plan_notify_enable,
                    "test_case_ids": test_case_ids,
                }
            )
        return result

    def create(self, validated_data):
        test_plan_ids = validated_data["id"]
        env_id = validated_data["env"]
        # 查询运行环境，所有的变量名和值
        plan_list = self.get_plan_list(test_plan_ids)
        env_name = f"{env_id.key}({env_id.region})"  # env object
        env_variables_dict = EnvironmentVariable.get_env_variables(env_id)
        for test_plan in plan_list:
            # 所有要运行的测试用例
            test_cases = TestCase.objects.filter(
                id__in=test_plan["test_case_ids"]
            ).values("id", "name")
            # 组装 JSON 数据
            test_cases_json = TestCase.get_test_case_jsons(
                test_cases, env_variables_dict
            )
            result = run_test_cases.delay(
                test_cases_json,
                env_name=env_name,
                report_name=test_plan["name"],
                run_type=(
                    RunType.DEBUG
                    if validated_data.get("is_debug", False)
                    else RunType.PLAN
                ),
                is_debug=validated_data.get("is_debug", False),
                notify=test_plan.get("notify_enable", False),
            )
            # 暂时支持单个测试计划运行
            return result


class RunTestCasesByPlanSerializer(serializers.Serializer):
    plan = serializers.PrimaryKeyRelatedField(queryset=TestPlan.objects.all())
    env = serializers.PrimaryKeyRelatedField(queryset=Environment.objects.all())

    def create(self, validated_data):
        plan = validated_data["plan"]
        env = validated_data["env"]
        # 查询运行环境，所有的变量名和值
        plan_name = plan.name  # plan object
        notify_enable = plan.notify_enable  # plan object
        env_name = f"{env.key}({env.region})"  # env object
        env_variables_dict = EnvironmentVariable.get_env_variables(env)
        test_plan_test_case_list = TestPlanTestCase.objects.filter(
            test_plan_id=plan.id
        ).values_list("test_case_id", flat=True)
        # 所有要运行的测试用例
        test_cases = TestCase.objects.filter(id__in=test_plan_test_case_list).values(
            "id", "name"
        )
        # 组装 JSON 数据
        test_cases_json = TestCase.get_test_case_jsons(test_cases, env_variables_dict)
        result = run_test_cases.delay(
            test_cases_json,
            env_name=env_name,
            report_name=plan_name,
            run_type=RunType.PLAN,
            notify=notify_enable,
        )
        return result
