import json
import logging
from enum import Enum
from typing import Any, Dict, List

from django.db import models
from django.db.models import Max
from django.utils import timezone

from app.configuration.models import Tag
from app.models import BaseModel
from app.project.models import Project

logger = logging.getLogger(__name__)
# Create your models here.


class RunType(str, Enum):
    PLAN = "plan"
    PROJECT = "project"
    DIRECTORY = "dir"
    CASE = "case"
    DEBUG = "debug"


class TestCaseDirectory(BaseModel):
    """测试用例目录"""

    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    key = models.BigAutoField(primary_key=True)
    title = models.CharField(max_length=255)
    parent = models.ForeignKey(
        "self", on_delete=models.CASCADE, null=True, blank=True, related_name="children"
    )

    def __str__(self):
        return self.title

    class Meta:
        db_table = "test_case_directory"
        unique_together = ("parent", "title")


class TestCase(BaseModel):
    """测试用例"""

    PRIORITY_CHOICES = [
        ("P0", "P0 - Critical"),
        ("P1", "P1 - High"),
        ("P2", "P2 - Medium"),
        ("P3", "P3 - Low"),
    ]
    name = models.CharField(max_length=255)
    desc = models.TextField(blank=True)
    project = models.ForeignKey(
        Project, on_delete=models.PROTECT
    )  # 删项目时候，如果存在测试用例，则不允许删除
    directory = models.ForeignKey(
        TestCaseDirectory, on_delete=models.PROTECT, null=True
    )
    priority = models.CharField(
        max_length=2,
        choices=PRIORITY_CHOICES,
        default="P3",
    )
    tags = models.ManyToManyField(Tag, blank=True)  # 添加标签字段

    class Meta:
        db_table = "test_case"
        # unique_together = ("project", "name", "is_deleted")
        constraints = [
            models.UniqueConstraint(
                fields=["name", "project"],
                condition=models.Q(
                    is_deleted=False
                ),  # TODO: 用例删除了，再上传同名的提示已存在
                name="unique_active_testcases",
            )
        ]

    @staticmethod
    def get_test_case_jsons(
        test_cases,
        env_variables_dict: Dict[str, Any],
    ) -> List[Dict[str, Any]]:
        test_cases_jsons = []
        for test_case in test_cases:
            test_case_steps_json = TestCaseStepMapping.get_test_case_steps_json(
                test_case["id"]
            )
            logger.info(test_case["name"])
            # 这里需要根据你的模型结构来组装 JSON
            test_cases_json = {
                "config": {
                    "name": test_case["name"],
                    "variables": env_variables_dict,
                },
                "teststeps": test_case_steps_json,
            }
            test_cases_jsons.append(test_cases_json)
            logger.info(test_cases_json)
        return test_cases_jsons

    @staticmethod
    def get_edit_test_case_json(test_case: "TestCase") -> Dict[str, Any]:
        test_case_steps_json = TestCaseStepMapping.get_test_case_steps_json(
            test_case.id
        )
        return {
            "config": {
                "name": test_case.name,
            },
            "teststeps": test_case_steps_json,
        }


class TestCaseStep(BaseModel):
    """测试用例步骤"""

    name = models.CharField(max_length=255)
    content = models.TextField()

    class Meta:
        db_table = "test_case_step"


class TestCaseStepMapping(BaseModel):
    """测试用例步骤映射"""

    test_case = models.ForeignKey(TestCase, on_delete=models.CASCADE)
    step = models.ForeignKey(
        TestCaseStep, on_delete=models.PROTECT
    )  # 删步骤时候，如果存在测试用例引用，则不允许删除
    step_seq = models.IntegerField(default=0)
    version = models.IntegerField(default=0)

    class Meta:
        db_table = "test_case_step_mapping"

    @staticmethod
    def get_test_case_steps_json(test_case_id: int) -> List[Dict[str, str]]:
        test_case_steps = (
            TestCaseStepMapping.objects.filter(test_case_id=test_case_id)
            .prefetch_related("step")
            .order_by("step_seq")
        )
        test_case_steps_json = [
            json.loads(test_case_step.step.content)
            for test_case_step in test_case_steps
        ]
        fmt_test_case_steps_json = []
        for index, content in enumerate(test_case_steps_json):
            # 保证 name 展示在 json 第一个字段
            fmt_test_case_steps_json.append(
                {"name": test_case_steps[index].step.name, **content}
            )
        return fmt_test_case_steps_json

    @staticmethod
    def update_test_case(test_case: TestCase, teststeps: List[Dict[str, str]]):
        # 查询当前数据，对比，有变动才更新
        test_case_steps_json = TestCaseStepMapping.get_test_case_steps_json(
            test_case.pk
        )
        logger.info(test_case_steps_json != teststeps)
        if test_case_steps_json != teststeps:
            # 获取当前最大版本号
            current_max_version = (
                TestCaseStepMapping.objects.filter(test_case=test_case).aggregate(
                    Max("version")
                )["version__max"]
                or 0
            )

            # 标记旧版本的步骤为已删除
            TestCaseStepMapping.objects.filter(
                test_case=test_case, version=current_max_version
            ).update(is_deleted=True)
            # 为新步骤创建新的映射记录
            new_version = current_max_version + 1
            for index, step in enumerate(teststeps):
                # 创建测试步骤实例
                name = step.pop("name")
                # logger.info(f"{index} {name}\n{step}")
                test_step_instance = TestCaseStep.objects.create(
                    name=name, content=json.dumps(step, ensure_ascii=False)
                )

                # 创建测试用例步骤映射实例
                TestCaseStepMapping.objects.create(
                    test_case=test_case,
                    step=test_step_instance,
                    step_seq=index,
                    version=new_version,
                )
            test_case.updated_at = timezone.now()
            test_case.save()


class TestResult(models.IntegerChoices):
    """测试结果状态"""

    PASS = 0, "Pass"
    FAIL = 1, "Fail"
    INTERRUPTED = 2, "Interrupted"
    INTERNAL_ERROR = 3, "Internal Error"
    USAGE_ERROR = 4, "Usage Error"
    NO_TESTS_COLLECTED = 5, "No Test"


class TestReport(BaseModel):
    """测试报告"""

    id = models.CharField(max_length=64, primary_key=True)
    name = models.CharField(max_length=512, default="")
    env = models.CharField(max_length=255)
    result = models.IntegerField(choices=TestResult.choices, null=True)
    summary = models.TextField(default="{}")

    class Meta:
        db_table = "test_report"


class TestPlan(BaseModel):
    """测试计划"""

    name = models.CharField(max_length=128)
    desc = models.TextField(blank=True)
    notify_enable = models.BooleanField(default=False)

    class Meta:
        db_table = "test_plan"

    def __str__(self) -> str:
        return self.name

    def delete(self, *args, **kwargs):
        # 删除测试计划时，删除所有关联的 TestPlanTestCase
        TestPlanTestCase.objects.filter(test_plan_id=self.pk).delete()
        super().delete(*args, **kwargs)


class TestPlanTestCase(models.Model):
    """测试计划用例列表"""

    test_plan_id = models.BigIntegerField()
    test_case_id = models.BigIntegerField()

    class Meta:
        db_table = "test_plan_cases"
        unique_together = (
            "test_plan_id",
            "test_case_id",
        )
