import logging

from celery.result import AsyncResult

from app.testcase.models import TestReport, TestResult

logger = logging.getLogger(__name__)


class Task:

    def __init__(self, task_id):
        self.task_id = task_id
        self.task = AsyncResult(task_id)

    def get_status(self):
        return self.task.state


class TestRunTask(Task):

    def get_test_result(self):
        results = TestReport.objects.filter(id=self.task_id).values("result")
        if results:
            result = results[0]["result"]
            if result == TestResult.PASS:
                return "PASS"
            elif result == TestResult.NO_TESTS_COLLECTED:
                return "NO_TESTS"
            else:
                return "FAIL"
        else:
            return None
