import json
import logging
import os
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, TypedDict, cast

from celery import shared_task
from celery._state import get_current_task
from django.conf import settings
from django.db import close_old_connections
from pytest import ExitCode
from redis import Redis

from app.testcase.models import RunType, TestReport
from app.util.db import batch_delete
from app.util.notify import notify_test_result
from autoswitchbot import celery_app
from autoswitchbot.metrics import (
    test_case_abnormal,
    test_case_failed,
    test_case_runtime,
    test_case_succeeded,
)

logger = logging.getLogger(__name__)

TC_QUEUE = "testcase_queue"
LOG_TTL = 30 * 60  # 30 min
BASE_PATH = Path("tmp").absolute()  # 项目目录下的 tmp 目录


class TestRunError(Exception):
    """Test run specific exception"""

    pass


def get_task_path(task_id: str) -> Path:
    """Return absolute path for task directory"""
    path = BASE_PATH / task_id
    path.mkdir(parents=True, exist_ok=True)
    return path


def get_task_id() -> str:
    """Get current task ID or raise error"""
    current_task = get_current_task()
    if current_task is None or current_task.request.id is None:
        raise TestRunError("No valid task ID available")
    return current_task.request.id


def before_run_test_case(
    task_id: str, env_name: str, report_name: str, is_debug: bool = False
) -> None:
    """Setup before test case run"""
    if not is_debug:
        TestReport.objects.create(id=task_id, env=env_name, name=report_name)


def after_run_test_case(
    task_id: str,
    ret_code: int,
    env_name: str,
    report_name: str,
    run_type: str,
    start_time: Optional[float] = None,
    is_debug: bool = False,
    notify: bool = False,
) -> None:
    """Cleanup after test case run"""
    if is_debug:
        return

    close_old_connections()

    # Update test report with results
    test_report = TestReport.objects.filter(id=task_id)
    test_report.update(result=ret_code)

    if start_time:
        runtime = time.monotonic() - start_time
        test_case_runtime.labels(
            env_name=env_name,
            run_type=run_type,
            report_name=report_name,
        ).observe(runtime)

        if ret_code == ExitCode.OK:
            test_case_succeeded.labels(
                env_name=env_name,
                run_type=run_type,
                report_name=report_name,
            ).inc()
        elif ret_code == ExitCode.TESTS_FAILED:
            test_case_failed.labels(
                env_name=env_name,
                run_type=run_type,
                report_name=report_name,
            ).inc()
        else:
            test_case_abnormal.labels(
                env_name=env_name,
                run_type=run_type,
                report_name=report_name,
            ).inc()

    # Process summary file if exists
    summary_file = get_task_path(task_id) / "logs/all.summary.json"
    if summary_file.exists():
        with summary_file.open() as f:
            summary = f.read()
            test_report.update(summary=summary)
            params: TestReportNotifyParams = {
                "task_id": task_id,
                "env_name": env_name,
                "report_name": report_name,
                "summary": summary,
                "notify": notify,
            }
            test_report_notify(**params)


def setup_virtual_env(venv_path: str) -> Dict[str, str]:
    """Setup virtual environment path"""
    env = os.environ.copy()
    bin_path = Path(venv_path) / "bin"
    env["PATH"] = f"{bin_path}:{env['PATH']}"
    return env


@celery_app.task(queue=TC_QUEUE)
def run_test_cases(
    test_cases_json: List[Dict],
    env_name: str,
    report_name: str,
    run_type: RunType,
    task_id: Optional[str] = None,
    is_debug: bool = False,
    notify: bool = False,
) -> None:
    """Execute test cases and handle results

    Args:
        test_cases_json: List of test case configurations
        env_name: Environment name
        report_name: Report name
        run_type: Type of test run (plan/project/directory)
        task_id: Optional task identifier
        is_debug: Debug mode flag
        notify: Notification flag
    """
    task_id = task_id or get_task_id()
    task_path = get_task_path(task_id)

    before_run_test_case(task_id, env_name, report_name, is_debug)
    start_time = time.monotonic()

    # Write test case files
    for test_case in test_cases_json:
        case_name = test_case.get("config", {}).get("name", "").replace("/", "_")
        with (task_path / f"{case_name}.json").open("w") as f:
            json.dump(test_case, f, ensure_ascii=False)

    # Setup command
    venv_path = "/data/runner_venv"
    srun_cmd = (
        f"{venv_path}/bin/srun "
        # f"--pushgateway {settings.PUSH_GATEWAY_URL} "
        f"--save-tests -s "
        f"--config-file={BASE_PATH / 'pytest.toml'} "
        f"{task_path}"
    )
    logger.info(srun_cmd)

    # Setup Redis queue
    redis = cast(Redis, Redis.from_url(settings.CELERY_BROKER_URL))
    queue_name = f"testcase-{task_id}"

    try:
        # Execute test process
        from subprocess import PIPE, STDOUT, Popen

        process = Popen(
            srun_cmd,
            shell=True,
            stdout=PIPE,
            stderr=STDOUT,
            cwd=task_path,
            env=setup_virtual_env(venv_path),
            text=True,
        )

        # Stream output to Redis
        if process.stdout:
            for line in process.stdout:
                line = line.strip()
                logger.info(line)
                redis.rpush(queue_name, line)
            process.stdout.close()
            # Set TTL after the key is created
            redis.expire(queue_name, LOG_TTL)

        ret_code = process.wait()
        logger.info(f"Task {task_id} completed with code {ret_code}")

        after_run_test_case(
            task_id,
            ret_code,
            env_name,
            report_name,
            run_type,
            start_time=start_time,
            is_debug=is_debug,
            notify=notify,
        )

    except Exception as e:
        raise TestRunError(f"Test execution failed: {e}") from e


class ProjectRunParams(TypedDict):
    """Project run parameters"""

    project: str  # project_id
    env: str  # env_id


class DirectoryRunParams(TypedDict):
    """Directory run parameters"""

    dir: str  # directory_id
    env: str  # env_id


class PlanRunParams(TypedDict):
    """Plan run parameters"""

    plan: str  # plan_id
    env: str  # env_id


@celery_app.task(queue=TC_QUEUE, expires=60 * 60)
def run_test_cases_by_project(**kwargs: ProjectRunParams) -> None:
    """运行项目下所有测试用例

    Args:
        project: Project ID
        env: Environment ID
    """
    logger.info(kwargs)
    from app.testcase.serializers import RunTestCasesByProjectSerializer

    serializer = RunTestCasesByProjectSerializer(data=kwargs)
    if serializer.is_valid():
        serializer.save()
    else:
        logger.error(serializer.errors)


@celery_app.task(queue=TC_QUEUE, expires=60 * 60)
def run_test_cases_by_dir(**kwargs: DirectoryRunParams) -> None:
    """运行目录下所有测试用例

    Args:
        dir: Directory ID
        env: Environment ID
    """
    logger.info(kwargs)
    from app.testcase.serializers import RunTestCasesByDirSerializer

    serializer = RunTestCasesByDirSerializer(data=kwargs)
    if serializer.is_valid():
        serializer.save()
    else:
        logger.error(serializer.errors)


@celery_app.task(queue=TC_QUEUE, expires=60 * 60)
def run_test_cases_by_plan(**kwargs: PlanRunParams) -> None:
    """运行测试计划下所有测试用例

    Args:
        plan: Plan ID
        env: Environment ID
    """
    logger.info(kwargs)
    from app.testcase.serializers import RunTestCasesByPlanSerializer

    serializer = RunTestCasesByPlanSerializer(data=kwargs)
    if serializer.is_valid():
        serializer.save()
    else:
        logger.error(serializer.errors)


class TestReportNotifyParams(TypedDict):
    """Test report notification parameters"""

    task_id: str
    env_name: str
    report_name: str
    summary: str
    notify: Optional[bool]


def test_report_notify(**kwargs: TestReportNotifyParams) -> None:
    """Notify test report results

    Args:
        task_id: Task identifier
        env_name: Environment name
        report_name: Report name
        summary: JSON string containing test results
        notify: Whether to send notification (default: False)

    Raises:
        ValueError: If required parameters are missing or invalid
        json.JSONDecodeError: If summary is not valid JSON
    """
    try:
        required_fields = {"task_id", "env_name", "report_name", "summary"}
        missing_fields = required_fields - kwargs.keys()
        if missing_fields:
            raise ValueError(f"Missing required fields: {missing_fields}")

        notify_test_result(
            task_id=str(kwargs["task_id"]),
            env_name=str(kwargs["env_name"]),
            report_name=str(kwargs["report_name"]),
            summary=json.loads(str(kwargs["summary"])),
            notify=bool(kwargs.get("notify", False)),
        )

    except json.JSONDecodeError as e:
        logger.error("Invalid summary JSON: %s", e)
        raise
    except Exception as e:
        logger.exception("Failed to send test report notification", e)
        raise


@shared_task(queue="celery")
def delete_old_test_reports():
    """删除超过5天的测试报告"""
    try:
        # Import here to avoid circular imports
        from app.testcase.models import TestReport

        days = 5
        logger.info(f"开始删除超过 {days} 天的测试报告")

        days_ago = datetime.now() - timedelta(days=days)
        # 获取需要删除的报告数量
        reports_to_delete = TestReport.objects.filter(created_at__lt=days_ago)
        count = reports_to_delete.count()

        # 执行删除
        batch_delete(reports_to_delete, TestReport)

        logger.info(f"成功删除 {count} 个超过 {days} 天的测试报告")
        return f"删除了 {count} 个测试报告"
    except Exception as e:
        logger.error(f"删除旧测试报告时发生错误: {e}")
        raise
