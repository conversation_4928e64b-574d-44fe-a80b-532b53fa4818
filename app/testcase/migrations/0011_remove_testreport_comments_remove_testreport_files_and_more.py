# Generated by Django 4.2.5 on 2024-01-18 15:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('testcase', '0010_alter_testcase_unique_together_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='testreport',
            name='comments',
        ),
        migrations.RemoveField(
            model_name='testreport',
            name='files',
        ),
        migrations.RemoveField(
            model_name='testreport',
            name='test_result',
        ),
        migrations.AddField(
            model_name='testreport',
            name='result',
            field=models.IntegerField(choices=[(0, 'Pass'), (1, 'Fail'), (2, 'Interrupted'), (3, 'Internal Error'), (4, 'Usage Error'), (5, 'No Test')], default=1),
        ),
        migrations.AddField(
            model_name='testreport',
            name='summary',
            field=models.TextField(default='{}'),
        ),
        migrations.DeleteModel(
            name='TestReportUploadFile',
        ),
    ]
