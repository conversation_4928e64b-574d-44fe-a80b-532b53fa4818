# Generated by Django 4.2.5 on 2024-01-13 11:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('testcase', '0009_testreportuploadfile_testreport'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='testcase',
            unique_together=set(),
        ),
        migrations.AddConstraint(
            model_name='testcase',
            constraint=models.UniqueConstraint(condition=models.Q(('is_deleted', False)), fields=('name', 'project'), name='unique_active_testcases'),
        ),
    ]
