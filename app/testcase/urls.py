from django.urls import path

from .views import (
    DevopsRunAPIView,
    DevopsTaskQueryAPIView,
    RunTestCaseAPIView,
    RunTestCaseByProjectAPIView,
    RunTestCasesByDirAPIView,
    RunTestPlanAPIView,
    TestCaseDataAPIView,
    TestCaseDetailAPIView,
    TestCaseDirectoryAPIView,
    TestCaseDirectoryDestoryAPIView,
    TestCaseExportAPIView,
    TestCaseListCreateAPIView,
    TestCaseUploadView,
    TestPlanDetailAPIView,
    TestPlanListCreateAPIView,
    TestReoprtDetailAPIView,
    TestReoprtListCreateAPIView,
)

urlpatterns = [
    path(
        "testcase/dir/<str:project_id>",
        TestCaseDirectoryAPIView.as_view(),
        name="directory-list",
    ),
    path(
        "testcase/dir/<str:project_id>/<str:key>",
        TestCaseDirectoryDestoryAPIView.as_view(),
        name="remove-directory",
    ),
    path(
        "testcase",
        TestCaseListCreateAPIView.as_view(),
        name="testcase-list",  # list batch-delete
    ),
    path(
        "testcase/upload",
        TestCaseUploadView.as_view(),
        name="testcase-upload",
    ),
    path(
        "testcase/run",
        RunTestCaseAPIView.as_view(),
        name="testcase-run",
    ),
    path(
        "testplan/run",
        RunTestPlanAPIView.as_view(),
        name="testplan-run",
    ),
    path(
        "testcase/runbyproject",
        RunTestCaseByProjectAPIView.as_view(),
        name="testcase-run-by-project",
    ),
    path(
        "testcase/runbydir",
        RunTestCasesByDirAPIView.as_view(),
        name="testcase-run-by-project",
    ),
    path(
        "testcase/export",
        TestCaseExportAPIView.as_view(),
        name="testcase-export",
    ),
    path(
        "testcase/<str:id>",  # 排在路由最后，默认匹配完其他的，再匹配 id
        TestCaseDetailAPIView.as_view(),
        name="testcase-detail",
    ),
    path(
        "testcasedata/<str:id>",
        TestCaseDataAPIView.as_view(),
        name="testcasedata",
    ),
    path("testreport", TestReoprtListCreateAPIView.as_view(), name="testreport-list"),
    path(
        "testreport/<str:id>",
        TestReoprtDetailAPIView.as_view(),
        name="testreport-detail",
    ),
    path("testplan", TestPlanListCreateAPIView.as_view(), name="testplan-list"),
    path(
        "testplan/<str:id>",
        TestPlanDetailAPIView.as_view(),
        name="testplan-detail",
    ),
    path("devops/run", DevopsRunAPIView.as_view(), name="run-test"),
    path(
        "devops/task/<str:task_id>", DevopsTaskQueryAPIView.as_view(), name="run-test"
    ),
]
