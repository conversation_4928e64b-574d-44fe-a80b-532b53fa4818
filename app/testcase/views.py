import logging
import zipfile
from datetime import datetime

import yaml
from django.conf import settings
from django.http import HttpResponse
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from rest_framework.parsers import MultiPartParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.views import APIView

from app import views
from app.response import <PERSON>rrorRespo<PERSON>, SuccessResponse
from app.testcase.filter import TestCase<PERSON>ilter, TestReportFilter
from app.testcase.service import TestRunTask

from .models import TestCase, TestCaseDirectory, TestPlan, TestReport
from .serializers import (
    DevopsRunSerializer,
    RunTestCasesByDirSerializer,
    RunTestCasesByProjectSerializer,
    RunTestCasesSerializer,
    RunTestPlanSerializer,
    TestCaseDataSerializer,
    TestCaseDirectorySerializer,
    TestCaseDirectoryUpdateSerializer,
    TestCaseExportSerializer,
    TestCaseSerializer,
    TestCaseUploadSerializer,
    TestPlanSerializer,
    TestReportDetailSerializer,
    TestReportSerializer,
)

logger = logging.getLogger(__name__)


# Create your views here.
# @extend_schema(request=TestCaseDirectoryUpdateSerializer)
@extend_schema_view(
    get=extend_schema(responses=TestCaseDirectorySerializer),
    post=extend_schema(request=TestCaseDirectoryUpdateSerializer),
    put=extend_schema(request=TestCaseDirectoryUpdateSerializer),
)
class TestCaseDirectoryAPIView(APIView):
    def get(self, request: Request, *args, **kwargs):
        """获取项目的测试用例目录"""
        project_id = kwargs.get("project_id")
        directories = TestCaseDirectory.objects.filter(
            project_id=project_id, parent=None
        )
        serializer = TestCaseDirectorySerializer(directories, many=True)
        return SuccessResponse(serializer.data)

    def post(self, request: Request, *args, **kwargs):
        """新增测试用例（子）目录"""
        request.data["project_id"] = kwargs.get("project_id")
        serializer = TestCaseDirectoryUpdateSerializer(
            data=request.data, context={"request": request}
        )
        if serializer.is_valid():
            serializer.save()
            return SuccessResponse(serializer.data)
        return ErrorResponse(serializer.errors, _status=status.HTTP_400_BAD_REQUEST)

    def put(self, request: Request, *args, **kwargs):
        """修改测试用例目录名称"""
        request.data["project_id"] = kwargs.get("project_id")
        key = request.data.get("key")
        directory = TestCaseDirectory.objects.get(key=key)
        serializer = TestCaseDirectoryUpdateSerializer(
            directory, data=request.data, context={"request": request}
        )
        if serializer.is_valid():
            serializer.save()
            return SuccessResponse(serializer.data)
        return ErrorResponse(serializer.errors, _status=status.HTTP_400_BAD_REQUEST)


class TestCaseDirectoryDestoryAPIView(APIView):
    def delete(self, request: Request, *args, **kwargs):
        """删除项目的单个测试用例目录"""
        key = kwargs.get("key")
        directory = TestCaseDirectory.objects.get(key=key)
        directory.delete()
        return SuccessResponse()


class TestCaseListCreateAPIView(views.ListCreateModelAPIView):
    """测试用例列表、测试用例新增、测试用例批量删除"""

    queryset = TestCase.objects.all()
    serializer_class = TestCaseSerializer
    filterset_class = TestCaseFilter


class TestCaseDetailAPIView(views.DetailAPIView):
    """测试用例修改、测试用例删除"""

    queryset = TestCase.objects.all()
    serializer_class = TestCaseSerializer


@extend_schema(
    methods=["POST"],
    request=TestCaseUploadSerializer,
)
class TestCaseUploadView(APIView):
    serializer_class = TestCaseUploadSerializer
    parser_classes = [MultiPartParser]

    def post(self, request: Request, *args, **kwargs):
        serializer = self.serializer_class(
            data=request.data, context={"request": request}
        )
        if serializer.is_valid():
            serializer.save()
            return SuccessResponse()
        else:
            return ErrorResponse(serializer.errors, _status=status.HTTP_400_BAD_REQUEST)

    def put(self, request: Request, *args, **kwargs):
        serializer = self.serializer_class(
            instance=True, data=request.data, context={"request": request}
        )
        if serializer.is_valid():
            serializer.save()
            return SuccessResponse()
        else:
            return ErrorResponse(serializer.errors, _status=status.HTTP_400_BAD_REQUEST)


@extend_schema(request=RunTestCasesSerializer)
class RunTestCaseAPIView(APIView):
    def post(self, request: Request, *args, **kwargs):
        serializer = RunTestCasesSerializer(data=request.data)
        if serializer.is_valid():
            result = serializer.save()
            task_id = result.id
            return SuccessResponse(task_id)
        else:
            return ErrorResponse(serializer.errors, _status=status.HTTP_400_BAD_REQUEST)


@extend_schema(request=RunTestCasesByProjectSerializer)
class RunTestCaseByProjectAPIView(APIView):
    def post(self, request: Request, *args, **kwargs):
        serializer = RunTestCasesByProjectSerializer(data=request.data)
        if serializer.is_valid():
            result = serializer.save()
            task_id = result.id
            return SuccessResponse(task_id)
        else:
            return ErrorResponse(serializer.errors, _status=status.HTTP_400_BAD_REQUEST)


@extend_schema(request=RunTestCasesByDirSerializer)
class RunTestCasesByDirAPIView(APIView):
    def post(self, request: Request, *args, **kwargs):
        serializer = RunTestCasesByDirSerializer(data=request.data)
        if serializer.is_valid():
            result = serializer.save()
            task_id = result.id
            return SuccessResponse(task_id)
        else:
            return ErrorResponse(serializer.errors, _status=status.HTTP_400_BAD_REQUEST)


@extend_schema_view(
    post=extend_schema(request=TestCaseDataSerializer),
    put=extend_schema(request=TestCaseDataSerializer),
)
class TestCaseDataAPIView(APIView):
    def get(self, request: Request, *args, **kwargs):
        testcase_id = kwargs.get("id")
        try:
            testcase = TestCase.objects.get(id=testcase_id)
            serializer = TestCaseDataSerializer(testcase)
            return SuccessResponse(serializer.data)
        except TestCase.DoesNotExist as e:
            return ErrorResponse(e, _status=status.HTTP_404_NOT_FOUND)

    def put(self, request: Request, *args, **kwargs):
        testcase_id = kwargs.get("id")
        try:
            testcase = TestCase.objects.get(id=testcase_id)
            serializer = TestCaseDataSerializer(testcase, data=request.data)
            # 后面补充校验
            if serializer.is_valid():
                serializer.save()
            else:
                return ErrorResponse(
                    serializer.errors, _status=status.HTTP_400_BAD_REQUEST
                )
            return SuccessResponse("更新成功")
        except TestCase.DoesNotExist as e:
            return ErrorResponse(e, _status=status.HTTP_404_NOT_FOUND)


class TestReoprtListCreateAPIView(views.ListCreateModelAPIView):
    """获取测试报告列表"""

    queryset = TestReport.objects.all()
    serializer_class = TestReportSerializer
    filterset_class = TestReportFilter


class TestReoprtDetailAPIView(views.DetailAPIView):
    queryset = TestReport.objects.all()
    serializer_class = TestReportDetailSerializer


@extend_schema(request=TestCaseExportSerializer)
class TestCaseExportAPIView(APIView):
    def get(self, request: Request, *args, **kwargs):
        id = request.query_params.getlist("id")
        serializer = TestCaseExportSerializer(data={"id": id})
        if serializer.is_valid():
            date_time = datetime.now().strftime("%Y%m%d%H%M$S")
            test_cases_json = serializer.save()
            response = HttpResponse(content_type="application/zip")
            # 设置HTTP头部，以便浏览器识别为文件下载
            response["Content-Disposition"] = (
                f'attachment; filename="testcase_{date_time}.zip"'
            )
            with zipfile.ZipFile(response, "w") as zip_file:  # type: ignore
                for testcase in test_cases_json:
                    file_name = f'{testcase["config"]["name"]}.yaml'
                    # logger.info(testcase)
                    # TODO: 优化导出测试用例的 yaml 格式
                    yaml_data = yaml.dump(
                        testcase,
                        sort_keys=False,
                        allow_unicode=True,
                        default_flow_style=None,
                    )
                    logger.info(yaml_data)
                    zip_file.writestr(file_name, yaml_data)
            return response
        else:
            return ErrorResponse(serializer.errors, _status=status.HTTP_400_BAD_REQUEST)


@extend_schema_view(
    # get=extend_schema(responses=TestCaseDirectorySerializer),
    post=extend_schema(request=DevopsRunSerializer),
)
class DevopsRunAPIView(APIView):
    """devops 平台运行测试用例"""

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request: Request, *args, **kwargs):
        """传入 env, region, label [], 运行某些服务相关的冒烟自动化测试"""
        serializer = DevopsRunSerializer(data=request.data)
        if serializer.is_valid():
            result = serializer.save()
            task_id = result.id
            return SuccessResponse(task_id)
        else:
            return ErrorResponse(serializer.errors, _status=status.HTTP_400_BAD_REQUEST)


class DevopsTaskQueryAPIView(APIView):
    """devops 平台查询任务状态和结果"""

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request: Request, *args, **kwargs):
        """
        •  PENDING: 任务等待执行。
        •  STARTED: 任务已经开始执行。
        •  SUCCESS: 任务执行成功完成。
        •  FAILURE: 任务执行失败。
        •  RETRY: 任务将被重试。
        •  REVOKED: 任务被撤销。
        """
        task_id = kwargs.get("task_id")
        resp = {"task_id": task_id}
        task = TestRunTask(task_id)
        task_status = task.get_status()
        resp["task_status"] = task_status
        if task_status == "SUCCESS":
            resp["report_url"] = (
                f"{settings.FRONTEND_HOST}/preview/testreport/{task_id}"
            )
            resp["test_result"] = task.get_test_result()
        return SuccessResponse(resp)


class TestPlanListCreateAPIView(views.ListCreateModelAPIView):
    queryset = TestPlan.objects.all()
    serializer_class = TestPlanSerializer


class TestPlanDetailAPIView(views.DetailAPIView):
    queryset = TestPlan.objects.all()
    serializer_class = TestPlanSerializer


@extend_schema(request=RunTestPlanSerializer)
class RunTestPlanAPIView(APIView):
    def post(self, request: Request, *args, **kwargs):
        serializer = RunTestPlanSerializer(data=request.data)
        if serializer.is_valid():
            result = serializer.save()
            task_id = result.id
            return SuccessResponse(task_id)
        else:
            return ErrorResponse(serializer.errors, _status=status.HTTP_400_BAD_REQUEST)
