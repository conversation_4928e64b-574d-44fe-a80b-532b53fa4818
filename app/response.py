import logging
from typing import Dict, List, Text, Union

from pydantic import BaseModel, StrictBool
from rest_framework import status
from rest_framework.response import Response

logger = logging.getLogger(__name__)


class ApiResponse(BaseModel):
    success: StrictBool
    data: Union[Dict, List, Text, None]
    errorCode: Union[Text, None] = None
    errorMessage: Union[Text, None] = None
    # showType: number; // error display type： 0 silent; 1 message.warn; 2 message.error; 4 notification; 9 page
    # traceId: string
    # host: string


class ErrorResponse(Response):
    def __init__(
        self,
        exception: Exception | str,
        data=None,
        _status=status.HTTP_500_INTERNAL_SERVER_ERROR,
    ):
        super().__init__(
            data=ApiResponse(
                success=False,
                data=data,
                errorCode=str(_status),
                errorMessage=str(exception),
            ).model_dump(),
            status=_status,
            exception=True,
        )


class SuccessResponse(Response):
    def __init__(self, data=None):
        format_data = ApiResponse(success=True, data=data).model_dump()
        super().__init__(
            data=format_data,
            status=status.HTTP_200_OK,
        )
