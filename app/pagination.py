from django.core.cache import cache
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response


class CachedPageNumberPagination(PageNumberPagination):
    page_size = 15
    page_size_query_param = "pageSize"
    page_query_param = "current"
    max_page_size = 1000

    def get_paginated_response(self, resp):
        if not isinstance(resp, dict):
            raise RuntimeError("serializer class should be CustomListSerializer")

        # 缓存总数查询结果
        cache_key = f"count_{self.request.path}"
        count = cache.get(cache_key)
        if count is None:
            count = self.page.paginator.count
            cache.set(cache_key, count, 60)  # 缓存1分钟

        resp["total"] = count
        return Response(resp)
