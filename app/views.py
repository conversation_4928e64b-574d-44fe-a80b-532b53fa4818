import logging
from typing import <PERSON>ple

from django.contrib.auth.models import Anonymous<PERSON>ser
from rest_framework import status
from rest_framework.generics import <PERSON>ricAPI<PERSON>iew, ListCreateAPIView, get_object_or_404
from rest_framework.mixins import (
    DestroyModelMixin,
    RetrieveModelMixin,
    UpdateModelMixin,
)
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView

from app.response import ApiResponse, ErrorResponse

logger = logging.getLogger(__name__)


class DetailModelMixin(RetrieveModelMixin):
    def get(self, request: Request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)


class PutModelMixin(UpdateModelMixin):
    def put(self, request: Request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)


class DeleteModelMixin(DestroyModelMixin):
    def delete(self, request: Request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)


class ListCreateModelAPIView(ListCreateAPIView):
    lookup_field = "id"
    ordering_fields: Tuple[str, ...] = ("created_at", "updated_at")
    ordering: Tuple[str, ...] = (
        "-created_at",
        "-updated_at",
    )

    def delete(self, request: Request, *args, **kwargs):
        """批量删除接口"""
        lookup_fields = request.data.get(self.lookup_field, [])
        # logger.info(f'{self.lookup_field} {lookup_fields}')
        if not lookup_fields:
            return ErrorResponse(
                exception="not found", _status=status.HTTP_404_NOT_FOUND
            )
        else:
            filter_kwargs = {f"{self.lookup_field}__in": lookup_fields}
            self.get_queryset().filter(**filter_kwargs).update(is_deleted=True)
            return Response(
                data=ApiResponse(success=True, data=None).model_dump(),
                status=status.HTTP_204_NO_CONTENT,
            )


class DetailAPIView(DetailModelMixin, PutModelMixin, DeleteModelMixin, GenericAPIView):
    lookup_field = "id"
    ordering_fields: Tuple[str, ...] = ("created_at", "updated_at")
    ordering: Tuple[str, ...] = (
        "-created_at",
        "-updated_at",
    )

    def get_object(self):
        """
        Returns the object the view is displaying.

        You may want to override this if you need to provide non-standard
        queryset lookups.  Eg if objects are referenced using multiple
        keyword arguments in the url conf.
        """
        queryset = self.filter_queryset(self.get_queryset())

        # Perform the lookup filtering.
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field

        assert lookup_url_kwarg in self.kwargs, (
            "Expected view %s to be called with a URL keyword argument "
            'named "%s". Fix your URL conf, or set the `.lookup_field` '
            "attribute on the view correctly."
            % (self.__class__.__name__, lookup_url_kwarg)
        )

        filter_kwargs = {self.lookup_field: self.kwargs[lookup_url_kwarg]}
        obj = get_object_or_404(queryset, **filter_kwargs)

        # May raise a permission denied
        self.check_object_permissions(self.request, obj)

        return obj


class SelfModelMixin(GenericAPIView):
    def get_queryset(self):
        assert self.queryset is not None, (
            "'%s' should either include a `queryset` attribute, "
            "or override the `get_queryset()` method." % self.__class__.__name__
        )
        # 只能操作自己创建的设备
        current_user = self.request.user
        if isinstance(current_user, AnonymousUser):
            return self.queryset
        return self.queryset.filter(created_by=current_user)


class HealthCheckView(APIView):
    def get(self, request: Request, *args, **kwargs):
        return Response({"status": "UP"})
