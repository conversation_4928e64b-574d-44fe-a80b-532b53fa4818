from django_celery_beat.models import PeriodicTask
from drf_spectacular.utils import extend_schema
from rest_framework import status
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView

from app import views
from app.response import ApiResponse, ErrorResponse, SuccessResponse

from .models import AwsAccessKey, CoverageBucket, Environment, Tag, Variable
from .serializers import (
    AwsAccessKeySerializer,
    CoverageBucketSerializer,
    EnvironmentListSerializer,
    EnvironmentVariableCreateSerializer,
    EnvironmentVariableSerializer,
    ScheduledTaskSerializer,
    TagSerializer,
)


class EnvironmentListCreateAPIView(views.ListCreateModelAPIView):
    queryset = Environment.objects.all()
    serializer_class = EnvironmentListSerializer


class EnvironmentDetailAPIView(views.DetailAPIView):
    queryset = Environment.objects.all()
    serializer_class = EnvironmentListSerializer


@extend_schema(request=EnvironmentVariableCreateSerializer)
class EnvironmentVariableListView(APIView):
    def get(self, request: Request, *args, **kwargs):
        """获取变量列表"""
        variables = Variable.objects.all().order_by("key")
        serializer = EnvironmentVariableSerializer(variables, many=True)
        return SuccessResponse(serializer.data)

    def post(self, request: Request, *args, **kwargs):
        serializer = EnvironmentVariableCreateSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return SuccessResponse()
        return ErrorResponse(serializer.errors, _status=status.HTTP_400_BAD_REQUEST)

    def put(self, request: Request, *args, **kwargs):
        serializer = EnvironmentVariableCreateSerializer(
            request.data, data=request.data
        )
        if serializer.is_valid():
            serializer.save()
            return SuccessResponse()
        return ErrorResponse(serializer.errors, _status=status.HTTP_400_BAD_REQUEST)


class EnvironmentVariableDeleteView(APIView):
    def delete(self, request: Request, *args, **kwargs):
        key = kwargs.get("key")
        if not key:
            return ErrorResponse("key is required", _status=status.HTTP_400_BAD_REQUEST)
        Variable.objects.filter(key=key).delete()
        return SuccessResponse()


class ScheduledTaskListCreateAPIView(views.ListCreateModelAPIView):
    ordering_fields = ("date_changed",)
    ordering = ("-date_changed",)
    queryset = PeriodicTask.objects.all()
    serializer_class = ScheduledTaskSerializer

    def delete(self, request: Request, *args, **kwargs):
        """批量删除接口"""
        lookup_fields = request.data.get(self.lookup_field, [])
        # logger.info(f'{self.lookup_field} {lookup_fields}')
        if not lookup_fields:
            return ErrorResponse(
                exception="not found", _status=status.HTTP_404_NOT_FOUND
            )
        else:
            filter_kwargs = {f"{self.lookup_field}__in": lookup_fields}
            # 不是baseModel，只能硬删除
            self.get_queryset().filter(**filter_kwargs).delete()
            return Response(
                data=ApiResponse(success=True, data=None).model_dump(),
                status=status.HTTP_204_NO_CONTENT,
            )


class ScheduledTaskRetrieveUpdateDestroyAPIView(views.DetailAPIView):
    lookup_field = "id"
    ordering_fields = ("date_changed",)
    ordering = ("-date_changed",)
    queryset = PeriodicTask.objects.all()
    serializer_class = ScheduledTaskSerializer


class TagListCreateAPIView(views.ListCreateModelAPIView):
    """测试用例标签列表、测试用例标签新增、测试用例标签批量删除"""

    queryset = Tag.objects.all()
    serializer_class = TagSerializer


class TagDetailAPIView(views.DetailAPIView):
    """测试用例标签修改、测试用例标签删除"""

    queryset = Tag.objects.all()
    serializer_class = TagSerializer


class AwsAccessKeyListCreateAPIView(views.ListCreateModelAPIView):
    queryset = AwsAccessKey.objects.all()
    serializer_class = AwsAccessKeySerializer


class AwsAccessKeyDetailAPIView(views.DetailAPIView):
    # permission_classes = (permissions.IsAdminUser,)
    queryset = AwsAccessKey.objects.all()
    serializer_class = AwsAccessKeySerializer


class CoverageBucketListCreateAPIView(views.ListCreateModelAPIView):
    queryset = CoverageBucket.objects.all()
    serializer_class = CoverageBucketSerializer


class CoverageBucketDetailAPIView(views.DetailAPIView):
    # permission_classes = (permissions.IsAdminUser,)
    queryset = CoverageBucket.objects.all()
    serializer_class = CoverageBucketSerializer
