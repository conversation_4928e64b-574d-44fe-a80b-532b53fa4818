from django.urls import path

from .views import (
    AwsAccessKeyDetailAPIView,
    AwsAccessKeyListCreateAPIView,
    CoverageBucketDetailAPIView,
    CoverageBucketListCreateAPIView,
    EnvironmentDetailAPIView,
    EnvironmentListCreateAPIView,
    EnvironmentVariableDeleteView,
    EnvironmentVariableListView,
    ScheduledTaskListCreateAPIView,
    ScheduledTaskRetrieveUpdateDestroyAPIView,
    TagDetailAPIView,
    TagListCreateAPIView,
)

urlpatterns = [
    path("env", EnvironmentListCreateAPIView.as_view(), name="env-list"),
    path("env/<str:id>", EnvironmentDetailAPIView.as_view(), name="env-detail"),
    path("var", EnvironmentVariableListView.as_view(), name="env-var"),
    path(
        "var/<str:key>", EnvironmentVariableDeleteView.as_view(), name="delete-env-var"
    ),
    path(
        "scheduled-tasks",
        ScheduledTaskListCreateAPIView.as_view(),
        name="scheduled-tasks-list-create",
    ),
    path(
        "scheduled-tasks/<str:id>",
        ScheduledTaskRetrieveUpdateDestroyAPIView.as_view(),
        name="scheduled-tasks-detail",
    ),
    path(
        "tag",
        TagListCreateAPIView.as_view(),
        name="testcasetag",
    ),
    path(
        "tag/<str:id>",
        TagDetailAPIView.as_view(),
        name="testcasetag",
    ),
    path("access_key", AwsAccessKeyListCreateAPIView.as_view(), name="access_key-list"),
    path(
        "access_key/<str:id>",
        AwsAccessKeyDetailAPIView.as_view(),
        name="access_key-detail",
    ),
    path(
        "coverage_bucket",
        CoverageBucketListCreateAPIView.as_view(),
        name="coverage_bucket-list",
    ),
    path(
        "coverage_bucket/<str:id>",
        CoverageBucketDetailAPIView.as_view(),
        name="coverage_bucket-detail",
    ),
]
