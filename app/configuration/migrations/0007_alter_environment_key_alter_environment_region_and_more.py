# Generated by Django 4.2.11 on 2024-03-21 16:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('configuration', '0006_alter_environment_key_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='environment',
            name='key',
            field=models.CharField(choices=[('test', 'Test'), ('test_1', 'Test 1'), ('prod', 'Prod')], db_comment='环境标识', max_length=64),
        ),
        migrations.AlterField(
            model_name='environment',
            name='region',
            field=models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-central-1', 'Eu'), ('cn-north-1', 'Cn')], db_comment='所在区域', default='us-east-1', max_length=32),
        ),
        migrations.CreateModel(
            name='CoverageBucket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('env', models.CharField(choices=[('test', 'Test'), ('test_1', 'Test 1'), ('prod', 'Prod')], db_comment='环境标识', max_length=64)),
                ('type', models.CharField(choices=[('go_lambda', 'Go Lambda'), ('go_web', 'Go Web')], max_length=128)),
                ('region', models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-central-1', 'Eu'), ('cn-north-1', 'Cn')], db_comment='所在区域', max_length=32)),
                ('bucket_name', models.CharField(max_length=128)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'coverage_bucket',
            },
        ),
        migrations.CreateModel(
            name='AwsAccessKey',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('env', models.CharField(choices=[('test', 'Test'), ('test_1', 'Test 1'), ('prod', 'Prod')], db_comment='环境标识', max_length=64)),
                ('desc', models.CharField(blank=True, max_length=128, null=True)),
                ('account_id', models.CharField(max_length=128)),
                ('access_key_id', models.CharField(max_length=128)),
                ('secret_access_key', models.CharField(max_length=128)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'aws_access_key',
            },
        ),
    ]
