# Generated by Django 4.2.5 on 2024-03-06 20:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('configuration', '0005_remove_environment_desc_remove_environment_name_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='environment',
            name='key',
            field=models.Char<PERSON>ield(max_length=64),
        ),
        migrations.AlterUniqueTogether(
            name='environment',
            unique_together={('key', 'region')},
        ),
    ]
