# Generated by Django 4.2.5 on 2024-03-06 16:50

from django.db import migrations, models


def fill_key_field(apps, schema_editor):
    Environment = apps.get_model("configuration", "Environment")
    for obj in Environment.objects.all():
        obj.key = obj.name
        obj.save(update_fields=["key"])


class Migration(migrations.Migration):

    dependencies = [
        ("configuration", "0004_tag"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="environment",
            name="desc",
        ),
        migrations.AddField(
            model_name="environment",
            name="key",
            field=models.CharField(max_length=64, blank=True),
        ),
        migrations.AddField(
            model_name="environment",
            name="region",
            field=models.Char<PERSON>ield(
                choices=[
                    ("us-east-1", "Us"),
                    ("ap-northeast-1", "Ap"),
                    ("eu-central-1", "Eu"),
                    ("cn-north-1", "Cn"),
                ],
                default="us-east-1",
                help_text="所在区域",
                max_length=32,
            ),
        ),
        migrations.RunPython(fill_key_field),
        migrations.RemoveField(
            model_name="environment",
            name="name",
        ),
        migrations.AlterField(
            model_name="environment",
            name="key",
            field=models.CharField(max_length=64, unique=True),
        ),
    ]
