import logging

from django_celery_beat.models import CrontabSchedule, PeriodicTask, cronexp
from rest_framework import serializers

from app.serializers import BaseModelSerializer, CustomListSerializer

from .models import (
    AwsAccessKey,
    CoverageBucket,
    Environment,
    EnvironmentVariable,
    Tag,
    Variable,
)

logger = logging.getLogger(__name__)


class EnvironmentListSerializer(BaseModelSerializer):
    class Meta:
        model = Environment
        exclude = ("is_deleted",)
        list_serializer_class = CustomListSerializer


class EnvironmentVariableSerializer(serializers.ModelSerializer):
    key = serializers.CharField()
    desc = serializers.CharField()
    values = serializers.SerializerMethodField()

    class Meta:
        model = Variable
        fields = ("key", "desc", "values")
        # list_serializer_class = CustomListSerializer

    def get_values(self, obj):
        env_vars = EnvironmentVariable.objects.filter(variable=obj)
        # 创建一个字典，其中包含每个环境的str(id)和相应的值
        return {str(env_var.env.pk): env_var.value for env_var in env_vars}


class EnvironmentVariableCreateSerializer(serializers.Serializer):
    key = serializers.CharField(max_length=255)
    desc = serializers.CharField(required=False)
    values = serializers.JSONField()

    def create(self, validated_data):
        key = validated_data.get("key")
        desc = validated_data.get("desc")
        values = validated_data.get("values")
        variable, _ = Variable.objects.get_or_create(key=key, desc=desc)
        env_variables = []
        for env_id, value in values.items():
            env_variable = EnvironmentVariable.objects.create(
                env_id=env_id, variable=variable, value=value
            )
            env_variables.append(env_variable)
        return env_variables

    def update(self, instance, validated_data):
        key = validated_data.get("key")
        desc = validated_data.get("desc")
        values = validated_data.get("values")
        try:
            variable = Variable.objects.get(key=key)
        except Variable.DoesNotExist:
            raise serializers.ValidationError({"key": "Variable does not exist"})
        variable.desc = desc
        variable.save()
        env_variables = []
        for env_id, value in values.items():
            env = EnvironmentVariable.objects.filter(env_id=env_id, variable=variable)
            if env.exists():
                logger.info(env)
            else:
                logger.info("Not found")
            env_variable, created = EnvironmentVariable.objects.update_or_create(
                env_id=env_id, variable=variable, defaults={"value": value}
            )
            env_variables.append(env_variable)
        return env_variables


class CrontabScheduleField(serializers.Field):
    def to_representation(self, value):
        # 将CrontabSchedule对象转换为字符串
        if isinstance(value, CrontabSchedule):
            cron_expression = "{} {} {} {} {}".format(
                cronexp(value.minute),
                cronexp(value.hour),
                cronexp(value.day_of_month),
                cronexp(value.month_of_year),
                cronexp(value.day_of_week),
            )
            return cron_expression
        return None

    def to_internal_value(self, data):
        # 将字符串转换为CrontabSchedule对象
        # 假设字符串格式为 "minute hour day_of_month month_of_year day_of_week"
        try:
            minute, hour, day_of_month, month_of_year, day_of_week = data.split(" ")
            crontab_schedule, created = CrontabSchedule.objects.get_or_create(
                minute=minute,
                hour=hour,
                day_of_month=day_of_month,
                month_of_year=month_of_year,
                day_of_week=day_of_week,
            )
            return crontab_schedule
        except (ValueError, CrontabSchedule.DoesNotExist):
            raise serializers.ValidationError(
                "Invalid crontab format or crontab does not exist."
            )


class ScheduledTaskSerializer(serializers.ModelSerializer):
    crontab = CrontabScheduleField()
    crontab_desc = serializers.SerializerMethodField(read_only=True)
    updated_at = serializers.DateTimeField(source="date_changed", read_only=True)
    last_run_at = serializers.DateTimeField(read_only=True)
    #  ("app.testcase.tasks.run_test_cases_by_project", "按项目运行测试用例"),
    #     ("app.testcase.tasks.run_test_cases_by_dir", "按目录运行测试用例"),

    class Meta:
        model = PeriodicTask
        fields = (
            "id",
            "name",
            "crontab",
            "crontab_desc",
            "task",
            "kwargs",
            "enabled",
            "last_run_at",
            "updated_at",
        )
        list_serializer_class = CustomListSerializer

    def get_crontab_desc(self, obj: PeriodicTask):
        if obj.crontab is None:
            return "No crontab set"
        else:
            crontab_desc = obj.crontab.human_readable
            return crontab_desc


class TagSerializer(BaseModelSerializer):
    class Meta:
        model = Tag
        exclude = ("is_deleted",)
        list_serializer_class = CustomListSerializer


class AwsAccessKeySerializer(BaseModelSerializer):
    access_key_id = serializers.CharField(
        max_length=128, write_only=True
    )  # 不允许查看，只允许写
    secret_access_key = serializers.CharField(
        max_length=128, write_only=True
    )  # 不允许查看，只允许写

    class Meta:
        model = AwsAccessKey
        exclude = ("is_deleted",)
        list_serializer_class = CustomListSerializer


class CoverageBucketSerializer(BaseModelSerializer):
    class Meta:
        model = CoverageBucket
        exclude = ("is_deleted",)
        list_serializer_class = CustomListSerializer
