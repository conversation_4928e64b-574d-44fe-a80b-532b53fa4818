from typing import Any, Dict

from django.core.exceptions import ValidationError
from django.db import models

from app.models import BaseModel


class EnvLabel(models.TextChoices):
    TEST = "test"
    TEST_1 = "test_1"
    PROD = "prod"


class AwsRegion(models.TextChoices):
    US = "us-east-1"
    AP = "ap-northeast-1"
    EU = "eu-central-1"
    CN = "cn-north-1"


class Environment(BaseModel):
    key = models.CharField(
        max_length=64,
        db_comment="环境标识",
        choices=EnvLabel.choices,
    )
    region = models.CharField(
        max_length=32,
        db_comment="所在区域",
        choices=AwsRegion.choices,
        default=AwsRegion.US,
    )

    class Meta:
        db_table = "env"
        unique_together = (("key", "region"),)


class Variable(BaseModel):
    key = models.CharField(max_length=255, unique=True)
    desc = models.TextField(null=True, blank=True)

    class Meta:
        db_table = "variable"


class EnvironmentVariable(BaseModel):
    env = models.ForeignKey(
        Environment, on_delete=models.PROTECT
    )  # 删除Env时，如果还有EnvironmentVariable，则不允许删除
    variable = models.ForeignKey(Variable, on_delete=models.CASCADE)
    value = models.JSONField()

    class Meta:
        db_table = "env_variable"
        unique_together = ("env", "variable")

    @staticmethod
    def get_env_variables(env_id: int) -> Dict[str, Any]:
        env_variables = EnvironmentVariable.objects.filter(env_id=env_id)
        return {var.variable.key: var.value for var in env_variables}


class Tag(BaseModel):
    name = models.CharField(max_length=128, unique=True)

    def __str__(self):
        return self.name

    def delete(self, *args, **kwargs):
        # 检查是否有测试用例关联了这个标签
        if self.testcase_set.exists():
            raise ValidationError(
                f"Cannot delete tag '{self.name}' because it is used by one or more test cases."
            )
        super(Tag, self).delete(*args, **kwargs)

    class Meta:
        db_table = "tag"


class CoverageType(models.TextChoices):
    GO_LAMBDA = "go_lambda"
    GO_WEB = "go_web"


class AwsAccessKey(BaseModel):
    env = models.CharField(
        max_length=64,
        db_comment="环境标识",
        choices=EnvLabel.choices,
    )
    desc = models.CharField(max_length=128, null=True, blank=True)
    account_id = models.CharField(max_length=128)
    access_key_id = models.CharField(max_length=128)
    secret_access_key = models.CharField(max_length=128)

    class Meta:
        db_table = "aws_access_key"
        # unique_together = ["env"]


class CoverageBucket(BaseModel):
    env = models.CharField(
        max_length=64,
        db_comment="环境标识",
        choices=EnvLabel.choices,
    )
    type = models.CharField(max_length=128, choices=CoverageType.choices)
    region = models.CharField(
        max_length=32,
        db_comment="所在区域",
        choices=AwsRegion.choices,
    )
    bucket_name = models.CharField(max_length=128)

    class Meta:
        db_table = "coverage_bucket"
        unique_together = ["env", "type", "region"]
