import logging

from django.contrib.auth.models import AnonymousUser
from drf_spectacular.extensions import OpenApiSerializerExtension
from drf_spectacular.openapi import AutoSchema
from drf_spectacular.plumbing import (
    ResolvedComponent,
    build_array_type,
    build_basic_type,
    build_object_type,
)
from rest_framework import serializers

from app.response import SuccessResponse

logger = logging.getLogger(__name__)


class BaseSerializer(serializers.Serializer):
    def create(self, validated_data):
        user = self.context["request"].user
        # AnonymousUser created_by 默认为 None
        if not isinstance(user, AnonymousUser):
            validated_data["created_by"] = user
        return super().create(validated_data)

    def update(self, instance, validated_data):
        user = self.context["request"].user
        # AnonymousUser updated_by 默认为 None
        if not isinstance(user, AnonymousUser):
            validated_data["updated_by"] = user
        return super().update(instance, validated_data)


class UsernameField(serializers.Field):
    def to_representation(self, value):
        # TODO: 匿名用户，返回用户名
        return value.username


class StringField(serializers.Field):
    def to_representation(self, value):
        return str(value)


class CustomListSerializer(serializers.ListSerializer):
    # list_serializer_class = CustomListSerializer # 写在基类里没有用，要写在使用的类上面
    @property
    def data(self):
        list_response = super().data
        return SuccessResponse(list_response).data


class BaseModelSerializer(BaseSerializer, serializers.ModelSerializer):
    id = StringField(read_only=True)
    created_by = UsernameField(read_only=True)
    updated_by = UsernameField(read_only=True)

    # class Meta:
    #   exclude = ("created_by", "updated_by", "created_at", "updated_at")
    #   list_serializer_class = CustomListSerializer # 没用

    @property
    def data(self):
        single_response = super().data
        return SuccessResponse(single_response).data


class BaseModelExcludeId(BaseModelSerializer):
    def get_fields(self):
        fields = super().get_fields()
        # 删除 'id' 字段
        fields.pop("id", None)
        return fields


class JsonValidationError(serializers.ValidationError):
    def __init__(self, detail=None, code=None):
        super().__init__({"success": False, "errorMessage": detail}, code)


class WrappedResponseExtension(OpenApiSerializerExtension):
    target_class = BaseModelSerializer

    def map_serializer(self, auto_schema: AutoSchema, direction):
        component = auto_schema.resolve_serializer(self.target.child, direction)
        schema = build_object_type(
            properties={
                "success": build_basic_type(bool),
                "data": build_array_type(component.ref),
            }
        )
        logger.info(schema)
        list_component = ResolvedComponent(
            name=f"{component.name}List",
            type=ResolvedComponent.SCHEMA,
            object=self.target.child,
            schema=schema,
        )
        auto_schema.registry.register_on_missing(list_component)
        return list_component.ref
