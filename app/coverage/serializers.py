import logging

from rest_framework import serializers

from app.coverage.tasks import get_covcount_and_report, get_covmeta_hash
from app.serializers import BaseModelSerializer, CustomListSerializer

from .models import CoverageMeta, CoverageReport

logger = logging.getLogger(__name__)


class CoverageMetaSerializer(BaseModelSerializer):
    covmeta = serializers.CharField(
        max_length=128
    )  # 提交时不需要，后面 task 中更新; read_only=True暂时删除，先手动输入 meta_hash值

    class Meta:
        model = CoverageMeta
        exclude = ("is_deleted",)
        list_serializer_class = CustomListSerializer

    def create(self, validated_data):
        if (
            CoverageMeta.objects.filter(
                app_name=validated_data["app_name"], git_tag=validated_data["git_tag"]
            ).count()
            > 0
        ):
            raise serializers.DjangoValidationError("已存在的数据，不允许重复")
        instance = super().create(validated_data)
        task = get_covmeta_hash.delay(instance.app_name, instance.git_tag)
        logger.info(f"创建成功, 开始获取covmeta_hash, 任务 {task.id}")
        return instance

    def update(self, instance, validated_data):
        if (
            CoverageMeta.objects.filter(
                app_name=validated_data["app_name"], git_tag=validated_data["git_tag"]
            ).count()
            > 1
        ):
            raise serializers.DjangoValidationError("已存在的数据，不允许重复")
        diffrent = (
            instance.app_name != validated_data["app_name"]
            or instance.git_tag != validated_data["git_tag"]
        )
        instance = super().update(instance, validated_data)

        if instance.covmeta == "" or diffrent:
            task = get_covmeta_hash.delay(instance.app_name, instance.git_tag)
            logger.info(f"更新成功, 开始获取covmeta_hash, 任务 {task.id}")
        else:
            logger.info("更新成功, 暂无变动需要获取covmeta_hash")
        return instance


class CoverageReportSerializer(BaseModelSerializer):
    covcount = serializers.IntegerField(
        read_only=True
    )  # 提交时不需要，后面 task 中更新
    cov = serializers.CharField(read_only=True)
    covhtml = serializers.CharField(read_only=True)

    class Meta:
        model = CoverageReport
        exclude = ("is_deleted",)
        list_serializer_class = CustomListSerializer

    def create(self, validated_data):
        if (
            CoverageReport.objects.filter(
                app_name=validated_data["app_name"],
                start_time=validated_data["start_time"],
                end_time=validated_data["end_time"],
            ).count()
            > 0
        ):
            raise serializers.DjangoValidationError("已存在的数据，不允许重复")
        instance = super().create(validated_data)
        task = get_covcount_and_report.delay(
            instance.app_name,
            instance.env,
            instance.region,
            instance.start_time,
            instance.end_time,
        )
        logger.info(f"创建成功, 开始获取covcount, 任务 {task.id}")
        return instance

    def update(self, instance, validated_data):
        # if (
        #     CoverageReport.objects.filter(
        #         app_name=validated_data["app_name"], git_tag=validated_data["git_tag"]
        #     ).count()
        #     > 1
        # ):
        #     raise serializers.DjangoValidationError("已存在的数据，不允许重复")
        diffrent = (
            instance.start_time != validated_data["start_time"]
            or instance.end_time != validated_data["end_time"]
        )
        instance = super().update(instance, validated_data)

        if instance.covhtml == "" or diffrent:
            task = get_covcount_and_report.delay(
                instance.app_name,
                instance.env,
                instance.region,
                instance.start_time,
                instance.end_time,
            )
            logger.info(f"更新成功, 开始获取covcount, 任务 {task.id}")
        else:
            logger.info("更新成功, 暂无变动需要获取covcount")
        return instance
