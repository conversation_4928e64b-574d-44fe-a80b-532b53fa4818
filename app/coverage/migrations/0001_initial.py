# Generated by Django 4.2.11 on 2024-03-22 16:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CoverageReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('app_name', models.CharField(db_comment='应用名称:lambda名称/web名称', max_length=128)),
                ('env', models.CharField(choices=[('test', 'Test'), ('test_1', 'Test 1'), ('prod', 'Prod')], db_comment='环境标识', max_length=64)),
                ('region', models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-central-1', 'Eu'), ('cn-north-1', 'Cn')], db_comment='所在区域', max_length=32)),
                ('start_time', models.DateTimeField(db_comment='测试开始时间')),
                ('end_time', models.DateTimeField(db_comment='测试结束时间')),
                ('covcount', models.IntegerField(db_comment='覆盖率统计文件数', default=0)),
                ('cov', models.TextField(db_comment='cov.txt文件内容')),
                ('covhtml', models.TextField(db_comment='coverage.html文件内容')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'coverage_report',
            },
        ),
        migrations.CreateModel(
            name='CoverageMeta',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('app_name', models.CharField(db_comment='应用名称:lambda名称/web名称', max_length=128)),
                ('git_repo', models.CharField(db_comment='git仓库', max_length=128)),
                ('git_tag', models.CharField(db_comment='git标签', max_length=128)),
                ('covmeta', models.CharField(db_comment='covmeta文件结尾的hash值', max_length=128)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'coverage_meta',
            },
        ),
    ]
