from app import views
from app.coverage.models import CoverageMeta, CoverageReport
from app.coverage.serializers import CoverageMetaSerializer, CoverageReportSerializer


class CoverageMetaListCreateAPIView(views.ListCreateModelAPIView):
    queryset = CoverageMeta.objects.all()
    serializer_class = CoverageMetaSerializer


class CoverageMetaDetailAPIView(views.DetailAPIView):
    queryset = CoverageMeta.objects.all()
    serializer_class = CoverageMetaSerializer


class CoverageReportListCreateAPIView(views.ListCreateModelAPIView):
    queryset = CoverageReport.objects.all()
    serializer_class = CoverageReportSerializer


class CoverageReportDetailAPIView(views.DetailAPIView):
    queryset = CoverageReport.objects.all()
    serializer_class = CoverageReportSerializer
