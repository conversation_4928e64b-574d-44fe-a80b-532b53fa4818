from django.db import models

from app.configuration.models import AwsRegion, EnvLabel
from app.models import BaseModel


class CoverageMeta(BaseModel):
    """记录lambda，代码仓库对应 tag， covmeta 的 hash 值"""

    app_name = models.CharField(
        max_length=128, db_comment="应用名称:lambda名称/web名称"
    )
    git_repo = models.CharField(max_length=128, db_comment="git仓库")
    git_tag = models.CharField(max_length=128, db_comment="git标签")
    covmeta = models.CharField(max_length=128, db_comment="covmeta文件结尾的hash值")

    class Meta:
        db_table = "coverage_meta"


class CoverageReport(BaseModel):
    app_name = models.CharField(
        max_length=128, db_comment="应用名称:lambda名称/web名称"
    )
    env = models.CharField(
        max_length=64, db_comment="环境标识", choices=EnvLabel.choices
    )
    region = models.CharField(
        max_length=32,
        db_comment="所在区域",
        choices=AwsRegion.choices,
    )
    start_time = models.DateTimeField(db_comment="测试开始时间")
    end_time = models.DateTimeField(
        db_comment="测试结束时间"
    )  #  lambda覆盖率报告文件截止时间，取测试结束时间+15min
    covcount = models.IntegerField(default=0, db_comment="覆盖率统计文件数")
    cov = models.TextField(db_comment="cov.txt文件内容")
    covhtml = models.TextField(db_comment="coverage.html文件内容")

    class Meta:
        db_table = "coverage_report"
