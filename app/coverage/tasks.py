import logging
import os
import subprocess
import tempfile
from datetime import datetime
from zoneinfo import ZoneInfo

import boto3
import git
from botocore.exceptions import ClientError as BotoCoreClientError
from celery._state import get_current_task

from app.configuration.models import (
    AwsAccessKey,
    AwsRegion,
    CoverageBucket,
    CoverageType,
    EnvLabel,
)
from app.coverage.models import CoverageMeta, CoverageReport
from autoswitchbot import celery_app

logger = logging.getLogger(__name__)


QUEUE = "coverage_queue"
# QUEUE = "testcase_queue"


@celery_app.task(queue=QUEUE)
def get_covmeta_hash(app_name: str, git_tag: str):
    logger.info(app_name)
    logger.info(git_tag)
    # 获取代码制品
    access_keys = AwsAccessKey.objects.filter(env=EnvLabel.TEST).values(
        "access_key_id", "secret_access_key"
    )
    if not access_keys:
        logger.warn("请先配置基础密钥")
        return
    access_key = access_keys[0]
    logger.info(access_key)
    if git_tag == "cover-v1.3.0.031901":
        git_tag = "v1.3.0.031501-cover"
    bucket_name = "switchbot-publish-platform-test"
    object_key = f"aws_lambda/{app_name}_{git_tag}.zip"
    local_file_name = f"/tmp/{app_name}_{git_tag}.zip"
    extract_dir = f"/tmp/{app_name}_{git_tag}/"
    logger.info(object_key)
    session = boto3.Session(
        region_name=AwsRegion.US,
        aws_access_key_id=access_key["access_key_id"],
        aws_secret_access_key=access_key["secret_access_key"],
    )
    s3 = session.client("s3")
    try:
        # 下载文件
        # resp = s3.list_objects_v2(Bucket=bucket_name, Prefix=object_key)
        # logger.info(resp)
        s3.download_file(bucket_name, object_key, local_file_name)
        logger.info(
            f"Downloaded {object_key} from S3 bucket {bucket_name} to {local_file_name}"
        )
        # bootstrap = "bootstrap"
        # main_file = "main"
        # with zipfile.ZipFile(local_file_name, "r") as zip_ref:
        #     if main_file in zip_ref.namelist():
        #         # 解压指定文件到当前目录
        #         zip_ref.extract(main_file, extract_dir)
        #         logger.info(f"Extracted {main_file}")
        #     if bootstrap in zip_ref.namelist():
        #         # 解压指定文件到当前目录
        #         zip_ref.extract(bootstrap, extract_dir)
        #         logger.info(f"Extracted {bootstrap}")
        #     else:
        #         logger.info(f"{bootstrap} not found in the ZIP archive.")
        # 从制品中运行main --hash，得到 covmeta 后面的 hash 值
        # TODO: worker的架构和 main 可执行文件架构问题，另外是 linux so 链接问题
        # exec_path = os.path.join(extract_dir, bootstrap)
        # main_path = os.path.join(extract_dir, main_file)
        # cmd = f"cd {extract_dir} && chmod +x {main_path} && ./{main_file} --hash"
        # ret_code, output = subprocess.getstatusoutput(cmd)
        # logger.info("%s %s", ret_code, output)
        # for filename in os.listdir(extract_dir):
        #     # 检查文件名是否为'covmeta'
        #     if filename.startswith("covmeta"):
        #         # 使用pathlib获取文件后缀
        #         suffix = Path(filename).suffix
        #         logger.info(suffix)
        #         # 如果存在，更新字段
        #         CoverageMeta.objects.filter(app_name=app_name, git_tag=git_tag).update(
        #             covmeta=suffix[1:]
        #         )
    except BotoCoreClientError as e:
        if e.response["Error"]["Code"] == "404":
            logger.error("The object does not exist.")
        else:
            raise


@celery_app.task(queue=QUEUE)
def get_covcount_and_report(
    app_name: str, env: str, region: str, start_time: datetime, end_time: datetime
):
    # 获取代码制品
    access_keys = AwsAccessKey.objects.filter(env=env).values(
        "access_key_id", "secret_access_key"
    )
    if not access_keys:
        logger.warn(f"请先配置基础密钥 {env}")
        return
    access_key = access_keys[0]
    logger.info(access_key)
    # if app_name.startswith("lambda_"):
    coverage_buckets = CoverageBucket.objects.filter(
        env=env, type=CoverageType.GO_LAMBDA, region=region
    ).values("bucket_name")
    if not coverage_buckets:
        logger.warn(f"请先配置覆盖率存储桶信息 {env} {region}")
        return
    bucket_name = coverage_buckets[0]["bucket_name"]
    if env.startswith("test"):  # test_1 lambda 部署后也是 test_lambda_xxx
        prefix = "test"
    elif env.startswith("prod"):  # prod_cn lambda 部署后也是 prod_lambda_xxx
        prefix = "prod"
    else:
        prefix = env
    key_prefix = f"{prefix}_{app_name}/"
    logger.info(key_prefix)
    session = boto3.Session(
        region_name=AwsRegion.US,
        aws_access_key_id=access_key["access_key_id"],
        aws_secret_access_key=access_key["secret_access_key"],
    )
    s3 = session.client("s3")
    tz = ZoneInfo("Asia/Shanghai")
    start_time_tz = start_time.astimezone(tz)  # 直接示为+8区的时间
    end_time_tz = end_time.astimezone(tz)
    logger.info("%s %s", start_time, end_time)
    resp = s3.list_objects_v2(Bucket=bucket_name, Prefix=key_prefix)
    # logger.info(resp)
    object_keys = []
    for item in resp.get("Contents"):
        last_modified: datetime = item["LastModified"]
        last_modified = last_modified.astimezone(tz)  # 默认+0 cst，转成+8时区的时间
        if start_time_tz <= last_modified < end_time_tz:
            object_keys.append(item["Key"])
    if not object_keys:
        logger.error("期间暂无覆盖率文件产生")
        return
    logger.info(f"{object_keys}")
    meta_hashs = []
    for object_key in object_keys:
        hash_value = object_key.split(".")[1]
        meta_hashs.append(hash_value)
    if len(meta_hashs) > 1:
        logger.warning(f"{meta_hashs}")
    CoverageReport.objects.filter(
        app_name=app_name,
        env=env,
        region=region,
        start_time=start_time,
        end_time=end_time,
    ).update(covcount=len(object_keys))
    meta_hash = meta_hashs[0]
    covmeta_file = f"{key_prefix}covmeta.{meta_hash}"
    logger.info(covmeta_file)
    # 列出要下载的文件
    download_object_keys = object_keys + [covmeta_file]
    task_id = get_current_task().request.id
    dst_dir = f"/tmp/{task_id}"
    # clone 代码仓库指定 tag 代码, 先 clone，这时候是空目录
    repo_infos = CoverageMeta.objects.filter(
        app_name=app_name, covmeta=meta_hash
    ).values("git_repo", "git_tag")
    if not repo_infos:
        logger.error(f"无法确定{app_name} {meta_hash}代码分支")
        return
    git_repo = repo_infos[0]["git_repo"]
    git_tag = repo_infos[0]["git_tag"]
    pri_key = """************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""
    temp_file = tempfile.NamedTemporaryFile(mode="w", delete=False)
    try:
        # 写入文本
        temp_file.write(pri_key)
        temp_file.close()
        # 设置文件权限为600
        os.chmod(temp_file.name, 0o600)
        git.Repo.clone_from(
            git_repo,
            dst_dir,
            branch=git_tag,
            env={
                "GIT_SSH_COMMAND": f"ssh -i {temp_file.name} -o IdentitiesOnly=yes -o StrictHostKeyChecking=no "
            },
        )
        # 下载期间内的 covcounter文件和 covmeta 文件
        for object_key in download_object_keys:
            download_s3_file(s3, bucket_name, object_key, dst_dir)

        ret_code, output = subprocess.getstatusoutput(
            f"cd {dst_dir} && go tool covdata textfmt -i=. -o=cov.txt"
        )
        logger.info("%s %s", ret_code, output)
        ret_code, output = subprocess.getstatusoutput(
            f"cd {dst_dir} && go tool cover -func=cov.txt"
        )
        logger.info("%s %s", ret_code, output)
        if ret_code == 0:
            CoverageReport.objects.filter(
                app_name=app_name,
                env=env,
                region=region,
                start_time=start_time,
                end_time=end_time,
            ).update(cov=output)
        ret_code, output = subprocess.getstatusoutput(
            f"cd {dst_dir} && go tool cover -html=cov.txt -o coverage.html"
        )
        logger.info("%s %s", ret_code, output)
        if ret_code == 0:
            with open(f"{dst_dir}/coverage.html", "r") as f:
                CoverageReport.objects.filter(
                    app_name=app_name,
                    env=env,
                    region=region,
                    start_time=start_time,
                    end_time=end_time,
                ).update(covhtml=f.read())
    except Exception as e:
        logger.error(e)
    # finally:
    #     os.unlink(temp_file.name)


def download_s3_file(s3_client, bucket_name: str, object_key: str, local_path: str):
    if not os.path.exists(local_path):
        os.makedirs(local_path, exist_ok=True)
    file_name = os.path.basename(object_key)
    local_file_name = os.path.join(local_path, file_name)
    s3_client.download_file(bucket_name, object_key, local_file_name)
    logger.info(
        f"Downloaded {object_key} from S3 bucket {bucket_name} to {local_file_name}"
    )
