from allauth.socialaccount.models import SocialAccount
from dj_rest_auth.serializers import UserDetailsSerializer
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import extend_schema_field
from rest_framework import serializers


class UserSerializer(serializers.ModelSerializer):
    extra_data = serializers.SerializerMethodField(method_name="get_extra_data")

    @extend_schema_field(OpenApiTypes.OBJECT)
    def get_extra_data(self, obj):
        social_account = SocialAccount.objects.get(user_id=obj.id)
        return social_account.extra_data

    class Meta(UserDetailsSerializer.Meta):
        fields = UserDetailsSerializer.Meta.fields + ("extra_data",)

    # def update(self, instance, validated_data):
    #     # extra_data not support update
    #     instance = super().update(instance, validated_data)
    #     return instance
