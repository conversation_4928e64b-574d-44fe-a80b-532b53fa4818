import logging

from allauth.socialaccount.helpers import complete_social_login
from allauth.socialaccount.providers.base import AuthError
from allauth.socialaccount.providers.feishu.client import FeishuOAuth2Client
from allauth.socialaccount.providers.feishu.views import Feishu<PERSON>uth2Adapter
from allauth.socialaccount.providers.oauth2.views import OAuth2View
from django.http import HttpResponseRedirect
from rest_framework.permissions import AllowAny
from rest_framework.request import Request
from rest_framework.views import APIView

logger = logging.getLogger(__name__)


class FeishuLoginAdapter(FeishuOAuth2Adapter):
    client_class = FeishuOAuth2Client

    def get_callback_url(self, request, app):
        callback_url = request.GET.get("callback_url")
        logger.info(callback_url)
        return callback_url


class FeishuOAuth2CallbackView(OAuth2View):
    # OAuth2CallbackView
    adapter_class = FeishuLoginAdapter

    def __init__(self):
        self.request: Request
        self.adapter: <PERSON><PERSON>u<PERSON>ogin<PERSON>dapter

    def dispatch(self, request: Request, *args, **kwargs):
        callback_url = request.GET.get("callback_url", None)
        provider = self.adapter.get_provider()
        if "error" in request.GET or "code" not in request.GET:
            # Distinguish cancel from error
            auth_error = request.GET.get("error", None)
            if auth_error == self.adapter.login_cancelled_error:
                error = AuthError.CANCELLED
            else:
                error = AuthError.UNKNOWN
            logger.error(error)
            return HttpResponseRedirect(callback_url)
        app = provider.app
        client = self.get_client(self.request, app)

        try:
            access_token = self.adapter.get_access_token_data(request, app, client)
            logger.info(access_token)
            token = self.adapter.parse_token(access_token)
            if app.pk:
                token.app = app
            login = self.adapter.complete_login(
                request, app, token, response=access_token
            )
            login.token = token
            return complete_social_login(request, login)
        except Exception as e:
            logger.error(e, exc_info=True)
            logger.info(f"will back to {callback_url}")
            return HttpResponseRedirect(callback_url)


oauth2_callback = FeishuOAuth2CallbackView.adapter_view(FeishuLoginAdapter)


class PasswordResetConfirmView(APIView):
    """
    proxy for front-end password reset page
    """

    permission_classes = [AllowAny]

    def get(self, request: Request, uidb64: str, token: str):
        # return HttpResponseRedirect("http://localhost:8001/user/reset-password?uid={}&token={}".format(uidb64, token))
        return HttpResponseRedirect(
            "/user/reset-password?uid={}&token={}".format(uidb64, token)
        )
