from allauth.account.utils import user_email
from allauth.utils import valid_email_or_none
from django.conf import settings
from allauth.account.adapter import DefaultAccountAdapter
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter
from allauth.core import context
from urllib.parse import urlparse
from rest_framework.authentication import SessionAuthentication
import logging

logger = logging.getLogger(__name__)


class MyAccountAdapter(DefaultAccountAdapter):

    def is_safe_url(self, url):
        try:
            from django.utils.http import url_has_allowed_host_and_scheme
        except ImportError:
            from django.utils.http import (
                is_safe_url as url_has_allowed_host_and_scheme,
            )

        # make safe for front-end urls
        allowed_hosts = {context.request.get_host()}.union(set(settings.ALLOWED_HOSTS))
        # logger.info(allowed_hosts)

        if "*" in allowed_hosts:
            parsed_host = urlparse(url).netloc
            allowed_host = {parsed_host} if parsed_host else None
            return url_has_allowed_host_and_scheme(url, allowed_hosts=allowed_host)

        return url_has_allowed_host_and_scheme(url, allowed_hosts=allowed_hosts)

    def add_message(
            self,
            request,
            level,
            message_template,
            message_context=None,
            extra_tags="",
    ):
        pass  # remove message in cookies

    def should_send_confirmation_mail(self, request, email_address):
        pass  # skip send confirmation mail


class MySocialAccountAdapter(DefaultSocialAccountAdapter):
    def populate_user(self, request, sociallogin, data):
        user = super().populate_user(request, sociallogin, data)
        # update email from sociallogin
        email = sociallogin.account.extra_data.get('email', '')
        user_email(user, valid_email_or_none(email) or "")
        return user


class CsrfExemptSessionAuthentication(SessionAuthentication):

    def enforce_csrf(self, request):
        return
