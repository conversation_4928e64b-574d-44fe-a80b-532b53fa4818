import logging

from django.db import transaction

logger = logging.getLogger(__name__)


def batch_delete(queryset, model, batch_size=1000):
    total = 0
    while True:
        # 分批获取并删除
        batch = list(queryset[:batch_size].values_list("id", flat=True))
        if not batch:
            break
        # 手动提交事务（关闭自动提交）
        with transaction.atomic():
            model.objects.filter(id__in=batch).delete()
            total += len(batch)
            logger.info(f"已删除 {total} 条记录")
    logger.info("删除完成")
