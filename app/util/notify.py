import logging
import os
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict
from urllib.parse import urljoin

import requests
from django.conf import settings
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger(__name__)


class NotificationType(Enum):
    """通知类型"""

    SUCCESS = "success"
    FAILURE = "failure"
    DEBUG = "debug"


class NotificationConfig:
    """通知配置"""

    WEBHOOKS = {
        NotificationType.SUCCESS: "a38c886e-b6dd-4d7f-9af8-d2ec560efa5c",
        NotificationType.FAILURE: "bdfc5f7d-c376-4459-8a44-d7dae036cde2",
        NotificationType.DEBUG: "74edb805-fdc8-4dcf-949b-becf92a28ccc",
    }
    FEISHU_BASE_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/"

    @classmethod
    def get_webhook_url(cls, notification_type: NotificationType) -> str:
        """获取webhook URL"""
        return urljoin(cls.FEISHU_BASE_URL, cls.WEBHOOKS[notification_type])


def format_duration(seconds: float) -> str:
    """格式化持续时间"""
    td = timedelta(seconds=seconds)

    if td.total_seconds() >= 3600:
        return str(td)
    elif td.total_seconds() >= 60:
        return f"{td.seconds // 60:02}:{td.seconds % 60:02}"
    else:
        return f"{td.seconds}s"


def create_notification_card(
    env_name: str,
    report_name: str,
    success: bool,
    stats: Dict,
    time_info: Dict,
    task_id: str,
) -> Dict:
    """创建通知卡片"""
    start_time = datetime.fromtimestamp(time_info["start_at"]).strftime(
        "%Y-%m-%d %H:%M:%S"
    )
    duration = format_duration(time_info["duration"])

    testcase_stats = stats["testcases"]
    teststep_stats = stats["teststeps"]

    return {
        "msg_type": "interactive",
        "card": {
            "header": {
                "template": "blue" if success else "red",
                "title": {
                    "tag": "plain_text",
                    "content": f"【{env_name}】[{report_name}] 测试结果: {'成功🎉' if success else '失败😭'}",
                },
            },
            "elements": [
                {
                    "tag": "column_set",
                    "columns": [
                        {
                            "tag": "column",
                            "width": "weighted",
                            "elements": [
                                {
                                    "tag": "markdown",
                                    "content": (
                                        f"**开始时间**: {start_time} (时长: {duration})\n"
                                        f"**用例总数**: {testcase_stats['total']} "
                                        f"<text_tag color='green'>{testcase_stats['success']}</text_tag>/"
                                        f"<text_tag color={'red' if testcase_stats['fail'] else 'blue'}>{testcase_stats['fail']}</text_tag>\n"
                                        f"**成功步骤/已执行**: "
                                        f"<text_tag color='green'>{teststep_stats['successes']}</text_tag>/"
                                        f"<text_tag>{teststep_stats['total']}</text_tag>"
                                    ),
                                }
                            ],
                        },
                        {
                            "tag": "column",
                            "width": "auto",
                            "elements": [
                                {
                                    "tag": "button",
                                    "text": {
                                        "tag": "plain_text",
                                        "content": "查看详情",
                                    },
                                    "type": "primary" if success else "danger",
                                    "url": f"{settings.FRONTEND_HOST}/preview/testreport/{task_id}",
                                }
                            ],
                        },
                    ],
                }
            ],
        },
    }


@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10),
    reraise=True,
)
def send_feishu_notification(webhook_url: str, message: Dict) -> None:
    """发送飞书通知，失败自动重试"""
    try:
        response = requests.post(webhook_url, json=message, timeout=5)
        response.raise_for_status()
        logger.info("测试报告已发送到飞书")
    except requests.exceptions.RequestException as e:
        logger.error("发送飞书通知失败: %s", str(e))
        raise


def notify_test_result(
    task_id: str, env_name: str, report_name: str, summary: Dict, notify: bool = False
) -> None:
    """发送测试结果通知"""
    if not summary:
        return

    # 创建通知卡片
    card = create_notification_card(
        env_name=env_name,
        report_name=report_name,
        success=summary["success"],
        stats=summary["stat"],
        time_info=summary["time"],
        task_id=task_id,
    )

    # 发送调试通知
    debug_webhook = NotificationConfig.get_webhook_url(NotificationType.DEBUG)
    send_feishu_notification(debug_webhook, card)

    # 发送正式通知
    if notify:
        notification_type = (
            NotificationType.SUCCESS if summary["success"] else NotificationType.FAILURE
        )
        webhook_url = NotificationConfig.get_webhook_url(notification_type)
        send_feishu_notification(webhook_url, card)


if __name__ == "__main__":
    # 测试代码
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "autoswitchbot.settings")
    test_args = {
        "task_id": "test_id",
        "report_name": "测试报告",
        "env_name": "prod(ap-northeast-1)",
        "summary": {
            "success": True,
            "stat": {
                "testcases": {"total": 3, "success": 3, "fail": 0},
                "teststeps": {"total": 9, "successes": 9},
            },
            "time": {"start_at": 1732589105.259302, "duration": 2.3986358642578125},
        },
    }
    notify_test_result(**test_args)
    test_args = {
        "task_id": "test_id",
        "report_name": "失败测试报告",
        "env_name": "prod(us-east-1)",
        "summary": {
            "success": False,
            "stat": {
                "testcases": {"total": 3, "success": 3, "fail": 1},
                "teststeps": {"total": 9, "successes": 8},
            },
            "time": {"start_at": 1732589105.259302, "duration": 2.3986358642578125},
        },
    }
    notify_test_result(**test_args)
