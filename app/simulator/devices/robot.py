import random


class Robot:
    def robot_task(self, *args, **kwargs):
        raise NotImplementedError


def generate_random_float(min_val=-4, max_val=4):
    return round(random.uniform(min_val, max_val), 3)


class WoSweeperOriginRobot(Robot):
    def __init__(self) -> None:
        super().__init__()
        self.mac = "B0E9FE00048C"
        self._type = "WoSweeperOrigin"

    def publish(self, mqtt_connection, topic, stop_index=100):
        pass

    def robot_task(self, task_id: str, area: int, speed: int, total_time: int):
        """
        func 1001

        interval 1, num 2,  worke_time ++
        cur position: lastest position

        "type": "robot_task", # v1_1/WoSweeperOrigin/B0E9FE00048C/taskPose/320292aa-4588-41a8-b482-f109b7068ec0   /message mapUpdate
        "param": {"area": 100, "speed": 100}


        """
        pass
