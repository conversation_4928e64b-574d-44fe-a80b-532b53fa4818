from django.urls import path

from .views import (
    CheckS3PolicyView,
    S3UploadMapView,
    SimulateAliasTypeEnumAPIView,
    SimulateDeviceCallbackConfigDetailAPIView,
    SimulateDeviceCallbackConfigListCreateAPIView,
    SimulateDeviceDetailAPIView,
    SimulateDeviceFuncView,
    SimulateDeviceListCreateAPIView,
    SimulateDevicePropSetView,
    SimulateDeviceTypeDetailAPIView,
    SimulateDeviceTypeListCreateAPIView,
    SimulateProductEnumAPIView,
    SimulateStatusCommandAPIView,
    SimulateSupportStatusAPIView,
)

urlpatterns = [
    path(
        "product",
        SimulateProductEnumAPIView.as_view(),
        name="simulate-product-enum-list",
    ),
    path(
        "support_type",
        SimulateDeviceTypeListCreateAPIView.as_view(),
        name="simulate-device-type-list",
    ),
    path(
        "support_type/<str:alias>",
        SimulateDeviceTypeDetailAPIView.as_view(),
        name="simulate-device-type-detail",
    ),
    path(
        "alias_type",
        SimulateAliasTypeEnumAPIView.as_view(),
        name="simulate-aliastype-enum-list",
    ),
    path(
        "device", SimulateDeviceListCreateAPIView.as_view(), name="simulate-device-list"
    ),
    path(
        "device/<str:mac>",
        SimulateDeviceDetailAPIView.as_view(),
        name="simulate-device-detail",
    ),
    # 显示支持的状态操作列表
    path(
        "support_status/<str:alias>",
        SimulateSupportStatusAPIView.as_view(),
        name="simulate-device-support-status-command",
    ),
    path(
        "command",
        SimulateStatusCommandAPIView.as_view(),
        name="simulate-device-command",
    ),
    path("props", SimulateDevicePropSetView.as_view(), name="simulate-device-propset"),
    path("funcs", SimulateDeviceFuncView.as_view(), name="simulate-device-funcs"),
    path(
        "callback_config",
        SimulateDeviceCallbackConfigListCreateAPIView.as_view(),
        name="simulate-device-callback-config-list",
    ),
    path(
        "callback_config/<str:id>",
        SimulateDeviceCallbackConfigDetailAPIView.as_view(),
        name="simulate-device-callback-config-detail",
    ),
    path(
        "s3_policy",
        CheckS3PolicyView.as_view(),
        name="s3-checkpolicy",
    ),
    path(
        "upload_map",
        S3UploadMapView.as_view(),
        name="simulate-device-s3-uploadmap",
    ),
]
