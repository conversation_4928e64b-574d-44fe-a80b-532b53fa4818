import json
import logging
import os
import tempfile
from datetime import datetime
from typing import Any, Dict, List, Tuple, cast

import requests
from django.conf import settings
from django.db import models
from redis import Redis

from app.models import BaseModel

logger = logging.getLogger(__name__)


class SwitchBotProduct(models.TextChoices):
    SweeperOrigin = "Floor Cleaning Robot S10", "WoSweeperOrigin"
    Hub2 = "Hub 2", "Hub2"  # WoHub2
    Bot = "Bot", "Bot"  # WoHand


SwitchBotDeviceChoices = [(key, key) for key in SwitchBotProduct.labels]


class AwsRegion(models.TextChoices):
    US = "us-east-1"
    AP = "ap-northeast-1"
    EU = "eu-central-1"


class SwitchBotStatus(models.TextChoices):
    Online = "on"
    Offline = "off"


class SwitchBotEnv(models.TextChoices):
    TEST = "test"
    PROD = "prod"


class SimulateDeviceType(BaseModel):
    # 同一个设备类型，可能同时配置了不同的物模型测试用
    alias = models.CharField(
        max_length=128, db_comment="设备类型别名", primary_key=True
    )
    type = models.CharField(
        max_length=64, db_comment="设备类型", choices=SwitchBotDeviceChoices
    )
    product_name = models.CharField(
        max_length=255, db_comment="产品名称", choices=SwitchBotProduct.choices
    )
    properties = models.JSONField(db_comment="设备物模型属性")

    class Meta:
        db_table = "simulate_device_type"


class SimulateDevice(BaseModel):
    mac = models.CharField(max_length=64, db_comment="设备mac地址", primary_key=True)
    region = models.CharField(
        max_length=32,
        db_comment="设备所在区域",
        choices=AwsRegion.choices,
        default=AwsRegion.US,
    )
    env = models.CharField(
        max_length=12,
        db_comment="设备环境",
        choices=SwitchBotEnv.choices,
        default=SwitchBotEnv.TEST,
    )
    is_sub = models.BooleanField(default=False, db_comment="是否是子设备")
    status = models.CharField(
        max_length=8,
        db_comment="设备状态",
        choices=SwitchBotStatus.choices,
        default=SwitchBotStatus.Online,
    )
    alias_type = models.CharField(max_length=128, db_comment="设备类型别名")
    product_name = models.CharField(
        max_length=255, db_comment="产品名称", choices=SwitchBotProduct.choices
    )
    type = models.CharField(
        max_length=64, db_comment="设备类型", choices=SwitchBotDeviceChoices
    )
    belong_user = models.CharField(
        max_length=64, db_comment="物模型设备所属用户id, client_id使用", blank=True
    )
    cert_pem = models.TextField(db_comment="iot私钥", blank=True)
    key_pem = models.TextField(db_comment="iot公钥", blank=True)

    class Meta:
        db_table = "simulate_device"


class SimulateDeviceCallbackConfig(BaseModel):
    name = models.CharField(max_length=64, db_comment="配置名称")
    mac = models.CharField(max_length=64, db_comment="设备mac地址")
    function_id = models.IntegerField(db_comment="物模型方法id")
    callback_template = models.JSONField(db_comment="物模型回复方法模板")

    """
    {
    "return": [xxx,xxx],
    "variables": {
    "task_id": "xxx"
    }
    "actions": [
    {
    "type": "props",
    "value": {

    },
    {
    "type": "robot_task",
    "param": {"area": 100, "speed": 100, "angle": 1}
    },
    "actions": [
    {
    "type": "props",
    "value": {

    },
    ]
    }
    """

    class Meta:
        db_table = "simulate_device_callback_config"


class SimulateDeviceProperty(models.Model):
    """虚拟设备接收到的属性变更"""

    mac = models.CharField(max_length=64, db_comment="mqtt client的mac地址")
    device_id = models.CharField(
        max_length=64, null=True, db_comment="mqtt消息的设备ID"
    )
    region = models.CharField(
        max_length=64,
        db_comment="mqtt消息的所在区域",
        choices=AwsRegion.choices,
        default=AwsRegion.US,
    )
    field = models.CharField(max_length=64, null=True, db_comment="物模型属性名称")
    field_id = models.CharField(max_length=64, db_comment="物模型属性ID")
    field_value = models.JSONField(db_comment="物模型属性值")
    seq = models.IntegerField(db_comment="物模型属性序号", default=0)
    mqtt_time = models.DateTimeField(db_comment="mqtt消息中的时间戳", null=True)
    from_server = models.BooleanField(default=False, db_comment="是否来自服务器")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "simulate_device_property"


class SimulateTerminalProperty(models.Model):
    """虚拟终端(APP) 接收到的属性变更，或notifyAll"""

    user_id = models.CharField(max_length=64, db_comment="topic中userid")
    mac = models.CharField(max_length=64, db_comment="topic中mac地址")
    device_id = models.CharField(
        max_length=64, null=True, db_comment="mqtt消息的设备ID"
    )
    region = models.CharField(
        max_length=64,
        db_comment="mqtt消息的所在区域",
        choices=AwsRegion.choices,
        default=AwsRegion.US,
    )
    field = models.CharField(max_length=64, null=True, db_comment="物模型属性名称")
    field_id = models.CharField(max_length=64, db_comment="物模型属性ID")
    field_value = models.JSONField(db_comment="物模型属性值")
    seq = models.IntegerField(db_comment="物模型属性序号", default=0)
    mqtt_time = models.DateTimeField(db_comment="mqtt消息中的时间戳", null=True)
    source = models.CharField(
        max_length=64, null=True, db_comment="属性来源,notify, notifyAll"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "simulate_terminal_property"


class SimulateDeviceFunc(models.Model):
    """虚拟设备收到的funcRequest"""

    mac = models.CharField(max_length=64, db_comment="topic的mac地址")
    device_id = models.CharField(
        max_length=64, null=True, db_comment="mqtt消息的设备ID"
    )
    region = models.CharField(
        max_length=64,
        db_comment="mqtt消息的所在区域",
        choices=AwsRegion.choices,
        default=AwsRegion.US,
    )
    field = models.CharField(max_length=64, null=True, db_comment="物模型方法名称")
    func_id = models.IntegerField(db_comment="物模型方法ID")
    request_id = models.CharField(max_length=64, db_comment="物模型方法请求ID")
    params = models.JSONField(db_comment="方法请求/响应参数")
    mqtt_time = models.DateTimeField(db_comment="mqtt消息中的时间戳", null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "simulate_device_func"


class SimulateTerminalFunc(models.Model):
    """func_invoke 收到的mqtt回复"""

    user_id = models.CharField(max_length=64, db_comment="topic中userid")
    client_id = models.CharField(max_length=64, db_comment="topic中client_id")
    device_id = models.CharField(
        max_length=64, null=True, db_comment="mqtt消息的设备ID"
    )
    region = models.CharField(
        max_length=64,
        db_comment="mqtt消息的所在区域",
        choices=AwsRegion.choices,
        default=AwsRegion.US,
    )
    field = models.CharField(max_length=64, null=True, db_comment="物模型方法名称")
    func_id = models.IntegerField(db_comment="物模型方法ID")
    request_id = models.CharField(max_length=64, db_comment="物模型方法请求ID")
    params = models.JSONField(db_comment="方法请求/响应参数")
    result_code = models.JSONField(db_comment="响应码")
    mqtt_time = models.DateTimeField(db_comment="mqtt消息中的时间戳", null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "simulate_terminal_func"


class SimulateTerminal(BaseModel):
    """虚拟终端 用于接收发给app的消息

    APP_Android_bfa28ca0-f50b-4662-9306-aac1cf442dbc_8c35-c112-e951-87e2

    虚拟终端这里终端号直接为空串
    APP_TEST_bfa28ca0-f50b-4662-9306-aac1cf442dbc_

    v1_1/{user_id}/+/notifyAllProperty
    v1_1/{user_id}/+/propertyChanged
    """

    user_id = models.CharField(max_length=64, db_comment="用户ID")
    terminal_id = models.CharField(max_length=64, db_comment="终端ID")
    region = models.CharField(
        max_length=64,
        db_comment="终端所在区域",
        choices=AwsRegion.choices,
        default=AwsRegion.US,
    )
    env = models.CharField(
        max_length=12,
        db_comment="终端环境",
        choices=SwitchBotEnv.choices,
        default=SwitchBotEnv.TEST,
    )
    status = models.CharField(
        max_length=8,
        db_comment="终端状态",
        choices=SwitchBotStatus.choices,
        default=SwitchBotStatus.Online,
    )

    class Meta:
        unique_together = (("user_id", "env"),)
        db_table = "simulate_terminal"


# Functions


def get_app_client_id(user_id: str, terminal_id: str) -> str:
    return f"APP_TEST_{user_id}_{terminal_id}"


def get_client_id(mac: str, device_type: str) -> str:
    if device_type in {"HUB2"}:
        return f"{device_type}-{mac}"
    elif device_type in {"WoSweeperOrigin"}:
        # mac主键，只会有一个
        user_id = ""
        user_ids = SimulateDevice.objects.filter(mac=mac).values_list(
            "belong_user", flat=True
        )
        if user_ids:
            user_id = user_ids[0]
        return f"Thing_{user_id}_{mac}"
    else:
        return f"BLE-{mac}"


# 查询特定mac和function_id的最新记录
def get_latest_callback_template(mac, function_id):
    latest_callback_template = (
        SimulateDeviceCallbackConfig.objects.filter(mac=mac, function_id=function_id)
        .order_by("-updated_at")
        .values("callback_template")
    )
    if latest_callback_template:
        return latest_callback_template[0]
    return {}


def get_online_devices():
    return SimulateDevice.objects.filter(
        is_deleted=False, status=SwitchBotStatus.Online
    ).values("mac", "env", "region", "type")


def get_online_terminals():
    return SimulateTerminal.objects.filter(
        is_deleted=False, status=SwitchBotStatus.Online
    ).values("user_id", "terminal_id", "region", "env")


def get_alias_types():
    return SimulateDeviceType.objects.filter(
        is_deleted=False, type__in=SwitchBotProduct.labels
    ).values_list("alias", flat=True)


def get_alias_type_status_command(alias_type: str) -> Dict[str, Dict]:
    properties = SimulateDeviceType.objects.filter(alias=alias_type).values(
        "properties"
    )
    if not properties:
        return {}
    properties_content: List[Dict] = properties[0]["properties"]
    if not isinstance(properties_content, list):
        logger.info("not a list object")
        return {}
    properties_fields = {}
    for properties in properties_content:
        field = properties.get("field")
        _type = properties.get("data_type", {}).get("type")
        props = {
            "field": field,
            "type": _type,
        }
        if _type in ("enum", "bool"):
            specs = properties.get("data_type", {}).get("specs", {})
            options = [{"label": value, "value": key} for key, value in specs.items()]
            props.setdefault("options", options)
        if _type == "int":
            specs = properties.get("data_type", {}).get("specs", {})
            for key, value in specs.items():
                props.setdefault(key, value)
        properties_fields.setdefault(properties.get("identifier"), props)

    # logger.info(properties_fields)
    return properties_fields


def get_identifier_map(mac: str) -> Tuple[Dict, Dict]:
    # 查询simulate device对象获取alias，使用alias查询属性列表
    alias = SimulateDevice.objects.get(mac=mac).alias_type
    field_map = get_alias_type_status_command(alias)
    identifier_map = {}
    field_id_map = {}
    for identifier, field_item in field_map.items():
        field_id = field_item.get("field")
        identifier_map.setdefault(identifier, field_id)
        field_id_map.setdefault(field_id, identifier)
    # logger.info(identifier_map)
    return identifier_map, field_id_map


def save_property_by_client(mac: str, region: str, field_id: str, value: Any):
    _, field_id_map = get_identifier_map(mac)
    field = field_id_map.get(field_id)
    SimulateDeviceProperty.objects.create(
        mac=mac,
        region=region,
        device_id=mac,
        field=field,
        field_id=field_id,
        field_value=value,
    )


def save_property_set(device_type: str, mac: str, region: str, message: str):
    # 毫秒时间戳
    try:
        payload = json.loads(message)
        device_id = payload.get("deviceID")
        set_payload = payload.get("payload", {})
        seq = set_payload.get("seq")
        timestamp = set_payload.get("timestamp")
        _property = set_payload.get("property", {})
        _, field_id_map = get_identifier_map(mac)
        property_objs = []
        for field_id, value in _property.items():
            field = field_id_map.get(field_id)
            property_objs.append(
                SimulateDeviceProperty(
                    mac=mac,
                    device_id=device_id,
                    region=region,
                    field=field,
                    field_id=field_id,
                    field_value=value,
                    from_server=True,
                    seq=seq,
                    mqtt_time=datetime.fromtimestamp(timestamp / 1000),
                )
            )
        # 批量把接收到后端发送的propertySet存入数据库
        SimulateDeviceProperty.objects.bulk_create(property_objs)
    except Exception as e:
        logger.error(e)


def save_property_to_user(
    user_id: str,
    mac: str,
    device_id: str,
    region: str,
    seq: int,
    mqtt_time: int,
    source: str,
    _property: dict,
):
    # 毫秒时间戳
    # logger.info(f"{mac}, {device_id}, {region}")
    try:
        _, field_id_map = get_identifier_map(mac)
        property_objs = []
        for field_id, value in _property.items():
            field = field_id_map.get(field_id)
            property_objs.append(
                SimulateTerminalProperty(
                    user_id=user_id,
                    mac=mac,
                    device_id=device_id,
                    region=region,
                    field=field,
                    field_id=field_id,
                    field_value=value,
                    source=source,
                    seq=seq,
                    mqtt_time=datetime.fromtimestamp(mqtt_time / 1000),
                )
            )
        # 批量把通知给用户的所有属性存入数据库
        SimulateTerminalProperty.objects.bulk_create(property_objs)
    except Exception as e:
        logger.error(e)


def save_notify_all_to_user(
    user_id: str,
    mac: str,
    device_id: str,
    region: str,
    source: str,
    _property: dict,
):
    # 毫秒时间戳
    # {"1003":{"id":1003,"type":0,"value":true,"seq":0,"timestamp":1705911324131,"statusStartTimestamp":0,"multiUser":false}
    try:
        _, field_id_map = get_identifier_map(mac)
        property_objs = []
        for field_id, field_item in _property.items():
            field = field_id_map.get(field_id)
            property_objs.append(
                SimulateTerminalProperty(
                    user_id=user_id,
                    mac=mac,
                    device_id=device_id,
                    region=region,
                    field=field,
                    field_id=field_id,
                    field_value=field_item.get("value"),
                    source=source,
                    seq=field_item.get("seq"),
                    mqtt_time=datetime.fromtimestamp(
                        field_item.get("timestamp") / 1000
                    ),
                )
            )
        # 批量把通知给用户的所有属性存入数据库
        SimulateTerminalProperty.objects.bulk_create(property_objs)
    except Exception as e:
        logger.error(e)


def save_func_request(device_type: str, mac: str, region: str, message: str):
    # 保存发给设备的方法请求
    # {"code":1,"deviceID":"B0E9FE00048C","payload":{"functionID":1009,"params":{"0":0},"requestID":"F28A522A-AAF0-440B-BB8F-4876C57E75E8","timestamp":1702348464121},"sign":"","version":"1.0"}
    try:
        # _, field_id_map = get_identifier_map(mac)
        payload = json.loads(message)
        device_id = payload.get("deviceID")
        req_payload = payload.get("payload", {})
        function_id = req_payload.get("functionID")
        params = req_payload.get("params")
        request_id = req_payload.get("requestID")
        timestamp = req_payload.get("timestamp")
        SimulateDeviceFunc.objects.create(
            mac=mac,
            device_id=device_id,
            region=region,
            func_id=function_id,
            request_id=request_id,
            params=params,
            mqtt_time=datetime.fromtimestamp(timestamp / 1000),
        )
    except Exception as e:
        logger.error(e)


# Redis


def get_redis_client() -> Redis:
    # 连接到Redis服务器
    redis_client = cast(Redis, Redis.from_url(settings.CELERY_BROKER_URL))
    return redis_client


def get_seq_number(mac: str) -> int:
    """
    获取seq_number
    """
    redis_client = get_redis_client()
    new_seq_number = redis_client.incr(f"{mac}_seq")
    return new_seq_number


def get_s3_credentials(mac: str, role_alias_url: str, thingname: str) -> Dict[str, Any]:
    certs = SimulateDevice.objects.filter(mac=mac).values("cert_pem", "key_pem")
    if not certs:
        raise Exception("MAC address not exist")
    cert_pem = certs[0]["cert_pem"]
    key_pem = certs[0]["key_pem"]
    if not cert_pem or not key_pem:
        raise Exception("key_pem or key_pem is null")
    # 创建临时文件来保存证书和密钥
    cert_file = tempfile.NamedTemporaryFile(delete=False)
    key_file = tempfile.NamedTemporaryFile(delete=False)
    cert_file.write(cert_pem.encode())
    key_file.write(key_pem.encode())
    cert_file.close()
    key_file.close()
    try:
        # 准备请求头部
        headers = {"x-amzn-iot-thingname": thingname}
        # 发送请求 不能使用session，因为会复用ssl cert信息
        response = requests.get(
            role_alias_url,
            headers=headers,
            cert=(cert_file.name, key_file.name),
        )
        if response.ok:
            return response.json()
        else:
            raise Exception(response.text)
    finally:
        # 清理临时文件
        os.unlink(cert_file.name)
        os.unlink(key_file.name)
        os.unlink(key_file.name)
        os.unlink(key_file.name)
