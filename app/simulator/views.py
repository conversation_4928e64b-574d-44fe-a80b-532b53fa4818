import logging
import tempfile
import time
from pathlib import Path

import boto3
from botocore.exceptions import ClientError
from django.db.models import Max
from django.utils import timezone
from drf_spectacular.utils import extend_schema
from rest_framework import status
from rest_framework.exceptions import ValidationError
from rest_framework.generics import ListAPIView
from rest_framework.permissions import AllowAny
from rest_framework.request import Request
from rest_framework.views import APIView

from app import views
from app.response import ErrorResponse, SuccessResponse

from ..views import SelfModelMixin
from .models import (
    SimulateDevice,
    SimulateDeviceCallbackConfig,
    SimulateDeviceFunc,
    SimulateDeviceProperty,
    SimulateDeviceType,
    SwitchBotProduct,
    get_alias_type_status_command,
    get_alias_types,
    get_s3_credentials,
)
from .serializers import (
    S3PolicyRequest,
    S3UploadMapRequest,
    SimulateDeviceCallbackConfigSerializer,
    SimulateDeviceFuncSerializer,
    SimulateDevicePropsSerializer,
    SimulateDeviceSerializer,
    SimulateDeviceTypeSerializer,
    StatusCommandRequest,
)
from .service import send_property_report_task

logger = logging.getLogger(__name__)
CUR_PATH = Path(__file__).parent.absolute()


class SimulateDeviceTypeListCreateAPIView(views.ListCreateModelAPIView):
    """虚拟设备类型 列表、新增、批量删除"""

    lookup_field = "alias"  # 批量删除时主键
    queryset = SimulateDeviceType.objects.all()
    serializer_class = SimulateDeviceTypeSerializer


class SimulateDeviceTypeDetailAPIView(views.DetailAPIView):
    """虚拟设备类型单条 详情、修改、删除"""

    lookup_field = "alias"
    queryset = SimulateDeviceType.objects.all()
    serializer_class = SimulateDeviceTypeSerializer


class SimulateDeviceListCreateAPIView(SelfModelMixin, views.ListCreateModelAPIView):
    """虚拟设备 列表、新增、批量删除"""

    lookup_field = "mac"  # 批量删除时主键
    queryset = SimulateDevice.objects.all()
    serializer_class = SimulateDeviceSerializer

    def get_permissions(self):
        """
        单独开放POST接口，给自动化测试用例调用
        """
        if self.request.method == "POST":
            return [AllowAny()]
        return super().get_permissions()

    def post(self, request: Request, *args, **kwargs):
        """
        虚拟设备生成
        1. 使用参数中的类型，生成对应类型的设备，产品名称复制
        2. 随机生成mac(特定格式)，插入防重表，避免线上撞mac
        3. 默认online状态，发送消息到任务服务，启动mqtt客户端监听
        """
        return super().post(request, *args, **kwargs)


class SimulateDeviceDetailAPIView(SelfModelMixin, views.DetailAPIView):
    """虚拟设备单条 详情、修改、删除

    虚拟设备信息修改
    1. 不允许让用户修改mac

    虚拟设备删除
    1. 软删除，其他信息保留
    2. 发送消息，通知mqtt客户端下线
    """

    lookup_field = "mac"
    queryset = SimulateDevice.objects.all()
    serializer_class = SimulateDeviceSerializer

    def get_permissions(self):
        """
        单独开放PUT接口，给自动化测试用例调用
        """
        if self.request.method == "PUT":
            return [AllowAny()]
        return super().get_permissions()


class SimulateProductEnumAPIView(APIView):
    def get(self, request: Request, *args, **kwargs):
        """
        产品类型枚举
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        return SuccessResponse(SwitchBotProduct.values)


class SimulateAliasTypeEnumAPIView(APIView):
    def get(self, request: Request, *args, **kwargs):
        """
        已创建的设备类型别名
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        return SuccessResponse(get_alias_types())


class SimulateSupportStatusAPIView(APIView):
    def get(self, request: Request, *args, **kwargs):
        # 获取路径参数
        alias = kwargs.get("alias", None)
        if not alias:
            return ErrorResponse(
                exception="alias is required", _status=status.HTTP_404_NOT_FOUND
            )
        field_list = get_alias_type_status_command(alias)
        if not field_list:
            return ErrorResponse(
                exception="no command list found", _status=status.HTTP_404_NOT_FOUND
            )
        return SuccessResponse(field_list)


@extend_schema(request=StatusCommandRequest)
class SimulateStatusCommandAPIView(APIView):
    permission_classes = [AllowAny]

    def post(self, request: Request, *args, **kwargs):
        mac = request.data.get("mac", None)
        if not mac:
            return ErrorResponse(
                exception="mac is required", _status=status.HTTP_404_NOT_FOUND
            )
        props = request.data.get("props", None)
        if not props or not isinstance(props, list):
            return ErrorResponse(
                exception="property is required", _status=status.HTTP_404_NOT_FOUND
            )
        _types = SimulateDevice.objects.filter(mac=mac).values_list("type", flat=True)
        if not _types:
            return ErrorResponse(
                exception="device not found", _status=status.HTTP_404_NOT_FOUND
            )
        success, message = send_property_report_task(mac, _types[0], props)
        if success:
            return SuccessResponse(message)
        else:
            return ErrorResponse(exception=message)


class SimulateDevicePropsAPIView(ListAPIView):
    queryset = SimulateDeviceType.objects.all()
    serializer_class = SimulateDevicePropsSerializer

    def list(self, request: Request, *args, **kwargs):
        mac = request.query_params.get("mac", None)
        from_server = request.query_params.get("from_server", True)
        if not mac:
            return ErrorResponse(
                "MAC address is required.", _status=status.HTTP_400_BAD_REQUEST
            )
        # 获取每个field_id的最新记录
        if isinstance(from_server, str):
            from_server = False if from_server.lower() == "false" else True
        latest_records = (
            SimulateDeviceProperty.objects.filter(mac=mac, from_server=from_server)
            .values("field_id")
            .annotate(latest_id=Max("id"))
            .values_list("latest_id", flat=True)
        )
        # 根据上面得到的最新记录ID，获取记录详情
        queryset = SimulateDeviceProperty.objects.filter(id__in=latest_records)
        serializer = self.get_serializer(queryset, many=True)
        props_list = serializer.data
        prop_map = {}
        for prop in props_list:
            # field_value 还需要使用value_type 转回原始的数据类型
            # _type = prop['value_type']
            # prop_map[prop['field']] = prop['field_value']
            prop_map[prop["field_id"]] = prop["field_value"]
        return SuccessResponse(prop_map)


class SimulateDeviceCallbackConfigListCreateAPIView(views.ListCreateModelAPIView):
    """虚拟设备callback配置 列表、新增、批量删除"""

    permission_classes = [AllowAny]  # 自动化用例，新增需要
    queryset = SimulateDeviceCallbackConfig.objects.all()
    serializer_class = SimulateDeviceCallbackConfigSerializer


class SimulateDeviceCallbackConfigDetailAPIView(views.DetailAPIView):
    """虚拟设备callback配置 单条 详情、修改、删除"""

    queryset = SimulateDeviceCallbackConfig.objects.all()
    serializer_class = SimulateDeviceCallbackConfigSerializer


@extend_schema(request=S3PolicyRequest)
class CheckS3PolicyView(APIView):
    permission_classes = [AllowAny]

    def post(self, request: Request, *args, **kwargs):
        serializer = S3PolicyRequest(data=request.data)
        if not serializer.is_valid():
            raise ValidationError(serializer.errors)
        access_key_id = serializer.data.get("access_key_id", None)
        secret_access_key = serializer.data.get("secret_access_key", None)
        session_token = serializer.data.get("session_token", None)
        action = serializer.data.get("action", None)
        destination = serializer.data.get("destination", None)
        s3_client = boto3.client(
            "s3",
            aws_access_key_id=access_key_id,
            aws_secret_access_key=secret_access_key,
            aws_session_token=session_token,
        )
        if action == "ListBucket":
            response = s3_client.list_objects_v2(Bucket=destination)
        else:
            # GetObject
            bucket_name = destination.split("/")[0]
            object_key = destination.removeprefix(bucket_name + "/")
            logger.info("GetObject: %s %s", bucket_name, object_key)
            try:
                response = s3_client.get_object(Bucket=bucket_name, Key=object_key)
                logger.info(response)
            except ClientError as e:
                message = e.response.get("Error", {}).get("Message", "")
                status_code = e.response.get("ResponseMetadata", {}).get(
                    "HTTPStatusCode", 0
                )
                return ErrorResponse(exception=message, _status=status_code)
            except Exception as e:
                return ErrorResponse(e)
        return SuccessResponse()


@extend_schema(request=S3UploadMapRequest)
class S3UploadMapView(APIView):
    permission_classes = [AllowAny]

    def post(self, request: Request, *args, **kwargs):
        serializer = S3UploadMapRequest(data=request.data)
        if not serializer.is_valid():
            raise ValidationError(serializer.errors)
        mac = serializer.data.get("mac", None)
        role_alias_url = serializer.data.get("role_alias_url", None)
        thingname = serializer.data.get("thingname", None)
        bucket = serializer.data.get("bucket", None)
        map_id = serializer.data.get("map_id", None)
        label = serializer.data.get("label", None)
        logger.info(serializer.data)
        ret = get_s3_credentials(mac, role_alias_url, thingname)
        credentials = ret.get("credentials", {})
        logger.info(credentials)
        s3_client = boto3.client(
            "s3",
            aws_access_key_id=credentials["accessKeyId"],
            aws_secret_access_key=credentials["secretAccessKey"],
            aws_session_token=credentials["sessionToken"],
        )
        for file in Path(CUR_PATH, "assets/map5").iterdir():
            upload_file(s3_client, str(file), bucket, thingname, map_id, label)
        return SuccessResponse()


def upload_file(
    s3_client,
    file_path: str,
    bucket: str,
    thingname: str,
    map_id: str,
    label: bool = False,
):
    file_name = Path(file_path).name
    if file_name == "labels.json" and not label:
        file = tempfile.NamedTemporaryFile(delete=False)
        file.write("{}".encode())
        file.close()
        file_path = file.name
    object_name = f"{thingname}/current_map/{map_id}/{file_name}"
    ret = s3_client.upload_file(
        file_path,
        bucket,
        object_name,
    )
    logger.info(f"{file_name} {object_name} {ret}")


class SimulateDeviceFuncView(APIView):
    def get(self, request, *args, **kwargs):
        device_id = request.query_params.get("device_id")
        start_ts = int(request.query_params.get("start_ts", time.time() * 1000))
        timeout = int(request.query_params.get("timeout", 30))  # 默认超时时间为30秒
        start_time = timezone.datetime.fromtimestamp(start_ts / 1000)
        if not device_id:
            return ErrorResponse(
                "device_id is required", _status=status.HTTP_400_BAD_REQUEST
            )
        end_time = timezone.now() + timezone.timedelta(seconds=timeout)
        # 开始轮询
        while timezone.now() < end_time:
            # 查询数据库
            queryset = SimulateDeviceFunc.objects.filter(created_at__gt=start_time)
            if device_id:
                queryset = queryset.filter(device_id=device_id)
            if queryset.exists():
                # 如果查询到数据，序列化并返回
                serializer = SimulateDeviceFuncSerializer(queryset, many=True)
                return SuccessResponse(serializer.data)
            time.sleep(1)
        # 如果轮询时间结束仍未查询到数据，则返回空列表
        return ErrorResponse(
            "Timeout, request not found", _status=status.HTTP_404_NOT_FOUND
        )


class SimulateDevicePropSetView(APIView):
    def get(self, request: Request, *args, **kwargs):
        device_id = request.query_params.get("device_id")
        props = request.query_params.getlist("props")
        start_ts = int(request.query_params.get("start_ts", time.time() * 1000))
        timeout = int(request.query_params.get("timeout", 30))  # 默认超时时间为30秒
        start_time = timezone.datetime.fromtimestamp(start_ts / 1000)
        logger.info(start_time)
        logger.info(props)
        if not device_id:
            return ErrorResponse(
                "device_id is required", _status=status.HTTP_400_BAD_REQUEST
            )
        end_time = timezone.now() + timezone.timedelta(seconds=timeout)
        # 开始轮询
        while timezone.now() < end_time:
            # 查询数据库
            queryset = SimulateDeviceProperty.objects.filter(created_at__gt=start_time)
            if device_id:
                queryset = queryset.filter(
                    device_id=device_id,
                    field_id__in=props,
                )
            if queryset.exists():
                # 如果查询到数据，序列化并返回
                serializer = SimulateDevicePropsSerializer(queryset, many=True)
                if len(serializer.data) >= len(set(props)):
                    return SuccessResponse(serializer.data)
                else:
                    logger.warning(f"{device_id} continue waiting all props")
                    continue
            time.sleep(1)
        # 如果轮询时间结束仍未查询到数据，则返回空列表
        return ErrorResponse(
            "Timeout, request not found", _status=status.HTTP_404_NOT_FOUND
        )
