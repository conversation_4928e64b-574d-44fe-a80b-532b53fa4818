import json
import logging
from json import JSONDecodeError

from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from app.serializers import (
    BaseModelExcludeId,
    BaseModelSerializer,
    CustomListSerializer,
)

from .models import (
    SimulateDevice,
    SimulateDeviceCallbackConfig,
    SimulateDeviceFunc,
    SimulateDeviceProperty,
    SimulateDeviceType,
    SwitchBotProduct,
)
from .utils import generate_mac

logger = logging.getLogger(__name__)


class SimulateDeviceTypeSerializer(BaseModelExcludeId):
    def to_internal_value(self, data):
        if not isinstance(data.get("properties"), dict):
            try:
                data["properties"] = json.loads(data.get("properties"))
            except JSONDecodeError:
                raise ValidationError({"properties": "properties must be a jsonobject"})
        # if ',' in data.get('alias') or ',' in data.get('type'):
        #     raise ValidationError({"alias": "alias or type cannot contain comma"})
        # 根据 productName 直接获取对应 type
        logger.info(dict(SwitchBotProduct.choices))
        data["type"] = data.get("type") or dict(SwitchBotProduct.choices).get(
            data.get("product_name")
        )
        # 在校验之前自动补充 alias 字段
        data["alias"] = data.get("alias") or data.get("type")
        logger.info(data)
        return super().to_internal_value(data)

    class Meta:
        model = SimulateDeviceType
        exclude = ("is_deleted",)
        list_serializer_class = CustomListSerializer


def fill_product_name_and_type(validated_data: dict):
    # 创建和更新虚拟设备时候，设备的类型和产品名称，都由alias_type决定
    alias_type = validated_data["alias_type"]
    try:
        device_type = SimulateDeviceType.objects.get(alias=alias_type)
        validated_data["type"] = device_type.type
        validated_data["product_name"] = device_type.product_name
    except SimulateDeviceType.DoesNotExist:
        raise ValidationError({"alias_type": "alias does not exist"})


class SimulateDeviceSerializer(BaseModelExcludeId):
    mac = serializers.CharField(max_length=64, required=False)
    type = serializers.CharField(max_length=64, required=False)
    product_name = serializers.CharField(max_length=255, required=False)

    class Meta:
        model = SimulateDevice
        exclude = ("is_deleted",)
        list_serializer_class = CustomListSerializer

    def create(self, validated_data):
        # 只在创建时自动生成 mac 字段 不允许使用传入的 mac
        validated_data["mac"] = generate_mac()
        fill_product_name_and_type(validated_data)
        # logger.info(validated_data)
        return super().create(validated_data)

    def update(self, instance, validated_data):
        validated_data.pop("mac", None)
        if validated_data.get("alias_type"):
            fill_product_name_and_type(validated_data)
        return super().update(instance, validated_data)


class StatusCommandRequest(serializers.Serializer):
    """status command 请求参数"""

    mac = serializers.CharField()
    props = serializers.ListField()


class SimulateDevicePropsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SimulateDeviceProperty
        fields = "__all__"


class SimulateDeviceCallbackConfigSerializer(BaseModelSerializer):
    class Meta:
        model = SimulateDeviceCallbackConfig
        exclude = ("is_deleted",)


# class SimulateTerminalSerializer(BaseModelSerializer):
#     def to_internal_value(self, data):
#         # 判断同个user_id + env 是否已经存在
#         user_id = data.get("user_id")
#         env = data.get("env")
#         if SimulateTerminal.objects.filter(user_id=user_id, env=env).exists():
#             raise ValidationError(
#                 {"user_id": "terminal for user_id in env already exists"}
#             )
#         return super().to_internal_value(data)


#     class Meta:
#         model = SimulateTerminal
#         exclude = ("is_deleted",)
#         list_serializer_class = CustomListSerializer
class S3PolicyRequest(serializers.Serializer):
    """请求参数"""

    access_key_id = serializers.CharField()
    secret_access_key = serializers.CharField()
    session_token = serializers.CharField()
    action = serializers.CharField()
    destination = serializers.CharField(required=False)


class S3UploadMapRequest(serializers.Serializer):
    """请求参数"""

    mac = serializers.CharField()
    role_alias_url = serializers.CharField()
    thingname = serializers.CharField()
    room = serializers.IntegerField()
    bucket = serializers.CharField()
    map_id = serializers.CharField()
    label = serializers.BooleanField(required=False)


class SimulateDeviceFuncSerializer(serializers.ModelSerializer):
    class Meta:
        model = SimulateDeviceFunc
        fields = "__all__"
