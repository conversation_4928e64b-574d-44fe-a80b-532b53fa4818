import logging

from django.db.models.signals import post_save
from django.dispatch import receiver

from .models import SimulateDevice, SimulateTerminal, SwitchBotStatus
from .tasks import (
    start_mqtt_client,
    start_mqtt_watcher,
    stop_mqtt_client,
    stop_mqtt_watcher,
)

logger = logging.getLogger(__name__)


@receiver(post_save, sender=SimulateDevice)
def simulate_device_post_save_handler(sender, instance, **kwargs):
    # 虚拟设备创建，修改时候，上线/下线设备
    mac, region, env, _type, status = (
        instance.mac,
        instance.region,
        instance.env,
        instance.type,
        instance.status,
    )
    if not instance.is_deleted and status == SwitchBotStatus.Online:
        start_mqtt_client.delay(mac, region, _type, env)
    else:
        stop_mqtt_client.delay(mac, _type)
    # 修改 belong_user 时，处理对应 SimulateTerminal
    if user_id := instance.belong_user:
        simulate_terminal = SimulateTerminal.objects.filter(user_id=user_id, env=env)
        if simulate_terminal.exists():
            simulate_terminal.update(region=region, status=status)
        else:
            terminal_id = ""
            SimulateTerminal.objects.create(
                user_id=user_id,
                terminal_id=terminal_id,
                region=region,
                env=env,
                status=status,
            )


@receiver(post_save, sender=SimulateTerminal)
def simulate_terminal_post_save_handler(sender, instance, **kwargs):
    # 虚拟终端创建，修改时候，上线/下线终端
    user_id, terminal_id, region, env, status = (
        instance.user_id,
        instance.terminal_id,
        instance.region,
        instance.env,
        instance.status,
    )
    if status == SwitchBotStatus.Online:
        start_mqtt_watcher.delay(user_id, terminal_id, region, env)
    else:
        stop_mqtt_watcher.delay(user_id, terminal_id, env)
