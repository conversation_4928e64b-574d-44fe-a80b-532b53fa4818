import logging

import boto3
from botocore.exceptions import ClientError

from .models import SimulateDevice

logger = logging.getLogger(__name__)


def check_mac_in_global(mac: str) -> bool:
    session = boto3.Session(profile_name="prod", region_name="us-east-1")
    dynamodb = session.resource("dynamodb")
    table_name = "GlobalWonderLabsDevices"
    primary_key = {"deviceID": mac}
    table = dynamodb.Table(table_name)
    try:
        # 使用get_item方法查询数据
        response = table.get_item(Key=primary_key)
        # 检查返回的数据中是否有'Item'键
        if "Item" in response:
            return True
        else:
            return False
    except ClientError as e:
        raise e


def generate_mac(mac: str = "FFFFFFFFFFFF") -> str:
    # 临时先修改成这样
    mac_int = int(mac, 16)
    while True:
        mac_int -= 1
        new_mac_hex = format(mac_int, "012X")
        logger.info(new_mac_hex)
        new_mac = "".join(new_mac_hex[i : i + 2] for i in range(0, 12, 2)).upper()
        raw_query = "select mac from simulate_device where mac=%s"
        instances = SimulateDevice.objects.raw(raw_query, [new_mac])
        # 软删除，所以这里直接sql查询，如果表里没有，dynamodb global device也没有，则可以用这个mac
        if len(instances) < 1 and not check_mac_in_global(new_mac):
            break
    return new_mac
