import logging
from typing import Any, Dict, List

from . import tasks
from .models import get_identifier_map

logger = logging.getLogger(__name__)


def send_property_report_task(mac: str, _type: str, props: List[Dict[str, Any]]):
    identifier_map, _ = get_identifier_map(mac)
    # redis_client = get_redis_client()
    # device_type = redis_client.hget(mac, "type")
    # "props":[{"inOfflineDetect":"true"}]
    payload_props = []
    for prop in props:
        for key, value in prop.items():
            # 前端表单的英文identifier 转换为物模型id 后发送topic
            payload_props.append({identifier_map[key]: value})
    # 发送转换成物模型id后的payload发送
    async_result = tasks.property_report.delay(mac, _type, payload_props)
    success, message = async_result.get(10)
    return success, message
