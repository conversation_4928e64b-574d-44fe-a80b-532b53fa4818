from pathlib import Path
from typing import List, <PERSON><PERSON>

from .models import AwsRegion

CUR_PATH = Path(__file__).parent.absolute()


def get_region_certificate(region: str, env: str = "test") -> Tuple[str, str, str]:
    """

    :param region:
    :param env:
    :return: address, certfile, keyfile
    """
    key_prefix = env
    if env == "test":
        iot_prefix = "akhrsh85xmt1q"
    elif env == "prod":
        iot_prefix = "a2alhn2dfztqv9"
    else:
        raise ValueError(f"env {env} is not supported")
    if region == AwsRegion.US:
        return (
            f"{iot_prefix}-ats.iot.{region}.amazonaws.com",
            f"{CUR_PATH}/assets/cert/{key_prefix}-us-certificate.pem.crt",
            f"{CUR_PATH}/assets/cert/{key_prefix}-us-private.pem.key",
        )
    elif region == AwsRegion.AP:
        return (
            f"{iot_prefix}-ats.iot.{region}.amazonaws.com",
            f"{CUR_PATH}/assets/cert/{key_prefix}-ap-certificate.pem.crt",
            f"{CUR_PATH}/assets/cert/{key_prefix}-ap-private.pem.key",
        )
    elif region == AwsRegion.EU:
        return (
            f"{iot_prefix}-ats.iot.{region}.amazonaws.com",
            f"{CUR_PATH}/assets/cert/{key_prefix}-eu-certificate.pem.crt",
            f"{CUR_PATH}/assets/cert/{key_prefix}-eu-private.pem.key",
        )
    else:
        raise ValueError(f"region {region} is not supported")


def func_response(
    mac: str,
    device_id: str,
    region: str,
    device_type: str,
    func_id: int,
    value: List,
    version: str = "v1_1",
):
    """
    收到v1_1/WoSweeperOrigin/B0E9FE00048C/funcRequest执行后
    回复信息

    :param mac: 父设备的mac地址
    :param device_id: 实际回复设备的mac地址，一般wifi设备就是自己的ble mac地址
    :param region: _description_
    :param device_type: _description_
    :param func_name: _description_
    :param version: 目前默认为 v1_1 版本
    """
    # device_type = "WoSweeperOrigin"
    topic = f"$aws/rules/funcResponse_{version}/{device_type}/{mac}/funcResponse"
    params = {}
    for i in range(len(value)):
        params[str(i)] = value[i]
    payload = {
        "code": 1,
        "deviceID": device_id,
        "payload": {
            "functionID": func_id,
            "params": params,
            "requestID": "DC8936A7-EC90-4628-B565-9ED4E41BC984",
            "timestamp": 1701947004799,
        },
        "sign": "",
        "version": "1.0",
    }
