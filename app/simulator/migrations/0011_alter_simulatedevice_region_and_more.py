# Generated by Django 4.2.5 on 2024-03-06 16:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('simulator', '0010_remove_simulatedevicefunc_user_id'),
    ]

    operations = [
        migrations.AlterField(
            model_name='simulatedevice',
            name='region',
            field=models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-central-1', 'Eu')], default='us-east-1', help_text='设备所在区域', max_length=32),
        ),
        migrations.AlterField(
            model_name='simulatedevicefunc',
            name='region',
            field=models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-central-1', 'Eu')], default='us-east-1', help_text='mqtt消息的所在区域', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulatedeviceproperty',
            name='region',
            field=models.Char<PERSON>ield(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-central-1', 'Eu')], default='us-east-1', help_text='mqtt消息的所在区域', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulateterminal',
            name='region',
            field=models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-central-1', 'Eu')], default='us-east-1', help_text='终端所在区域', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulateterminalfunc',
            name='region',
            field=models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-central-1', 'Eu')], default='us-east-1', help_text='mqtt消息的所在区域', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulateterminalproperty',
            name='region',
            field=models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-central-1', 'Eu')], default='us-east-1', help_text='mqtt消息的所在区域', max_length=64),
        ),
    ]
