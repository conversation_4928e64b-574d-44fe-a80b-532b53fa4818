# Generated by Django 4.2.5 on 2023-12-13 16:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('simulator', '0003_remove_simulatedeviceproperty_value_type_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SimulateDeviceCallbackConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('name', models.CharField(help_text='配置名称', max_length=64)),
                ('mac', models.CharField(help_text='设备mac地址', max_length=64)),
                ('function_id', models.Integer<PERSON>ield(help_text='物模型方法id')),
                ('callback_template', models.J<PERSON><PERSON>ield(help_text='物模型回复方法模板')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'simulate_device_callback_config',
            },
        ),
    ]
