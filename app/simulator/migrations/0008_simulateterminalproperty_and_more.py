# Generated by Django 4.2.5 on 2024-01-26 10:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('simulator', '0007_simulatedevice_cert_pem_simulatedevice_key_pem'),
    ]

    operations = [
        migrations.CreateModel(
            name='SimulateTerminalProperty',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.Char<PERSON>ield(help_text='topic中userid', max_length=64)),
                ('mac', models.CharField(help_text='topic中mac地址', max_length=64)),
                ('device_id', models.Char<PERSON>ield(help_text='mqtt消息的设备ID', max_length=64, null=True)),
                ('region', models.Char<PERSON>ield(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-northeast-1', 'Eu')], default='us-east-1', help_text='mqtt消息的所在区域', max_length=64)),
                ('field', models.Char<PERSON><PERSON>(help_text='物模型属性名称', max_length=64, null=True)),
                ('field_id', models.CharField(help_text='物模型属性ID', max_length=64)),
                ('field_value', models.JSONField(help_text='物模型属性值')),
                ('seq', models.IntegerField(default=0, help_text='物模型属性序号')),
                ('mqtt_time', models.DateTimeField(help_text='mqtt消息中的时间戳', null=True)),
                ('source', models.CharField(help_text='属性来源,notify, notifyAll', max_length=64, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'simulate_terminal_property',
            },
        ),
        migrations.RemoveField(
            model_name='simulatedeviceproperty',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='simulatedeviceproperty',
            name='is_deleted',
        ),
        migrations.RemoveField(
            model_name='simulatedeviceproperty',
            name='updated_at',
        ),
        migrations.RemoveField(
            model_name='simulatedeviceproperty',
            name='updated_by',
        ),
        migrations.AddField(
            model_name='simulatedeviceproperty',
            name='seq',
            field=models.IntegerField(default=0, help_text='物模型属性序号'),
        ),
        migrations.AlterField(
            model_name='simulatedeviceproperty',
            name='mac',
            field=models.CharField(help_text='mqtt client的mac地址', max_length=64),
        ),
    ]
