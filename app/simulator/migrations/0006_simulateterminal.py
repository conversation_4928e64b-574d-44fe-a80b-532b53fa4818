# Generated by Django 4.2.5 on 2023-12-19 19:56

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('simulator', '0005_simulatedevice_belong_user'),
    ]

    operations = [
        migrations.CreateModel(
            name='SimulateTerminal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_deleted', models.Boolean<PERSON>ield(default=False)),
                ('user_id', models.Char<PERSON>ield(help_text='用户ID', max_length=64)),
                ('terminal_id', models.Char<PERSON>ield(help_text='终端ID', max_length=64)),
                ('region', models.Char<PERSON>ield(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-northeast-1', 'Eu')], default='us-east-1', help_text='终端所在区域', max_length=64)),
                ('env', models.CharField(choices=[('test', 'Test'), ('prod', 'Prod')], default='test', help_text='终端环境', max_length=12)),
                ('status', models.CharField(choices=[('on', 'Online'), ('off', 'Offline')], default='on', help_text='终端状态', max_length=8)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'simulate_terminal',
                'unique_together': {('user_id', 'env')},
            },
        ),
    ]
