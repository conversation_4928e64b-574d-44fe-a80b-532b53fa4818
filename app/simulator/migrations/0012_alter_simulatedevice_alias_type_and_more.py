# Generated by Django 4.2.11 on 2024-03-21 15:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('simulator', '0011_alter_simulatedevice_region_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='simulatedevice',
            name='alias_type',
            field=models.Char<PERSON>ield(db_comment='设备类型别名', max_length=128),
        ),
        migrations.AlterField(
            model_name='simulatedevice',
            name='belong_user',
            field=models.CharField(blank=True, db_comment='物模型设备所属用户id, client_id使用', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulatedevice',
            name='cert_pem',
            field=models.TextField(blank=True, db_comment='iot私钥'),
        ),
        migrations.AlterField(
            model_name='simulatedevice',
            name='env',
            field=models.CharField(choices=[('test', 'Test'), ('prod', 'Prod')], db_comment='设备环境', default='test', max_length=12),
        ),
        migrations.AlterField(
            model_name='simulatedevice',
            name='is_sub',
            field=models.BooleanField(db_comment='是否是子设备', default=False),
        ),
        migrations.AlterField(
            model_name='simulatedevice',
            name='key_pem',
            field=models.TextField(blank=True, db_comment='iot公钥'),
        ),
        migrations.AlterField(
            model_name='simulatedevice',
            name='mac',
            field=models.CharField(db_comment='设备mac地址', max_length=64, primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='simulatedevice',
            name='product_name',
            field=models.CharField(choices=[('Floor Cleaning Robot S10', 'WoSweeperOrigin'), ('Hub 2', 'Hub2'), ('Bot', 'Bot')], db_comment='产品名称', max_length=255),
        ),
        migrations.AlterField(
            model_name='simulatedevice',
            name='region',
            field=models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-central-1', 'Eu')], db_comment='设备所在区域', default='us-east-1', max_length=32),
        ),
        migrations.AlterField(
            model_name='simulatedevice',
            name='status',
            field=models.CharField(choices=[('on', 'Online'), ('off', 'Offline')], db_comment='设备状态', default='on', max_length=8),
        ),
        migrations.AlterField(
            model_name='simulatedevice',
            name='type',
            field=models.CharField(choices=[('WoSweeperOrigin', 'WoSweeperOrigin'), ('Hub2', 'Hub2'), ('Bot', 'Bot')], db_comment='设备类型', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulatedevicecallbackconfig',
            name='callback_template',
            field=models.JSONField(db_comment='物模型回复方法模板'),
        ),
        migrations.AlterField(
            model_name='simulatedevicecallbackconfig',
            name='function_id',
            field=models.IntegerField(db_comment='物模型方法id'),
        ),
        migrations.AlterField(
            model_name='simulatedevicecallbackconfig',
            name='mac',
            field=models.CharField(db_comment='设备mac地址', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulatedevicecallbackconfig',
            name='name',
            field=models.CharField(db_comment='配置名称', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulatedevicefunc',
            name='device_id',
            field=models.CharField(db_comment='mqtt消息的设备ID', max_length=64, null=True),
        ),
        migrations.AlterField(
            model_name='simulatedevicefunc',
            name='field',
            field=models.CharField(db_comment='物模型方法名称', max_length=64, null=True),
        ),
        migrations.AlterField(
            model_name='simulatedevicefunc',
            name='func_id',
            field=models.IntegerField(db_comment='物模型方法ID'),
        ),
        migrations.AlterField(
            model_name='simulatedevicefunc',
            name='mac',
            field=models.CharField(db_comment='topic的mac地址', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulatedevicefunc',
            name='mqtt_time',
            field=models.DateTimeField(db_comment='mqtt消息中的时间戳', null=True),
        ),
        migrations.AlterField(
            model_name='simulatedevicefunc',
            name='params',
            field=models.JSONField(db_comment='方法请求/响应参数'),
        ),
        migrations.AlterField(
            model_name='simulatedevicefunc',
            name='region',
            field=models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-central-1', 'Eu')], db_comment='mqtt消息的所在区域', default='us-east-1', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulatedevicefunc',
            name='request_id',
            field=models.CharField(db_comment='物模型方法请求ID', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulatedeviceproperty',
            name='device_id',
            field=models.CharField(db_comment='mqtt消息的设备ID', max_length=64, null=True),
        ),
        migrations.AlterField(
            model_name='simulatedeviceproperty',
            name='field',
            field=models.CharField(db_comment='物模型属性名称', max_length=64, null=True),
        ),
        migrations.AlterField(
            model_name='simulatedeviceproperty',
            name='field_id',
            field=models.CharField(db_comment='物模型属性ID', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulatedeviceproperty',
            name='field_value',
            field=models.JSONField(db_comment='物模型属性值'),
        ),
        migrations.AlterField(
            model_name='simulatedeviceproperty',
            name='from_server',
            field=models.BooleanField(db_comment='是否来自服务器', default=False),
        ),
        migrations.AlterField(
            model_name='simulatedeviceproperty',
            name='mac',
            field=models.CharField(db_comment='mqtt client的mac地址', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulatedeviceproperty',
            name='mqtt_time',
            field=models.DateTimeField(db_comment='mqtt消息中的时间戳', null=True),
        ),
        migrations.AlterField(
            model_name='simulatedeviceproperty',
            name='region',
            field=models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-central-1', 'Eu')], db_comment='mqtt消息的所在区域', default='us-east-1', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulatedeviceproperty',
            name='seq',
            field=models.IntegerField(db_comment='物模型属性序号', default=0),
        ),
        migrations.AlterField(
            model_name='simulatedevicetype',
            name='alias',
            field=models.CharField(db_comment='设备类型别名', max_length=128, primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='simulatedevicetype',
            name='product_name',
            field=models.CharField(choices=[('Floor Cleaning Robot S10', 'WoSweeperOrigin'), ('Hub 2', 'Hub2'), ('Bot', 'Bot')], db_comment='产品名称', max_length=255),
        ),
        migrations.AlterField(
            model_name='simulatedevicetype',
            name='properties',
            field=models.JSONField(db_comment='设备物模型属性'),
        ),
        migrations.AlterField(
            model_name='simulatedevicetype',
            name='type',
            field=models.CharField(choices=[('WoSweeperOrigin', 'WoSweeperOrigin'), ('Hub2', 'Hub2'), ('Bot', 'Bot')], db_comment='设备类型', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulateterminal',
            name='env',
            field=models.CharField(choices=[('test', 'Test'), ('prod', 'Prod')], db_comment='终端环境', default='test', max_length=12),
        ),
        migrations.AlterField(
            model_name='simulateterminal',
            name='region',
            field=models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-central-1', 'Eu')], db_comment='终端所在区域', default='us-east-1', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulateterminal',
            name='status',
            field=models.CharField(choices=[('on', 'Online'), ('off', 'Offline')], db_comment='终端状态', default='on', max_length=8),
        ),
        migrations.AlterField(
            model_name='simulateterminal',
            name='terminal_id',
            field=models.CharField(db_comment='终端ID', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulateterminal',
            name='user_id',
            field=models.CharField(db_comment='用户ID', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulateterminalfunc',
            name='client_id',
            field=models.CharField(db_comment='topic中client_id', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulateterminalfunc',
            name='device_id',
            field=models.CharField(db_comment='mqtt消息的设备ID', max_length=64, null=True),
        ),
        migrations.AlterField(
            model_name='simulateterminalfunc',
            name='field',
            field=models.CharField(db_comment='物模型方法名称', max_length=64, null=True),
        ),
        migrations.AlterField(
            model_name='simulateterminalfunc',
            name='func_id',
            field=models.IntegerField(db_comment='物模型方法ID'),
        ),
        migrations.AlterField(
            model_name='simulateterminalfunc',
            name='mqtt_time',
            field=models.DateTimeField(db_comment='mqtt消息中的时间戳', null=True),
        ),
        migrations.AlterField(
            model_name='simulateterminalfunc',
            name='params',
            field=models.JSONField(db_comment='方法请求/响应参数'),
        ),
        migrations.AlterField(
            model_name='simulateterminalfunc',
            name='region',
            field=models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-central-1', 'Eu')], db_comment='mqtt消息的所在区域', default='us-east-1', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulateterminalfunc',
            name='request_id',
            field=models.CharField(db_comment='物模型方法请求ID', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulateterminalfunc',
            name='result_code',
            field=models.JSONField(db_comment='响应码'),
        ),
        migrations.AlterField(
            model_name='simulateterminalfunc',
            name='user_id',
            field=models.CharField(db_comment='topic中userid', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulateterminalproperty',
            name='device_id',
            field=models.CharField(db_comment='mqtt消息的设备ID', max_length=64, null=True),
        ),
        migrations.AlterField(
            model_name='simulateterminalproperty',
            name='field',
            field=models.CharField(db_comment='物模型属性名称', max_length=64, null=True),
        ),
        migrations.AlterField(
            model_name='simulateterminalproperty',
            name='field_id',
            field=models.CharField(db_comment='物模型属性ID', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulateterminalproperty',
            name='field_value',
            field=models.JSONField(db_comment='物模型属性值'),
        ),
        migrations.AlterField(
            model_name='simulateterminalproperty',
            name='mac',
            field=models.CharField(db_comment='topic中mac地址', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulateterminalproperty',
            name='mqtt_time',
            field=models.DateTimeField(db_comment='mqtt消息中的时间戳', null=True),
        ),
        migrations.AlterField(
            model_name='simulateterminalproperty',
            name='region',
            field=models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-central-1', 'Eu')], db_comment='mqtt消息的所在区域', default='us-east-1', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulateterminalproperty',
            name='seq',
            field=models.IntegerField(db_comment='物模型属性序号', default=0),
        ),
        migrations.AlterField(
            model_name='simulateterminalproperty',
            name='source',
            field=models.CharField(db_comment='属性来源,notify, notifyAll', max_length=64, null=True),
        ),
        migrations.AlterField(
            model_name='simulateterminalproperty',
            name='user_id',
            field=models.CharField(db_comment='topic中userid', max_length=64),
        ),
    ]
