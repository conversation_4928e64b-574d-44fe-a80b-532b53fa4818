# Generated by Django 4.2.5 on 2023-12-12 18:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('simulator', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='simulatedevice',
            name='env',
            field=models.CharField(choices=[('test', 'Test'), ('prod', 'Prod')], default='test', help_text='设备环境', max_length=12),
        ),
        migrations.AlterField(
            model_name='simulatedevice',
            name='product_name',
            field=models.CharField(choices=[('Floor Cleaning Robot S10', 'WoSweeperOrigin'), ('Hub 2', 'Hub2'), ('Bot', 'Bot')], help_text='产品名称', max_length=255),
        ),
        migrations.AlterField(
            model_name='simulatedevice',
            name='type',
            field=models.CharField(choices=[('WoSweeperOrigin', 'WoSweeperOrigin'), ('Hub2', 'Hub2'), ('Bot', 'Bot')], help_text='设备类型', max_length=64),
        ),
        migrations.AlterField(
            model_name='simulatedevicetype',
            name='product_name',
            field=models.CharField(choices=[('Floor Cleaning Robot S10', 'WoSweeperOrigin'), ('Hub 2', 'Hub2'), ('Bot', 'Bot')], help_text='产品名称', max_length=255),
        ),
        migrations.AlterField(
            model_name='simulatedevicetype',
            name='type',
            field=models.CharField(choices=[('WoSweeperOrigin', 'WoSweeperOrigin'), ('Hub2', 'Hub2'), ('Bot', 'Bot')], help_text='设备类型', max_length=64),
        ),
        migrations.CreateModel(
            name='SimulateDeviceProperty',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('mac', models.CharField(help_text='收到消息的mqttclient设备mac地址', max_length=64)),
                ('device_id', models.CharField(help_text='mqtt消息的设备ID', max_length=64, null=True)),
                ('region', models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-northeast-1', 'Eu')], default='us-east-1', help_text='mqtt消息的所在区域', max_length=64)),
                ('field', models.CharField(help_text='物模型属性名称', max_length=64, null=True)),
                ('field_id', models.CharField(help_text='物模型属性ID', max_length=64)),
                ('field_value', models.TextField(help_text='物模型属性值')),
                ('value_type', models.CharField(help_text='物模型属性值类型', max_length=64)),
                ('mqtt_time', models.DateTimeField(help_text='mqtt消息中的时间戳', null=True)),
                ('from_server', models.BooleanField(default=False, help_text='是否来自服务器')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'simulate_device_property',
            },
        ),
    ]
