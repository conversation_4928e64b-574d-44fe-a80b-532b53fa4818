# Generated by Django 4.2.5 on 2024-02-18 15:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('simulator', '0008_simulateterminalproperty_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SimulateDeviceFunc',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.Char<PERSON>ield(help_text='topic中userid', max_length=64)),
                ('mac', models.CharField(help_text='topic的mac地址', max_length=64)),
                ('device_id', models.CharField(help_text='mqtt消息的设备ID', max_length=64, null=True)),
                ('region', models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-northeast-1', 'Eu')], default='us-east-1', help_text='mqtt消息的所在区域', max_length=64)),
                ('field', models.CharField(help_text='物模型方法名称', max_length=64, null=True)),
                ('func_id', models.IntegerField(help_text='物模型方法ID')),
                ('request_id', models.CharField(help_text='物模型方法请求ID', max_length=64)),
                ('params', models.JSONField(help_text='方法请求/响应参数')),
                ('mqtt_time', models.DateTimeField(help_text='mqtt消息中的时间戳', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'simulate_device_func',
            },
        ),
        migrations.CreateModel(
            name='SimulateTerminalFunc',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.CharField(help_text='topic中userid', max_length=64)),
                ('client_id', models.CharField(help_text='topic中client_id', max_length=64)),
                ('device_id', models.CharField(help_text='mqtt消息的设备ID', max_length=64, null=True)),
                ('region', models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-northeast-1', 'Eu')], default='us-east-1', help_text='mqtt消息的所在区域', max_length=64)),
                ('field', models.CharField(help_text='物模型方法名称', max_length=64, null=True)),
                ('func_id', models.IntegerField(help_text='物模型方法ID')),
                ('request_id', models.CharField(help_text='物模型方法请求ID', max_length=64)),
                ('params', models.JSONField(help_text='方法请求/响应参数')),
                ('result_code', models.JSONField(help_text='响应码')),
                ('mqtt_time', models.DateTimeField(help_text='mqtt消息中的时间戳', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'simulate_terminal_func',
            },
        ),
    ]
