# Generated by Django 4.2.5 on 2023-11-28 20:03

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SimulateDeviceType',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('alias', models.CharField(help_text='设备类型别名', max_length=128, primary_key=True, serialize=False)),
                ('type', models.CharField(choices=[('Floor Cleaning Robot S10', 'Floor Cleaning Robot S10'), ('Hub 2', 'Hub 2'), ('Bot', 'Bot')], help_text='设备类型', max_length=64)),
                ('product_name', models.Char<PERSON>ield(choices=[('Floor Cleaning Robot S10', 'Sweeperorigin'), ('Hub 2', 'Hub2'), ('Bot', 'Bot')], help_text='产品名称', max_length=255)),
                ('properties', models.JSONField(help_text='设备物模型属性')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'simulate_device_type',
            },
        ),
        migrations.CreateModel(
            name='SimulateDevice',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('mac', models.CharField(help_text='设备mac地址', max_length=64, primary_key=True, serialize=False)),
                ('region', models.CharField(choices=[('us-east-1', 'Us'), ('ap-northeast-1', 'Ap'), ('eu-northeast-1', 'Eu')], default='us-east-1', help_text='设备所在区域', max_length=64)),
                ('is_sub', models.BooleanField(default=False, help_text='是否是子设备')),
                ('status', models.CharField(choices=[('on', 'Online'), ('off', 'Offline')], default='on', help_text='设备状态', max_length=8)),
                ('alias_type', models.CharField(help_text='设备类型别名', max_length=128)),
                ('product_name', models.CharField(choices=[('Floor Cleaning Robot S10', 'Sweeperorigin'), ('Hub 2', 'Hub2'), ('Bot', 'Bot')], help_text='产品名称', max_length=255)),
                ('type', models.CharField(choices=[('Floor Cleaning Robot S10', 'Floor Cleaning Robot S10'), ('Hub 2', 'Hub 2'), ('Bot', 'Bot')], help_text='设备类型', max_length=64)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'simulate_device',
            },
        ),
    ]
