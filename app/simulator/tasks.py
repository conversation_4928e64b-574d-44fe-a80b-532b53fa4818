import json
import logging
import ssl
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, <PERSON>ple

import paho.mqtt.client as mqtt
from celery import shared_task
from paho.mqtt.client import MQTTv5
from paho.mqtt.enums import CallbackAPIVersion, MQTTErrorCode

from app.util.db import batch_delete
from autoswitchbot import celery_app

from .devices.robot import generate_random_float
from .models import (
    get_app_client_id,
    get_client_id,
    get_latest_callback_template,
    get_online_devices,
    get_online_terminals,
    get_redis_client,
    get_seq_number,
    save_func_request,
    save_notify_all_to_user,
    save_property_set,
    save_property_to_user,
)
from .mqtt import get_region_certificate

logger = logging.getLogger("celery")


class MqttClient(mqtt.Client):
    def loop_start(self) -> MQTTErrorCode:
        """This is part of the threaded client interface. Call this once to
        start a new thread to process network traffic. This provides an
        alternative to repeatedly calling `loop()` yourself.

        Under the hood, this will call `loop_forever` in a thread, which means that
        the thread will terminate if you call `disconnect()`
        """
        if self._thread is not None:
            return MQTTErrorCode.MQTT_ERR_INVAL

        self._thread_terminate = False
        self._thread = threading.Thread(
            target=self.loop_forever,
            name=f"paho-mqtt-client-{self._client_id.decode()}",
        )
        self._thread.daemon = True
        self._thread.start()

        return MQTTErrorCode.MQTT_ERR_SUCCESS


# MQTT客户端列表
# mqtt_clients = []
# client_id: {'mqtt', 'mac', '_type'}
mqtt_clients: Dict[str, MqttClient] = {}


def host2region(_host):
    # akhrsh85xmt1q-ats.iot.us-east-1.amazonaws.com
    return _host.split(".")[-3]


@celery_app.task(queue="simulator_queue")
def save_property_set_task(topic: str, region: str, message: str):
    """保存服务端下发给设备的属性设置"""
    device_type, mac = get_device_info(topic)
    if not device_type:
        return False
    save_property_set(device_type, mac, region, message)


@celery_app.task(queue="simulator_queue")
def save_notify_all_to_user_task(
    user_id: str,
    mac: str,
    device_id: str,
    region: str,
    source: str,
    _property: dict,
):
    """保存首页通知给 app(用户)的属性"""
    save_notify_all_to_user(
        user_id=user_id,
        mac=mac,
        device_id=device_id,
        region=region,
        source=source,
        _property=_property,
    )


@celery_app.task(queue="simulator_queue")
def save_property_to_user_task(
    user_id: str,
    mac: str,
    device_id: str,
    region: str,
    seq: int,
    mqtt_time: int,
    source: str,
    _property: dict,
):
    """保存通知给 app(用户)的属性"""
    save_property_to_user(
        user_id=user_id,
        mac=mac,
        device_id=device_id,
        region=region,
        seq=seq,
        mqtt_time=mqtt_time,
        source=source,
        _property=_property,
    )


@celery_app.task(queue="simulator_queue")
def save_func_request_task(topic: str, region: str, message: str):
    """保存给设备的方法请求"""
    device_type, mac = get_device_info(topic)
    if not device_type:
        return False
    save_func_request(device_type, mac, region, message)
    return True, "success"


@celery_app.task(queue="simulator_queue")
def send_property_change_task(topic: str, message: str):
    """保存给设备的属性变更"""
    device_type, mac = get_device_info(topic)
    if not device_type:
        return False
    client = wait_for_client(mac, device_type, 10)
    if not client:
        return False, "MQTT client maybe already stopped."
    payload = json.loads(message)
    set_payload = payload.get("payload", {})
    _property = set_payload.get("property", {})
    send_property_change(client, device_type, mac, _property)


def on_message_default(client: mqtt.Client, userdata, msg):
    # 收到消息时，存储对应属性到设备表中
    topic = msg.topic
    message = msg.payload.decode("utf-8")
    logger.info(f"{topic}\n{message}")
    region = host2region(client._host)  # type: ignore
    if topic.endswith("/propertySet"):
        # 下发给设备的属性设置
        # 设备收到保存后，再通知给server属性变更
        # {"version":"1","deviceID":"2DBA9D991A4C","code":2,"payload":{"seq":0,"timestamp":1708146318372,"property":{"1039":20}}}
        send_property_change_task.delay(topic, message)
        save_property_set_task.delay(topic, region, message)
    elif topic.endswith("/funcRequest"):
        # 下发给设备的命令
        # 查询是否有父设备，有的话，使用父设备的mqtt进行回复
        # 查询模板，按模板回复，和执行动作，执行动作后，需要可以暂停、继续打扫
        # {"code":1,"deviceID":"B0E9FE00048C","payload":{"functionID":1009,"params":{"0":0},"requestID":"F28A522A-AAF0-440B-BB8F-4876C57E75E8","timestamp":1702348464121},"sign":"","version":"1.0"}
        func_reply_task.delay(topic, message)
        save_func_request_task.delay(topic, region, message)


def on_connect(client, userdata, flags, rc, properties):
    # subscribe property set on connect
    mac = userdata.get("mac")
    _type = userdata.get("_type")
    logger.info(f"Connected with result code {rc} {userdata}")
    subscribe_property_set_topic(client, mac, _type)


def on_terminal_message(client: mqtt.Client, userdata, msg):
    # 收到消息时，存储对应属性到设备表中
    logger.info(f"{msg.topic}\n{msg.payload.decode('utf-8')}")
    region = host2region(client._host)  # type: ignore
    if msg.topic.endswith("/propertyChanged"):
        # 属性变更，通知到用户的手机
        topics = msg.topic.split("/")
        if len(topics) != 4:
            logger.error(f"Invalid topic {msg.topic}")
            return
        user_id = topics[1]
        mac = topics[2]
        message_type = topics[3]  # propertyChanged
        message_obj = json.loads(msg.payload.decode("utf-8"))
        device_id = message_obj.get("deviceID", None)
        payload = message_obj.get("payload", {})
        mqtt_seq = payload.get("seq", None)
        mqtt_timestamp = payload.get("timestamp", None)
        _property = payload.get("property", {})
        save_property_to_user_task.delay(
            user_id=user_id,
            mac=mac,
            device_id=device_id,
            region=region,
            seq=mqtt_seq,
            mqtt_time=mqtt_timestamp,
            source=message_type,
            _property=_property,
        )
    elif msg.topic.endswith("/notifyAllProperty"):
        # 首页刷新获取属性，通知到用户的手机
        topics = msg.topic.split("/")
        if len(topics) != 4:
            logger.error(f"Invalid topic {msg.topic}")
            return
        user_id = topics[1]
        mac = topics[2]
        message_type = topics[3]  # notifyAllProperty
        message_obj = json.loads(msg.payload.decode("utf-8"))
        # {"FFFFFFFFFFD2":{"1003":{"id":1003,"type":0,"value":true,"seq":0,"timestamp":1705911324131,"statusStartTimestamp":0,"multiUser":false}}
        for device_id, _property in message_obj.items():
            save_notify_all_to_user_task.delay(
                user_id=user_id,
                mac=mac,
                device_id=device_id,
                region=region,
                source=message_type,
                _property=_property,
            )


def on_terminal_connect(client, userdata, flags, rc, properties):
    # subscribe app notify on connect
    user_id = userdata.get("user_id")
    logger.info(f"Connected with result code {rc} {userdata}")
    subscribe_terminal_notify_topic(client, user_id)


def on_disconnect(client, userdata, disconnect_flags, rc, properties):
    logger.info(
        f"Disconnected with result code {rc}: {mqtt.error_string(rc)} {disconnect_flags} {userdata}"
    )


def create_mqtt_client(
    mac: str, region: str, _type: str, env: str
) -> Tuple[str, MqttClient]:
    """创建MQTT客户端并连接到MQTT代理

    :param mac: _description_
    :param region: _description_
    :param _type: _description_
    :param env: _description_
    :return: _description_
    """
    client_id = get_client_id(mac, _type)
    address, certfile, keyfile = get_region_certificate(region, env=env)
    if client := mqtt_clients.get(client_id):
        if client._host != address:  # type: ignore
            # 已启动其他区的客户端，先停止，再启动新区域的客户端
            logger.info(
                "client_id maybe connected to different host, stop exist mqtt client"
            )
            ret = stop_mqtt_client(mac, _type)
            logger.info(ret)
        # 已启动同区的客户端， 不需要重连
        return client_id, client
    client = MqttClient(
        CallbackAPIVersion.VERSION2,
        client_id=client_id,
        userdata={"mac": mac, "_type": _type},
        protocol=MQTTv5,
    )
    client.tls_set(certfile=certfile, keyfile=keyfile, tls_version=ssl.PROTOCOL_TLSv1_2)
    client.tls_insecure_set(False)
    client.on_connect = on_connect
    client.on_disconnect = on_disconnect
    client.connect(address, 8883, 30)  # 替换为你的MQTT代理地址和端口
    client.loop_start()
    return client_id, client


def create_mqtt_for_app(
    user_id: str, terminal_id: str, region: str, env: str
) -> Tuple[str, MqttClient]:
    """创建MQTT客户端 监听app消息

    terminal_id和user_id+env有关, client_id不会重复

    :param user_id: _description_
    :param region: _description_
    :param env: _description_
    :return: _description_
    """
    client_id = get_app_client_id(user_id, terminal_id)
    address, certfile, keyfile = get_region_certificate(region, env=env)
    if client := mqtt_clients.get(client_id):
        # 已启动同区的客户端， 不需要重连
        return client_id, client
    client = MqttClient(
        CallbackAPIVersion.VERSION2,
        client_id=client_id,
        userdata={"user_id": user_id, "terminal_id": terminal_id},
        protocol=MQTTv5,
    )
    client.tls_set(certfile=certfile, keyfile=keyfile, tls_version=ssl.PROTOCOL_TLSv1_2)
    client.tls_insecure_set(False)
    client.on_connect = on_terminal_connect
    client.on_disconnect = on_disconnect
    client.connect(address, 8883, 30)  # 替换为你的MQTT代理地址和端口
    client.loop_start()
    return client_id, client


mqtt_mac_lock = threading.Lock()  # mqtt client锁，防止同个mac的client重复实例化


# Celery任务：启动指定数量的MQTT客户端
@celery_app.task(queue="simulator_queue", expires=60 * 60)
def start_mqtt_client(mac: str, region: str, type: str, env: str):
    """启动单个mqtt客户端"""
    _type = type
    with mqtt_mac_lock:
        logger.info(f"start_mqtt_client mac:{mac}, region:{region}, env:{env}")
        client_id, client = create_mqtt_client(mac, region, _type, env=env)
        mqtt_clients[client_id] = client
        redis_client = get_redis_client()
        # 接口模拟属性上报的时候，需要用这个type
        redis_client.hset(
            mac,
            mapping={
                "region": region,
                "type": _type,
            },
        )
        # save_property_by_client(
        #     mac, region, field_id="66", value=1
        # )  # client上线时，本地存储online属性
        return f"{client_id} MQTT client started at {client._host}"  # type: ignore


@celery_app.task(queue="simulator_queue", expires=60 * 60)
def start_mqtt_watcher(user_id: str, terminal_id: str, region: str, env: str):
    """启动单个mqtt终端 for APP"""
    with mqtt_mac_lock:
        logger.info(f"start_mqtt_watcher user_id:{user_id}, region:{region}, env:{env}")
        client_id, client = create_mqtt_for_app(user_id, terminal_id, region, env=env)
        mqtt_clients[client_id] = client
        return f"{client_id} MQTT client started at {client._host}"  # type: ignore


@celery_app.task(queue="simulator_queue")
def stop_mqtt_client(mac: str, _type: str):
    """停止单个mqtt客户端"""
    try:
        client = mqtt_clients.pop(get_client_id(mac, _type))
        if client:
            client.loop_stop()
            client.disconnect()
    except KeyError:
        logger.warn(f"{mac} client maybe not exist.")
    return f"{mac} MQTT client stopped."


@celery_app.task(queue="simulator_queue")
def stop_mqtt_watcher(user_id: str, terminal_id: str):
    """停止单个mqtt终端 for APP"""
    try:
        client = mqtt_clients.pop(get_app_client_id(user_id, terminal_id))
        if client:
            client.loop_stop()
            client.disconnect()
    except KeyError:
        logger.warn(f"{user_id} client maybe not exist.")
    return f"{user_id} MQTT client stopped."


@celery_app.task(queue="simulator_queue", expires=60 * 60)
def start_mqtt_clients():
    """启动所有mqtt客户端"""
    devices = get_online_devices()
    for device in devices:
        logger.info(f"start_mqtt_client :{device}")
        start_mqtt_client.delay(**device)
    return f"Start {len(devices)} MQTT clients."


@celery_app.task(queue="simulator_queue", expires=60 * 60)
def start_mqtt_watchers():
    """启动所有mqtt客户端 for app"""
    app_terminals = get_online_terminals()
    for app_terminal in app_terminals:
        logger.info(f"start_mqtt_watcher :{app_terminal}")
        start_mqtt_watcher.delay(**app_terminal)
    return f"Start {len(app_terminals)} MQTT terminals."


def wait_for_client(mac: str, type: str, wait_time: int):
    """属性上报时，等待客户端上线"""
    start_time = time.time()
    while time.time() - start_time < wait_time:
        if client := mqtt_clients.get(get_client_id(mac, type)):
            return client
        time.sleep(0.5)
    return None


# Celery任务：使用MQTT客户端发送消息
@celery_app.task(queue="simulator_queue")
def property_report(mac: str, device_type: str, props: list[dict], wait_time: int = 10):
    """虚拟设备，属性上报

    刚启动完立即发送属性时，可能找不到client，这里可以等待client上线
    """

    client = wait_for_client(mac, device_type, wait_time)
    if not client:
        return False, "MQTT client maybe already stopped."
    # 使用第一个客户端发送消息
    for prop in props:
        send_property_change(client, device_type, mac, prop)
    return True, "Message sent"


def subscribe_property_set_topic(client: mqtt.Client, mac: str, _type: str):
    """订阅来自服务端的属性和方法 设置默认 on_message 回调"""
    client.subscribe(topic=f"v1_1/{_type}/{mac}/propertySet", qos=1)
    client.subscribe(topic=f"v1_1/{_type}/{mac}/funcRequest", qos=1)
    logger.info(f"subscribe to v1_1/{_type}/{mac}/propertySet")
    logger.info(f"subscribe to v1_1/{_type}/{mac}/funcRequest")
    client.on_message = on_message_default


def subscribe_terminal_notify_topic(client: mqtt.Client, user_id: str):
    """订阅来自服务端的属性和方法 设置 on_terminal_message 回调
    v1_1/{user_id}/{device_id}/notifyAllProperty
    v1_1/{user_id}/{device_id}/propertyChanged

    v1_1/{user_id}/{device_id}/taskPose/{task_id}
    v1_1/{user_id}/{device_id}/message
    """
    client.subscribe(topic=f"v1_1/{user_id}/+/funcResp", qos=1)
    client.subscribe(topic=f"v1_1/{user_id}/+/notify", qos=1)
    client.subscribe(topic=f"v1_1/{user_id}/+/notifyAllProperty", qos=1)
    client.subscribe(topic=f"v1_1/{user_id}/+/propertyChanged", qos=1)
    client.subscribe(topic=f"v1_1/{user_id}/+/taskPose/+", qos=1)
    client.subscribe(topic=f"v1_1/{user_id}/+/message", qos=1)
    logger.info(f"subscribe to v1_1/{user_id}/+/funcResp")
    logger.info(f"subscribe to v1_1/{user_id}/+/notify")
    logger.info(f"subscribe to v1_1/{user_id}/+/notifyAllProperty")
    logger.info(f"subscribe to v1_1/{user_id}/+/propertyChanged")
    logger.info(f"subscribe to v1_1/{user_id}/+/taskPose/+")
    logger.info(f"subscribe to v1_1/{user_id}/+/message")
    client.on_message = on_terminal_message


def send_property_change(client: MqttClient, device_type: str, mac: str, prop: dict):
    """发送属性上报消息"""
    topic = f"$aws/rules/propertyChanged_v1_1/{device_type}/{mac}/propertyChanged"
    seq = get_seq_number(mac)  # type: ignore
    payload = {
        "code": 1,
        "payload": {
            "seq": seq,
            "timestamp": int(time.time()) * 1000,
            "property": prop,
        },
    }
    message = json.dumps(payload, separators=(",", ":"))
    rc, mid = client.publish(topic, message, qos=0)
    return_status = "" if rc == 0 else "\n发送失败"
    logger.info(f"{seq} {topic}\n{message}{return_status}")
    return rc


def send_func_response_message(
    client: MqttClient,
    device_type: str,
    mac: str,
    device_id: str,
    request_payload: dict,
    return_params: list,
):
    """发送方法响应消息"""
    topic = f"$aws/rules/funcResponse_v1_1/{device_type}/{mac}/funcResponse"
    function_id = request_payload.get("functionID")
    request_id = request_payload.get("requestID")
    params = {}
    for i in range(len(return_params)):
        params[str(i)] = return_params[i]
    payload = {
        "code": 1,
        "deviceID": device_id,
        "payload": {
            "functionID": function_id,
            "params": params,
            "requestID": request_id,
            "timestamp": int(time.time()) * 1000,
        },
        "sign": "",
        "version": "1.0",
    }
    message = json.dumps(payload, separators=(",", ":"))
    rc, mid = client.publish(topic, message, qos=0)
    return_status = "" if rc == 0 else "\n发送失败"
    logger.info(f"{topic}\n{message}{return_status}")
    return rc


@celery_app.task(queue="simulator_queue")
def stop_mqtt_clients():
    # 扫缓存，把离线的设备从列表里清除掉
    for client in mqtt_clients.values():
        client.loop_stop()
        client.disconnect()
    mqtt_clients.clear()
    return "MQTT clients stopped."


def get_device_info(topic: str):
    items = topic.split("/")
    if len(items) < 4:
        logger.warning(f"invalid topic {topic}")
        return False, f"invalid topic {topic}"
    device_type, mac = items[1], items[2]
    return device_type, mac


@celery_app.task(queue="simulator_queue")
def func_reply_task(topic: str, message: str):
    """回复方法响应和执行模拟动作

    先查询模板配置，按模版响应
    v1_1/WoSweeperOrigin/2DBA9D991A4C/funcRequest
    """
    device_type, mac = get_device_info(topic)
    if not device_type:
        return False
    client = mqtt_clients.get(get_client_id(mac, device_type))
    if not client:
        return False, "MQTT client maybe already stopped."
    payload = json.loads(message)
    req_payload = payload.get("payload", {})
    latest_callback_template = get_latest_callback_template(
        mac, req_payload.get("functionID")
    )
    if latest_callback_template:
        # logger.debug(latest_callback_template)
        config = latest_callback_template.get("callback_template", {})
        # 先回复
        send_func_response_message(
            client,
            device_type,
            mac,
            payload.get("deviceID"),
            req_payload,
            config["return"],
        )
        # vars = config.get("variables", {})
        # 主线线程不能执行长时间操作
        for action in config.get("actions", []):
            logger.info(action)
            if action.get("type") == "props":
                send_property_change(
                    client,
                    device_type,
                    mac,
                    action["value"],
                )
            elif action.get("type") == "robot_task":
                param = action.get("param", {})
                robot_task(client, device_type, mac, **param)


def publish(
    client: MqttClient, device_type: str, mac: str, task_id: str, stop_index: int = 100
):
    # {"data":[[0.934,-0.348,-3.05,1784699,1]],"endIndex":4420,"startIndex":4420}
    start_ms = time.time()
    index = 0
    topic = f"v1_1/{device_type}/{mac}/taskPose/{task_id}"
    position = []
    while True:
        if index > stop_index:
            break
        position = [generate_random_float() for _ in range(3)]
        duration = int((time.time() - start_ms) * 1000) * 10
        data = [position + [duration, 1]]
        data_length = len(data)
        message = {
            "data": data,
            "endIndex": index + data_length - 1,
            "startIndex": index,
        }
        payload = json.dumps(message, separators=(",", ":"))
        logger.info("Publishing message to topic '{}'\n{}".format(topic, payload))
        client.publish(topic=topic, payload=payload, qos=0)
        prop = {
            # "1022": str(position),  # 清扫过程中的当前点上报，后面已经取消了，清扫结束后再上报
            "1052": {
                "clean_area": 0.5 * index,
                "clean_time": 1,
                "clean_time_ms": duration,
                "duration": duration,
                "task_id": task_id,
                "total_area": 50.0,
            },
        }
        send_property_change(
            client,
            device_type,
            mac,
            prop,
        )
        index += data_length
        time.sleep(0.5)  # send message every
    if position:
        send_property_change(
            client,
            device_type,
            mac,
            {
                "1022": position,  # 清扫过程中的当前点上报，后面已经取消了，清扫结束后再上报
            },
        )


def robot_task(client: MqttClient, device_type: str, mac: str, **kwargs):
    """
    func 1001

    interval 1, num 2,  worke_time ++
    cur position: lastest position

    "type": "robot_task", # v1_1/WoSweeperOrigin/B0E9FE00048C/taskPose/320292aa-4588-41a8-b482-f109b7068ec0   /message mapUpdate
    "param": {"area": 100, "speed": 100}

    {"code":1,"deviceID":"B0E9FE00048C","payload":{"property":{"1022":"[0.375000,1.184000,3.089000]","1052":{"clean_area":0.0,"clean_time":1,"clean_time_ms":64768,"duration":78310,"task_id":"564059cc-66a4-450f-aa82-e921825ccd54","total_area":0.0}},"seq":182,"timestamp":1702463635214},"sign":"","version":"1.0"}


    """
    task_id = kwargs.get("task_id", "")
    # total_area = kwargs.get("area", None)  # 清扫面积
    # time = kwargs.get("time", None)  # 清扫次数
    # total_time = kwargs.get("total_time", None)  # 清扫时长
    publish(client, device_type, mac, task_id)
    # total_area = kwargs.get("area", None)  # 清扫面积
    # time = kwargs.get("time", None)  # 清扫次数
    # total_time = kwargs.get("total_time", None)  # 清扫时长
    publish(client, device_type, mac, task_id)
    publish(client, device_type, mac, task_id)


@shared_task(queue="celery")
def delete_old_property():
    """删除超过5天的property"""
    try:
        # Import here to avoid circular imports
        from app.simulator.models import (
            SimulateDeviceProperty,
            SimulateTerminalProperty,
        )

        days = 5
        logger.info(f"开始删除超过 {days} 天的property")

        days_ago = datetime.now() - timedelta(days=days)

        property_to_delete = SimulateDeviceProperty.objects.filter(
            created_at__lt=days_ago
        )
        count = property_to_delete.count()

        batch_delete(property_to_delete, SimulateDeviceProperty)

        logger.info(f"成功删除 {count} 个超过15天的DeviceProperty")

        property_to_delete = SimulateTerminalProperty.objects.filter(
            created_at__lt=days_ago
        )
        count = property_to_delete.count()

        batch_delete(property_to_delete, SimulateTerminalProperty)

        logger.info(f"成功删除 {count} 个超过15天的DeviceProperty")
        return f"删除了 {count} 个Property"
    except Exception as e:
        logger.error(f"删除旧Property时发生错误: {e}")
        raise
