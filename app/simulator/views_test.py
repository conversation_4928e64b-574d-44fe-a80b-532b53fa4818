import logging

import pytest
from rest_framework import status
from rest_framework.test import APIClient

logger = logging.getLogger(__name__)


class SimulatorViewTest:
    pytestmark = pytest.mark.django_db

    def test_product_list(self):
        url = "/api/simulate/product"  # 使用 reverse 函数获取视图的 URL

        client = APIClient()
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK  # 检查响应状态码

        logger.info(response.data)

    def test_alias_type_list(self):
        url = "/api/simulate/alias_type"

        client = APIClient()
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK  # 检查响应状态码

        logger.info(response.data)
