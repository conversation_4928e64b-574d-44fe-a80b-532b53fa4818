import gzip
import json
import logging
import time

from django.db import connection
from django.http.request import HttpRequest
from django.http.response import Http404
from django.utils.deprecation import MiddlewareMixin
from rest_framework.exceptions import APIException

from app.response import ErrorResponse

logger = logging.getLogger(__name__)


def custom_exception_handler(exc, context):
    if isinstance(exc, APIException):
        return ErrorResponse(json.dumps(exc.detail), _status=exc.status_code)

    elif isinstance(exc, Http404):
        return ErrorResponse(str(exc), _status=404)
    else:
        # ValidationError
        last_query = (
            connection.queries[-1].get("sql", "No queries")
            if connection.queries
            else "No queries"
        )
        logger.exception(exc)
        logger.info(f"Last SQL query: {last_query}")
        return ErrorResponse(str(exc), _status=500)


class GZipMiddlewareConditional(MiddlewareMixin):
    def process_response(self, request, response):
        # 检查请求头中是否接受 gzip 编码
        accept_encoding = request.headers.get("Accept-Encoding", "")
        if "gzip" not in accept_encoding:
            return response
        # 检查响应内容是否足够大（通常 gzip 压缩对小内容不太有效）
        if len(response.content) < 200:
            return response
        # 确保响应还没有被压缩
        if response.has_header("Content-Encoding"):
            return response
        # 压缩响应内容
        response.content = gzip.compress(response.content)
        response["Content-Encoding"] = "gzip"
        response["Content-Length"] = str(len(response.content))
        return response


class RequestTimingMiddleware(MiddlewareMixin):
    def process_request(self, request: HttpRequest):
        request.start_time = time.monotonic_ns()
        request.start_process = time.process_time()
        # if logger.isEnabledFor(logging.INFO):
        #     path = request.path
        #     if request.FILES:
        #         logger.info(path)
        #     else:
        #         try:
        #             body = request.body.decode("utf-8") if request.body else ""
        #         except UnicodeDecodeError:
        #             body = "<non-decodable body>"
        #         logger.info(f"{path} {body}")

    def process_response(self, request, response):
        start_time = getattr(request, "start_time", None)
        if start_time is not None:
            duration = (time.monotonic_ns() - start_time) / 1000000
            response["X-Backend-Duration-ms"] = duration
        else:
            duration = 0

        start_process = getattr(request, "start_process", None)
        if start_process is not None:
            process_duration = (time.process_time() - start_process) * 1000
            response["X-Process-Duration-ms"] = process_duration
        else:
            process_duration = 0

        if logger.isEnabledFor(logging.INFO):
            path = request.get_full_path()
            logger.info(f"{path} {duration:.2f} ms | process {process_duration:.2f} ms")
        return response
