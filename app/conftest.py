import pytest
from django.contrib.auth.models import User
from rest_framework.test import APIClient

# def pytest_collection_modifyitems(config, items):
#     for item in items:
#         item.add_marker(pytest.mark.django_db(django_db_createdb=True))


@pytest.fixture
def login_user(django_db_setup):
    # 创建用户
    User.objects.create_user(username="testuser", password="testpassword")
    # 登录用户
    client = APIClient()
    login = client.login(username="testuser", password="testpassword")
    assert login is True
    client.get("/api/token")
    return client.cookies
