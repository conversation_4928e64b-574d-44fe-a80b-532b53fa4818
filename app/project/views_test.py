import logging

import pytest
from rest_framework import status
from rest_framework.test import APIClient

from .models import Project
from .serializers import ProjectSerializer

logger = logging.getLogger(__name__)


class ProjectViewTest:
    pytestmark = pytest.mark.django_db

    def test_project_list(self):
        url = "/api/project/"  # 使用 reverse 函数获取视图的 URL

        client = APIClient()
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK  # 检查响应状态码

        logger.info(response.data)

    def test_add_project(self, login_user):
        url = "/api/project/"
        data = {
            "name": "string",
            "description": "string",
        }
        print(login_user)

        # 发起 POST 请求
        client = APIClient()
        response = client.post(url, data, format="json", cookies=login_user)

        print(response.data)
        assert response.status_code == status.HTTP_201_CREATED  # 检查响应状态码

        # 检查数据库中是否创建了新的对象
        assert Project.objects.count() == 1
        assert Project.objects.first().foo == "bar"

        # 检查响应数据是否与预期的序列化结果相匹配
        assert response.data == ProjectSerializer(Project.objects.first()).data
        assert response.data == ProjectSerializer(Project.objects.first()).data
