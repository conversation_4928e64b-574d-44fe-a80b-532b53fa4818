import logging

import redis.asyncio as aioredis  # fix error for aioredis at python3.11
from channels.generic.websocket import AsyncWebsocketConsumer
from django.conf import settings

logger = logging.getLogger(__name__)


class LogConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.queue_name = self.scope["url_route"]["kwargs"]["queue_name"]
        self.redis = await aioredis.from_url(settings.CELERY_BROKER_URL)
        await self.accept()
        # 启动一个循环来监听Redis队列
        while True:
            message = await self.redis.blpop(self.queue_name)
            if message:
                _, text = message
                await self.send(text.decode("utf-8"))

    async def disconnect(self, close_code):
        await self.redis.close()

    # 处理从WebSocket接收到的消息（如果需要）
    async def receive(self, text_data):
        pass
