# 自动化测试平台 FE

## 线上部署（prod-hk 环境，与 devops 平台一起）

```
aws ecr create-repository --repository-name switchbot/autoswitchbot-fe --image-tag-mutability IMMUTABLE --region us-east-1 --profile test
aws ecr get-login-password --region us-east-1 --profile test | docker login --username AWS --password-stdin 247192748490.dkr.ecr.us-east-1.amazonaws.com
```

```
# prod us ecr
aws ecr get-login-password --region us-east-1 --profile prod | docker login --username AWS --password-stdin 443283509441.dkr.ecr.us-east-1.amazonaws.com

docker build -t switchbot/autoswitchbot-fe:release --no-cache --platform linux/arm64 .

docker tag switchbot/autoswitchbot-fe:release 443283509441.dkr.ecr.us-east-1.amazonaws.com/switchbot/autoswitchbot-fe:2024.3.9.1539
docker push 443283509441.dkr.ecr.us-east-1.amazonaws.com/switchbot/autoswitchbot-fe:2024.3.9.1539

```

## openapi 文件生成

`curl http://127.0.0.1:8000/schema/ -o config/oneapi.json`

http://127.0.0.1:8000/schema/swagger-ui

# Ant Design Pro

## Environment Prepare

Install `node_modules`:

```bash
ns -f

npm install yarn tyarn -g

tyarn
```

## Provided Scripts

Ant Design Pro provides some useful script to help you quick start and build with web project, code style check and test.

Scripts provided in `package.json`. It's safe to modify or add additional script:

### Start project

```bash
npm start
```

### Build project

```bash
npm run build
```

### Check code style

```bash
npm run lint
```

You can also use script to auto fix some lint error:

```bash
npm run lint:fix
```

### Test code

```bash
npm test
```

## More

You can view full document on our [official website](https://pro.ant.design). And welcome any feedback in our [github](https://github.com/ant-design/ant-design-pro).

devDependency

```json
// "@ant-design/pro-cli": "^2.1.5",
// "@umijs/fabric": "^2.14.1",

```
