# SwitchBotRunner

`SwitchBotRunner` fork from `HttpRunner`

## Usage

### System requirements

Poetry requires Python 3.8+

### Setup

- Install poetry [https://python-poetry.org/docs/#installation](https://python-poetry.org/docs/#installation)

- Install aws-cli [https://docs.aws.amazon.com/zh_cn/cli/latest/userguide/getting-started-install.html](https://docs.aws.amazon.com/zh_cn/cli/latest/userguide/getting-started-install.html)

- aws-cli configuration [https://docs.aws.amazon.com/zh_cn/cli/latest/userguide/cli-configure-files.html](https://docs.aws.amazon.com/zh_cn/cli/latest/userguide/cli-configure-files.html)

### Run tests

- Install switchbot-runner

`poetry install`

- activate virtualenv

`poetry shell`

- Run test

`srun demo_requests.yml --save-tests`

# ChangeLog

[CHANGELOG]

[CHANGELOG]: docs/CHANGELOG.md
