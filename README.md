```
INSERT INTO public.auth_user (id, password, last_login, is_superuser, username, first_name, last_name, email, is_staff, is_active, date_joined) VALUES (1000, '!', '2024-03-07 14:16:42.712000 +00:00', false, 'DevOps', 'DevOps', '', '', false, true, '2024-03-07 14:17:17.744000 +00:00')

python manage.py shell

from django.contrib.auth.models import User
User.objects.create_user('devops','devops')

python manage.py drf_create_token devops

# 9a868c56a410cba23b6903c86a248f9bfeead7a6
```

## 接口列表

http://127.0.0.1:8000/schema/swagger-ui

## uv

https://docs.astral.sh/uv/

## django-allauth feishu

https://dj-rest-auth.readthedocs.io/en/latest/

https://django-allauth.readthedocs.io/en/latest/account/configuration.html

## openapi 3.0

drf-spectacular

swagger-ui

https://drf-spectacular.readthedocs.io/en/latest/customization.html

## orm cache

http://django-cachalot.readthedocs.io
