import { ModalForm, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import type { FC } from 'react';
import useStyles from '../style.style';
type OperationModalProps = {
  done: boolean;
  open: boolean;
  current: API.Project;
  onDone: () => void;
  onSubmit: (values: API.Project) => void;
  children?: React.ReactNode;
};
const OperationModal: FC<OperationModalProps> = (props) => {
  const { styles } = useStyles();
  const { done, open, current, onDone, onSubmit, children } = props;
  if (!open) {
    return null;
  }
  return (
    <ModalForm<API.Project>
      open={open}
      title={done ? null : `${current ? '编辑' : '新增'}项目`}
      className={styles.standardListForm}
      width={640}
      onFinish={async (values) => {
        onSubmit(values);
        // 删除了提交后的 Result, 这里直接完成
        onDone();
      }}
      initialValues={current}
      submitter={{
        render: (_, dom) => (done ? null : dom),
      }}
      trigger={<>{children}</>}
      modalProps={{
        onCancel: () => onDone(),
        destroyOnClose: true,
        styles: {
          body: done ? { padding: '72px 0' } : {},
        },
      }}
    >
      {!done ? (
        <>
          <ProFormText name="id" hidden />
          <ProFormText
            name="name"
            label="项目名称"
            rules={[
              {
                required: true,
                message: '请输入项目名称',
              },
            ]}
            placeholder="请输入"
          />
          <ProFormTextArea
            name="desc"
            label="项目描述"
            rules={[
              {
                required: true,
                message: '请输入至少三个字符的项目描述！',
                min: 3,
              },
            ]}
            placeholder="请输入至少三个字符"
          />
        </>
      ) : (
        <></>
      )}
    </ModalForm>
  );
};
export default OperationModal;
