import { request } from '@umijs/max';

/** 获取项目列表 GET /api/project */
export async function ProjectListApi(params: API.Pagination, options?: { [key: string]: any }) {
  return request<API.ProjectList>('/api/project', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 新增项目 POST /api/project */
export async function ProjectCreateApi(body: API.Project, options?: { [key: string]: any }) {
  return request<API.Project>('/api/project', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除项目 DELETE /api/project */
export async function ProjectBatchDeleteApi(body: API.IdsParam, options?: { [key: string]: any }) {
  return request<any>('/api/project', {
    method: 'DELETE',
    data: body,
    ...(options || {}),
  });
}

/** 获取单个项目 GET /api/project/${param0} */
export async function ProjectDetailApi(params: API.IdParam, options?: { [key: string]: any }) {
  const { id: param0, ...queryParams } = params;
  return request<API.Project>(`/api/project/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改单个项目 PUT /api/project/${param0} */
export async function ProjectUpdateApi(
  params: API.IdParam,
  body: API.Project,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Project>(`/api/project/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除单个项目 DELETE /api/project/${param0} */
export async function ProjectDeleteApi(params: API.IdParam, options?: { [key: string]: any }) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/api/project/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}
