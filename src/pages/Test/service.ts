import { request } from '@umijs/max';

/** 测试用例列表 GET /api/testcase */
export async function TestcaseListApi(params: API.Pagination, options?: { [key: string]: any }) {
  return request<API.TestCaseList>('/api/testcase', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量删除接口 DELETE /api/testcase */
export async function TestcaseBatchDeleteApi(body: API.IdsParam, options?: { [key: string]: any }) {
  return request<any>('/api/testcase', {
    method: 'DELETE',
    data: body,
    ...(options || {}),
  });
}

/** 新增测试用例 POST /api/testcase */
export async function TestcaseCreateApi(body: API.TestCase, options?: { [key: string]: any }) {
  return request<API.TestCase>('/api/testcase', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取单个测试用例 GET /api/testcase/${id} */
export async function TestcaseDetailApi(params: API.IdParam, options?: { [key: string]: any }) {
  const { id: id, ...queryParams } = params;
  return request<any>(`/api/testcase/${id}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改单个测试用例 PUT /api/testcase/${id} */
export async function TestcaseUpdateApi(
  params: API.IdParam,
  body: API.TestCase,
  options?: { [key: string]: any },
) {
  const { id: id, ...queryParams } = params;
  return request<any>(`/api/testcase/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除单个测试用例 DELETE /api/testcase/${id} */
export async function TestcaseDeleteApi(params: API.IdParam, options?: { [key: string]: any }) {
  const { id: id } = params;
  return request<any>(`/api/testcase/${id}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/** 获取单个测试用例数据 GET /api/testcase/${id} */
export async function TestcaseDataDetailApi(params: API.IdParam, options?: { [key: string]: any }) {
  const { id: id, ...queryParams } = params;
  return request<API.TestCaseData>(`/api/testcasedata/${id}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改单个测试用例数据 PUT /api/testcase/${id} */
export async function TestcaseDataUpdateApi(
  params: API.IdParam,
  body: API.TestCaseData,
  options?: { [key: string]: any },
) {
  const { id: id, ...queryParams } = params;
  return request<any>(`/api/testcasedata/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 获取项目的测试用例目录 GET /api/testcase/dir/${project_id} */
export async function TestcaseDirApi(params: API.ProjectIdParam, options?: { [key: string]: any }) {
  const { project_id: project_id, ...queryParams } = params;
  return request<API.TestCaseDirectory>(`/api/testcase/dir/${project_id}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 新增测试用例（子）目录 POST /api/testcase/dir/${project_id} */
export async function TestcaseDirCreateApi(
  params: API.ProjectIdParam,
  body: API.TestcaseDir,
  options?: { [key: string]: any },
) {
  const { project_id: project_id, ...queryParams } = params;
  return request<any>(`/api/testcase/dir/${project_id}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 修改测试用例目录名称 PUT /api/testcase/dir/${project_id} */
export async function TestcaseDirUpdateApi(
  params: API.ProjectIdParam,
  body: API.TestcaseDir,
  options?: { [key: string]: any },
) {
  const { project_id: project_id, ...queryParams } = params;
  return request<any>(`/api/testcase/dir/${project_id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除项目的单个测试用例目录 DELETE /api/testcase/dir/${project_id}/${key} */
export async function TestcaseDirDeleteApi(
  params: { key: string; project_id: string },
  options?: { [key: string]: any },
) {
  const { key: key, project_id: project_id, ...queryParams } = params;
  return request<any>(`/api/testcase/dir/${project_id}/${key}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 运行测试用例 POST /api/testcase/run */
export async function TestcaseRunApi(body: API.RunTestCases, options?: { [key: string]: any }) {
  return request<any>('/api/testcase/run', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 按目录运行测试用例 POST /api/testcase/runbydir */
export async function TestcaseRunbydirApi(
  body: API.RunTestCasesByDir,
  options?: { [key: string]: any },
) {
  return request<any>('/api/testcase/runbydir', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 按项目运行测试用例 POST /api/testcase/runbyproject */
export async function TestcaseRunbyprojectApi(
  body: API.RunTestCasesByProject,
  options?: { [key: string]: any },
) {
  return request<any>('/api/testcase/runbyproject', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

const getFormData = (body: API.TestCaseUpload) => {
  const formData = new FormData();

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (item instanceof Array) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });
  return formData;
};

/** 测试用例上传更新 PUT /api/testcase/upload */
export async function TestcaseUploadUpdateApi(
  body: API.TestCaseUpload,
  options?: { [key: string]: any },
) {
  const formData = getFormData(body);

  return request<API.TestCaseUpload>('/api/testcase/upload', {
    method: 'PUT',
    data: formData,
    requestType: 'form',
    ...(options || {}),
  });
}

/** 测试用例上传新增 POST /api/testcase/upload */
export async function TestcaseUploadCreateApi(
  body: API.TestCaseUpload,
  options?: { [key: string]: any },
) {
  const formData = getFormData(body);

  return request<API.TestCaseUpload>('/api/testcase/upload', {
    method: 'POST',
    data: formData,
    requestType: 'form',
    ...(options || {}),
  });
}

/** 获取测试报告列表 GET /api/testreport */
export async function TestreportListApi(params: API.Pagination, options?: { [key: string]: any }) {
  return request<API.TestReportList>('/api/testreport', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 新增测试报告 POST /api/testreport */
export async function TestreportCreateApi(body: API.TestReport, options?: { [key: string]: any }) {
  return request<API.TestReport>('/api/testreport', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量测试报告 DELETE /api/testreport */
export async function TestreportBatchDeleteApi(
  body: API.IdsParam,
  options?: { [key: string]: any },
) {
  return request<any>('/api/testreport', {
    method: 'DELETE',
    data: body,
    ...(options || {}),
  });
}

/** 获取单个测试报告 GET /api/testreport/${id} */
export async function TestreportDetailApi(params: API.IdParam, options?: { [key: string]: any }) {
  const { id: id, ...queryParams } = params;
  return request<API.TestReportDetail>(`/api/testreport/${id}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改单个测试报告 PUT /api/testreport/${id} */
export async function TestreportDetailUpdateApi(
  params: API.IdParam,
  body: API.TestReportDetail,
  options?: { [key: string]: any },
) {
  const { id: id, ...queryParams } = params;
  return request<API.TestReportDetail>(`/api/testreport/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除单个测试报告 DELETE /api/testreport/${id} */
export async function TestreportDeleteApi(params: API.IdParam, options?: { [key: string]: any }) {
  const { id: id, ...queryParams } = params;
  return request<any>(`/api/testreport/${id}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取测试计划列表 GET /api/testplan */
export async function TestPlanListApi(params: API.Pagination, options?: { [key: string]: any }) {
  return request<API.TestPlanList>('/api/testplan', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 新增测试计划 POST /api/testplan */
export async function TestPlanCreateApi(body: API.TestReport, options?: { [key: string]: any }) {
  return request<API.TestReport>('/api/testplan', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量测试计划 DELETE /api/testplan */
export async function TestPlanBatchDeleteApi(body: API.IdsParam, options?: { [key: string]: any }) {
  return request<any>('/api/testplan', {
    method: 'DELETE',
    data: body,
    ...(options || {}),
  });
}

/** 获取单个测试计划 GET /api/testplan/${id} */
export async function TestPlanDetailApi(params: API.IdParam, options?: { [key: string]: any }) {
  const { id: id, ...queryParams } = params;
  return request<API.TestPlan>(`/api/testplan/${id}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改单个测试计划 PUT /api/testplan/${id} */
export async function TestPlanUpdateApi(
  params: API.IdParam,
  body: API.TestPlan,
  options?: { [key: string]: any },
) {
  const { id: id, ...queryParams } = params;
  return request<API.TestPlan>(`/api/testplan/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除单个测试计划 DELETE /api/testplan/${id} */
export async function TestPlanDeleteApi(params: API.IdParam, options?: { [key: string]: any }) {
  const { id: id, ...queryParams } = params;
  return request<any>(`/api/testplan/${id}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 运行测试用例 POST /api/testplan/run */
export async function TestPlanRunApi(body: API.RunTestCases, options?: { [key: string]: any }) {
  return request<any>('/api/testplan/run', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
