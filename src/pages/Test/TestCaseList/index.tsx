import { EnvListApi } from '@/pages/Base/service';
import { ProjectListApi } from '@/pages/Project/service';
import { LeftOutlined, RightOutlined, UploadOutlined } from '@ant-design/icons';
import {
  ActionType,
  FooterToolbar,
  Key,
  ModalForm,
  PageContainer,
  ProColumns,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { connect, useLocation, useRequest } from '@umijs/max';
import type { MenuProps, UploadProps } from 'antd';
import {
  Button,
  Col,
  Dropdown,
  Layout,
  Popconfirm,
  Row,
  Select,
  Space,
  Tag,
  Tree,
  Upload,
  message,
} from 'antd';
import type { RcFile } from 'antd/es/upload/interface';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  TestcaseBatchDeleteApi,
  TestcaseDirApi,
  TestcaseDirCreateApi,
  TestcaseDirDeleteApi,
  TestcaseDirUpdateApi,
  TestcaseListApi,
  TestcaseRunApi,
  TestcaseUpload<PERSON>reate<PERSON><PERSON>,
  TestcaseUploadUpdateApi,
} from '../service';
import CaseDetailDrawer from './components/CaseDetail';
import CreateForm from './components/CreateForm';
import LogModal from './components/LogModal';
import UpdateForm from './components/UpdateForm';
import useStyles from './style.style';
const { Sider, Content } = Layout;
const { DirectoryTree } = Tree;
interface TreeNode {
  key: string | null;
  title: string;
  parent_id?: string | null;
}
// 定义Select选项接口
interface Option {
  value: string;
  label: string;
}
interface TestCaseListProps {
  dispatch?: any;
  base?: any;
  common?: any;
}
const TestCaseList: React.FC<TestCaseListProps> = (props) => {
  const { styles } = useStyles();
  const [selectedKeys, setSelectedKeys] = useState<Key[]>([]);
  const [collapsed, setCollapsed] = useState<boolean>(false);
  const [projects, setProjects] = useState<Option[]>([]);
  const [project, setProject] = useState<string>();
  const [envs, setEnvs] = useState<Option[]>([]);
  const [selectedEnvId, setSelectedEnvId] = useState<string>();
  const [treeData, setTreeData] = useState<TreeNode[]>([
    {
      key: '0-0',
      title: '根目录',
    },
  ]);
  const [dirChangeOpen, setDirChangeOpen] = useState<boolean>(false);
  const [dirAction, setDirAction] = useState<string>('');
  const [currentDir, setCurrentDir] = useState<TreeNode>();
  const [fileList, setFileList] = useState<RcFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [selectedRowsState, setSelectedRows] = useState<API.TestCase[]>([]); // TODO: 改为selectedRowKeys
  const actionRef = useRef<ActionType>();
  const [logOpen, setLogOpen] = useState(false);
  const [taskId, setTaskId] = useState<string>('');
  // caseDetailModal
  const [detailOpen, setDetailOpen] = useState(false);
  const [caseId, setCaseId] = useState<string>('');
  const [detailTitle, setDetailTitle] = useState<string>('');
  const [messageApi, contextHolder] = message.useMessage();
  const { dispatch, base } = props;

  // 从 URL 参数中读取状态
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const projectIdFromUrl = searchParams.get('project_id');
  const directoryIdFromUrl = searchParams.get('directory_id');
  const envIdFromUrl = searchParams.get('env_id');
  if (projectIdFromUrl || directoryIdFromUrl || envIdFromUrl) {
    dispatch({
      type: 'common/updateState',
      payload: {
        project_id: projectIdFromUrl,
        directory_id: directoryIdFromUrl,
        env_id: envIdFromUrl,
      },
    });
  }
  const { run: fetchDirTree } = useRequest(
    (project_id: string) => {
      return TestcaseDirApi({
        project_id: project_id,
      });
    },
    {
      manual: true,
      onSuccess: (result) => {
        setTreeData([
          {
            key: '0-0',
            title: '根目录',
            children: (result as API.TestCaseDirectory[]) || [],
          },
        ]);
        if (directoryIdFromUrl) {
          setSelectedKeys([directoryIdFromUrl]);
        }
      },
    },
  );
  const { run: fetchProjects } = useRequest(
    () => {
      return ProjectListApi({
        pageSize: 1000,
      });
    },
    {
      manual: true,
      onSuccess: (result) => {
        if (!result) {
          return;
        }
        const projectOptions: Option[] = result.map((project) => ({
          value: project.id,
          label: project.name,
        }));
        setProjects(projectOptions);
        const projectIdExists = projectOptions.some((option) => option.value === projectIdFromUrl);
        if (projectIdFromUrl && projectIdExists) {
          // 如果 url 参数有，并且存在于 projectOptions 中，则选择该项目
          setProject(projectIdFromUrl);
        } else {
          setProject(projectOptions[projectOptions.length - 1].value);
        }
      },
    },
  );
  const { run: fetchEnv } = useRequest(
    () => {
      return EnvListApi({
        pageSize: 1000,
      });
    },
    {
      manual: true,
      onSuccess: (result) => {
        const envOptions: Option[] = result.map((env) => ({
          value: env.id,
          label: `${env.key}(${env.region})`,
        }));
        setEnvs(envOptions);
        const envIdExists = envOptions.some((option) => option.value === envIdFromUrl);
        if (envIdFromUrl && envIdExists) {
          // 如果 url 参数有，并且存在于 envOptions 中，则选择该项目
          setSelectedEnvId(envIdFromUrl);
        } else {
          setSelectedEnvId(envOptions[envOptions.length - 1].value);
        }
      },
    },
  );

  // 挂载时，获取项目列表
  useEffect(() => {
    fetchProjects();
    fetchEnv();
    dispatch({
      type: 'base/fetchTags',
    }); // 在父组件中获取一次
  }, [dispatch]);

  // 当项目 id 发生变化时，重新获取目录信息
  useEffect(() => {
    if (project) {
      fetchDirTree(project);
      dispatch({
        type: 'common/syncUrlParams',
        payload: {
          project_id: project,
        },
      });
    }
  }, [project]);
  useEffect(() => {
    if (selectedKeys[0] !== '0-0') {
      dispatch({
        type: 'common/syncUrlParams',
        payload: {
          directory_id: selectedKeys[0],
        },
      });
    }
  }, [selectedKeys]);
  useEffect(() => {
    if (selectedEnvId) {
      dispatch({
        type: 'common/syncUrlParams',
        payload: {
          env_id: selectedEnvId,
        },
      });
    }
  }, [selectedEnvId]);
  const onTreeSelect = (keys: Key[]) => {
    console.debug('onTreeSelect', keys);
    setSelectedKeys(keys);
    // if (keys[0] !== '0-0') {
    //   setDirectory(keys[0]);
    // } else {
    //   setDirectory(null);
    // }
  };

  const { run: batchRun } = useRequest(TestcaseRunApi, {
    manual: true,
    onSuccess: (data) => {
      // 这里直接得到的是resp.data
      setTaskId(data);
      messageApi.success('运行成功');
      setLogOpen(true);
    },
    onError: () => {
      messageApi.error('运行失败，请重试');
    },
  });
  const handleBatchRun = useCallback(
    async (selectedRows: API.TestCase[]) => {
      if (!selectedRows?.length) {
        messageApi.warning('请选择测试用例');
        return;
      }
      console.debug('batchrun', selectedEnvId);
      await batchRun({
        id: selectedRows.map((row) => row.id.toString()),
        env: selectedEnvId as string,
      });
    },
    [batchRun, selectedEnvId],
  );

  const columns: ProColumns<API.TestCase>[] = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      hideInSearch: true,
      width: 50,
      // hideInTable: true,
    },
    {
      title: '用例名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'desc',
      key: 'desc',
      width: 100,
      ellipsis: true,
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      width: 60,
      render: (_, { priority }) => (
        <Tag
          color={
            priority === 'P0'
              ? 'red'
              : priority === 'P1'
              ? 'orange'
              : priority === 'P2'
              ? 'green'
              : priority === 'P3'
              ? 'blue'
              : ''
          }
        >
          {priority}
        </Tag>
      ),
    },
    {
      title: '标签',
      dataIndex: 'tags',
      width: 60,
      render: (_, { tags }) => {
        return tags.map((id, index) => <Tag key={index}>{base.tags[id].label}</Tag>);
      },
      renderFormItem: () => {
        return (
          <Select allowClear>
            <Select.Option value="account_api1">account_api1</Select.Option>
            <Select.Option value="robot_api">robot_api</Select.Option>
          </Select>
        );
      },
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
      key: 'created_by',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      valueType: 'dateTime',
      width: 160,
    },
    {
      title: '修改人',
      dataIndex: 'updated_by',
      key: 'updated_by',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '修改时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      valueType: 'dateTime',
      width: 160,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      render: (_, record) => [
        <a
          key="edit"
          onClick={() => {
            setCaseId(record.id);
            setDetailTitle(`${record.id} ${record.name}`);
            setDetailOpen(true);
            // history.push(`/test/testcase/${record.id}`, {
            //   project: project,
            //   directory: selectedKeys[0],
            //   env: selectedEnvId,
            // });
          }}
        >
          编辑
        </a>,
        <UpdateForm
          key="update"
          trigger={<a>修改</a>}
          onOk={actionRef.current?.reload}
          values={record}
        />,
        <a
          key="run"
          onClick={() => {
            batchRun({
              id: [record.id],
              env: selectedEnvId as string,
            });
          }}
        >
          运行
        </a>,
      ],
    },
  ];

  const handleProjectChange = (selectedOption: string) => {
    console.debug(selectedOption);
    setProject(selectedOption);
  };
  const handleEnvChange = (selectedOption: string) => {
    console.debug(selectedOption);
    setSelectedEnvId(selectedOption);
  };
  const items: MenuProps['items'] = [
    {
      label: '增加子目录',
      key: 'add',
    },
    {
      label: '修改目录名',
      key: 'update',
    },
    {
      label: '删除目录',
      key: 'remove',
    },
  ];
  const handleDirSubmit = async (values) => {
    const hide = message.loading('开始处理');
    try {
      if (dirAction === 'remove') {
        await TestcaseDirDeleteApi({
          project_id: project as string,
          key: values?.key,
        });
      } else if (dirAction === 'update') {
        await TestcaseDirUpdateApi(
          {
            project_id: project as string,
          },
          values,
        );
      } else {
        await TestcaseDirCreateApi(
          {
            project_id: project as string,
          },
          {
            key: null,
            ...values,
          },
        );
      }
      hide();
      message.success('操作成功');
      setDirChangeOpen(false);
      fetchDirTree(project as string);
    } catch (error) {
      hide();
      message.error('操作失败，请重试！');
    }
  };
  const isYamlFile = (filename: string) => {
    return /\.(yaml|yml)$/i.test(filename);
  };
  const uploadProps: UploadProps = {
    name: 'file',
    directory: true,
    onRemove: (file) => {
      setFileList((prevFileList) => prevFileList.filter((item) => item.uid !== file.uid));
    },
    beforeUpload: (file: RcFile) => {
      if (isYamlFile(file.name)) {
        // 使用函数式更新确保每次都是基于最新的 fileList 状态
        setFileList((prevFileList: RcFile[]) => {
          // 首先过滤掉所有同名但修改时间不同的旧文件
          const filteredFileList = prevFileList.filter(
            (existingFile) =>
              // !(existingFile.name === file.name && existingFile.lastModified !== file.lastModified),
              !(existingFile.name === file.name),
          );
          // 然后添加新文件到过滤后的文件列表中
          const updatedFileList = [...filteredFileList, file];
          return updatedFileList;
        });
      }
      return false;
    },
    fileList,
  };
  const handleUpload = (update: boolean = false) => {
    if (selectedKeys.length < 1) {
      message.error(`请选择一个非根目录后再上传`);
      return;
    }
    if (selectedKeys[0] === '0') {
      message.error(`请选择一个非根目录后再上传`);
      return;
    }
    let directory = selectedKeys[0];
    setUploading(true);
    if (update) {
      TestcaseUploadUpdateApi({
        project_id: project as string,
        env: selectedEnvId as string,
        directory: directory as string,
        file: fileList,
      })
        .then(() => {
          setFileList([]);
          message.success('更新用例成功');
        })
        .catch(() => {
          message.error(`文件上传失败.`);
        })
        .finally(() => {
          setUploading(false);
          actionRef.current?.reload?.();
        });
    } else {
      TestcaseUploadCreateApi({
        project_id: project as string,
        env: selectedEnvId as string,
        directory: directory as string,
        file: fileList,
      })
        .then(() => {
          setFileList([]);
          message.success('新增用例成功');
        })
        .catch(() => {
          message.error(`文件上传失败.`);
        })
        .finally(() => {
          setUploading(false);
          actionRef.current?.reload?.();
        });
    }
  };
  const { run: delRun } = useRequest(TestcaseBatchDeleteApi, {
    manual: true,
    onSuccess: () => {
      setSelectedRows([]);
      actionRef.current?.reloadAndRest?.();
      messageApi.success('Deleted successfully and will refresh soon');
    },
    onError: () => {
      messageApi.error('Delete failed, please try again');
    },
  });
  const handleRemove = useCallback(
    async (selectedRows: API.TestCase[]) => {
      if (!selectedRows?.length) {
        messageApi.warning('请选择删除项');
        return;
      }
      await delRun({
        id: selectedRows.map((row) => row.id),
      });
    },
    [delRun],
  );
  const handleAddOpen = () => {
    if (selectedKeys.length < 1) {
      message.error(`请选择一个非根目录后再新建`);
      return false;
    }
    if (selectedKeys[0] === '0') {
      message.error(`请选择一个非根目录后再新建`);
      return false;
    }
    return true;
  };
  return (
    <PageContainer title={false} breadcrumbRender={false}>
      {contextHolder}
      <Layout>
        <Row>
          <Col>
            <Select
              key="project"
              options={projects}
              value={project}
              style={{
                width: 200,
              }}
              onChange={handleProjectChange}
            />
          </Col>
          <Col>
            <Select
              key="env"
              options={envs}
              value={selectedEnvId}
              style={{
                width: 200,
              }}
              onChange={handleEnvChange}
            />
          </Col>
        </Row>
        <Layout>
          <Sider theme="light" collapsible collapsed={collapsed} collapsedWidth={0} trigger={null}>
            <DirectoryTree
              onSelect={onTreeSelect}
              className={styles.siderItem} // 给折叠按钮留距离
              treeData={treeData}
              defaultExpandedKeys={['0-0']}
              defaultSelectedKeys={[directoryIdFromUrl as Key]} // 根据 url 参数默认选中目录
              // onRightClick={onRightClick}
              titleRender={(node: TreeNode) => {
                return (
                  <Dropdown
                    menu={{
                      items,
                      onClick: (e) => {
                        if (e.key === 'add') {
                          // 增加子节点，如果key存在，则把key作为parent_id调用后端
                          setCurrentDir({
                            key: node?.key === '0-0' ? null : node?.key, // 根目录新增子目录
                            title: node?.title,
                            parent_id: node?.key === '0-0' ? null : node?.key,
                          });
                        } else {
                          setCurrentDir(node);
                        }
                        setDirChangeOpen(true);
                        setDirAction(e.key);
                      },
                    }}
                    trigger={['contextMenu']}
                  >
                    <span className="ant-tree-title">{node?.title}</span>
                  </Dropdown>
                );
              }}
            />
            <ModalForm
              title={
                dirAction === 'add'
                  ? '新增子目录'
                  : dirAction === 'update'
                  ? '修改目录名'
                  : dirAction === 'remove'
                  ? '删除目录'
                  : ''
              }
              open={dirChangeOpen}
              onFinish={async (values) => {
                handleDirSubmit(values);
              }}
              initialValues={currentDir}
              width="400px"
              modalProps={{
                onCancel: () => setDirChangeOpen(false),
                destroyOnClose: true,
              }}
            >
              <ProFormText name="key" hidden />
              <ProFormText name="parent_id" hidden />
              {dirAction === 'remove' ? (
                <></>
              ) : (
                <ProFormText
                  name="title"
                  label="目录名"
                  rules={[
                    {
                      required: true,
                      message: '请输入目录名',
                    },
                  ]}
                  placeholder="请输入"
                />
              )}
            </ModalForm>
          </Sider>
          <Button
            type="text"
            icon={
              collapsed ? (
                <RightOutlined className={styles.collapseIcon} />
              ) : (
                <LeftOutlined className={styles.collapseIcon} />
              )
            }
            onClick={() => {
              setCollapsed(!collapsed);
            }}
            className={`${styles.collapseButton} ant-pro-sider-collapsed-button`}
          />
          <Content>
            <ProTable<API.TestCase>
              toolBarRender={() => [
                <CreateForm
                  key="create"
                  projectState={project as string}
                  directoryState={selectedKeys[0] as string}
                  onOpen={handleAddOpen}
                  reload={actionRef.current?.reload}
                />,
                <Upload key="upload" {...uploadProps}>
                  <Button icon={<UploadOutlined />}>选择用例文件</Button>
                </Upload>,
                <Button
                  key="upload_new"
                  type="primary"
                  onClick={() => handleUpload()}
                  disabled={fileList.length === 0}
                  loading={uploading}
                >
                  {uploading ? '上传中' : '上传新增'}
                </Button>,
                <Button
                  key="upload_update"
                  type="primary"
                  onClick={() => handleUpload(true)}
                  disabled={fileList.length === 0}
                  loading={uploading}
                >
                  {uploading ? '上传中' : '上传更新'}
                </Button>,
              ]}
              rowKey={'id'}
              columns={columns}
              request={TestcaseListApi}
              params={{
                directory: selectedKeys[0] === '0-0' ? directoryIdFromUrl : selectedKeys[0],
                project,
              }}
              actionRef={actionRef}
              rowSelection={{
                onChange: (_, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
                selectedRowKeys: selectedRowsState.map((row) => row.id),
                preserveSelectedRowKeys: true,
              }}
              tableAlertRender={false}
            />
            {selectedRowsState?.length > 0 && (
              <FooterToolbar
                extra={
                  <div>
                    已选择{' '}
                    <a
                      style={{
                        fontWeight: 600,
                      }}
                    >
                      {selectedRowsState.length}
                    </a>{' '}
                    项 &nbsp;&nbsp;
                  </div>
                }
              >
                <Space size={16}>
                  <Button
                    type="primary"
                    onClick={async () => {
                      handleBatchRun(selectedRowsState);
                    }}
                  >
                    批量运行
                  </Button>
                  <Popconfirm
                    title="确定要删除吗?"
                    onConfirm={async () => {
                      handleRemove(selectedRowsState);
                    }}
                  >
                    <Button danger onClick={async () => {}}>
                      批量删除
                    </Button>
                  </Popconfirm>

                  <a
                    onClick={async () => {
                      actionRef.current?.clearSelected?.();
                    }}
                  >
                    取消选择
                  </a>
                </Space>
              </FooterToolbar>
            )}

            <CaseDetailDrawer
              open={detailOpen}
              title={detailTitle}
              id={caseId}
              envId={selectedEnvId as string}
              onClose={() => setDetailOpen(false)}
              handleTaskId={(taskId: string) => setTaskId(taskId)}
              handleLogOpen={(open: boolean) => setLogOpen(open)}
            />

            {logOpen && (
              <LogModal open={logOpen} task_id={taskId} onCancel={() => setLogOpen(false)} />
            )}
          </Content>
        </Layout>
      </Layout>
    </PageContainer>
  );
};
export default connect((props) => props)(TestCaseList);
