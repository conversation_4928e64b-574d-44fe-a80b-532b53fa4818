import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token }) => {
  return {
    collapseButton: {
      width: '24px !important',
      height: '24px !important',
      borderRadius: 40,
      backgroundColor: token.colorWhite,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      insetBlockStart: 13, // 下移
      insetInlineEnd: 12, // 左移一些
    },
    collapseIcon: {
      fontSize: '12px !important',
    },
    siderItem: { paddingRight: 12 },
  };
});

export default useStyles;
