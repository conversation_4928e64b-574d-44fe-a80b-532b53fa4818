import Editor from '@monaco-editor/react';
import { memo } from 'react';
type CaseEditProps = {
  casedata: string;
  onContentChange: (newValue: string, event) => void;
};

const CaseEdit: React.FC<CaseEditProps> = memo(
  (props) => {
    // 包裹memo，保证在 value真实变动时候，才重新渲染，而不是修改文本就重写渲染
    const { casedata, onContentChange } = props;

    // const monaco = useMonaco();

    // useEffect(() => {
    //   if (monaco) {
    //     console.log('here is the monaco instance:', monaco);
    //     monaco.languages.registerCompletionItemProvider('yaml', {
    //       provideCompletionItems: () => {
    //         return [
    //           {
    //             label: 'request',
    //           },
    //         ];
    //       },
    //       triggerCharacters: ['re'],
    //     });
    //   }
    // }, [monaco]);

    return (
      <Editor
        language="yaml"
        options={{ wordWrap: true, wordWrapColumn: 120 }}
        width="100%"
        height="100%"
        value={casedata}
        onChange={onContentChange}
      />
    );
  },
  (prevProps, nextProps) => prevProps.casedata === nextProps.casedata,
);
export default CaseEdit;
