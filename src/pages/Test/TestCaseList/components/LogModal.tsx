import { Editor } from '@monaco-editor/react';
import { Modal, Tabs } from 'antd';
import { useEffect, useRef, useState } from 'react';
import useWebSocket from 'react-use-websocket';
import TestReport from '../../TestReportDetail/TestReport';

const { TabPane } = Tabs;
type LogModalProps = {
  open: boolean;
  onCancel: () => void;
  task_id: string;
};
const LogModal: React.FC<LogModalProps> = (props) => {
  const editorRef = useRef(null);
  const [testLog, setTestLog] = useState([]);
  const [preventReconnect, setPreventReconnect] = useState<boolean>(false);
  const [activeTabKey, setActiveTabKey] = useState<string>('log');

  const { open, onCancel, task_id } = props;

  const currentUrl = window.location.href;

  // 解析URL以获取协议
  const protocol = new URL(currentUrl).protocol;
  const wsProtocol = protocol === 'https:' ? 'wss:' : 'ws:';

  const { lastMessage, readyState, getWebSocket } = useWebSocket(
    // 必须加上 host
    `${wsProtocol}//${window.location.host}/ws/log/testcase-${task_id}`,
    {
      onOpen: () => console.log('WebSocket Connected'),
      onClose: () => console.log('WebSocket Disconnected'),
      shouldReconnect: () => {
        // 如果 preventReconnect 为 true，则不自动重连
        if (preventReconnect) {
          return false;
        }
        // 返回true, 会自动重连
        return true;
      },
      reconnectAttempts: 5,
      reconnectInterval: 3000,
    },
  );

  const moveCursorToEnd = () => {
    // 确保编辑器实例存在
    if (!editorRef.current) return;
    // // 获取编辑器的模型（文档）
    // const model = editorRef.current.getModel();
    // // 获取文档的行数
    // const lineCount = model.getLineCount();

    // // 获取最后一行的长度
    // const lastLineLength = model.getLineLength(lineCount);
    // 创建一个位置，指向文档的末尾
    const endPosition = {
      lineNumber: Infinity,
      column: 0,
    };
    // 滚动到光标位置
    editorRef.current.revealPosition(endPosition);
    // 将光标移动到文档的末尾
    editorRef.current.setPosition(endPosition);
  };

  useEffect(() => {
    if (lastMessage !== null) {
      // 假设服务器发送的消息是字符串格式
      // 使用函数式更新
      setTestLog((prev) => [...prev, lastMessage.data]);
      moveCursorToEnd();
    }
  }, [lastMessage]);

  function handleEditorDidMount(editor) {
    // here is the editor instance
    // you can store it in `useRef` for further usage
    editorRef.current = editor;
  }

  const closeWebsocket = () => {
    setPreventReconnect(true);
    const ws = getWebSocket();
    if (ws && readyState === WebSocket.OPEN) {
      ws.close();
    }
  };

  useEffect(() => {
    if (!open) {
      closeWebsocket();
    }
  }, [open, getWebSocket]);

  return (
    <Modal
      title="测试日志"
      open={open}
      destroyOnClose={true}
      closable={true}
      maskClosable={false}
      onCancel={() => {
        onCancel();
      }}
      footer={null}
      width={1000}
    >
      <Tabs type="card" onChange={(key) => setActiveTabKey(key)}>
        <TabPane tab="日志" key="log">
          <Editor
            height={'500px'}
            language="json"
            onMount={handleEditorDidMount}
            options={{ wordWrap: true, wordWrapColumn: 80, readOnly: true }}
            value={testLog.join('\n')}
          />
        </TabPane>
        <TabPane tab="报告" key="report">
          {activeTabKey === 'report' && <TestReport report_id={task_id} />}
        </TabPane>
      </Tabs>
    </Modal>
  );
};
export default LogModal;
