import { TestcaseUpdateApi } from '@/pages/Test/service';
import { ModalForm, ProFormItem, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { connect, useRequest } from '@umijs/max';
import { Tag, message } from 'antd';
import React, { cloneElement, useCallback, useState } from 'react';
type UpdateFormProps = {
  trigger?: JSX.Element;
  onOk?: () => void;
  values: Partial<API.TestCase>;
  dispatch?: any;
  base?: any; // 父组件已dispatch，这里不用在 useEffect 时请求
};
export const optionsWithColor = [
  {
    label: <Tag color="red">P0</Tag>,
    value: 'P0',
  },
  {
    label: <Tag color="orange">P1</Tag>,
    value: 'P1',
  },
  {
    label: <Tag color="green">P2</Tag>,
    value: 'P2',
  },
  {
    label: <Tag color="blue">P3</Tag>,
    value: 'P3',
  },
];
const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { onOk, values, trigger, base } = props;
  const [open, setOpen] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const { run, loading } = useRequest(TestcaseUpdateApi, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Configuration is successful');
      onOk?.();
    },
    onError: () => {
      messageApi.error('Configuration failed, please try again!');
    },
  });
  const onCancel = useCallback(() => {
    setOpen(false);
  }, []);
  const onOpen = useCallback(() => {
    setOpen(true);
  }, []);
  const onFinish = useCallback(
    async (value?: any) => {
      await run(
        {
          id: value.id,
        },
        {
          ...value,
        },
      );
      onCancel();
    },
    [onCancel, run],
  );
  return (
    <>
      {contextHolder}
      <ModalForm
        title={'修改测试用例'}
        initialValues={values}
        trigger={
          trigger
            ? cloneElement(trigger, {
                onClick: onOpen,
              })
            : undefined
        }
        open={open}
        width="400px"
        modalProps={{
          okButtonProps: {
            loading,
          },
          onCancel: onCancel,
        }}
        onFinish={onFinish}
      >
        <ProFormItem name="id" hidden />
        <ProFormText
          label={'用例名称'}
          rules={[
            {
              required: true,
              message: '用例名称为必填项',
            },
          ]}
          width="md"
          name="name"
        />
        <ProFormText label={'描述'} width="md" name="desc" />
        <ProFormSelect
          label={'优先级'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="priority"
          valueEnum={{
            P0: {
              text: optionsWithColor[0].label,
            },
            P1: {
              text: optionsWithColor[1].label,
            },
            P2: {
              text: optionsWithColor[2].label,
            },
            P3: {
              text: optionsWithColor[3].label,
            },
          }}
        />
        <ProFormSelect
          label={'测试用例标签'}
          mode="multiple"
          width="md"
          name="tags"
          convertValue={(value) => {
            return value.map((item) => item.toString());
          }}
          request={async () => Object.values(base.tags)}
        />
      </ModalForm>
    </>
  );
};
export default connect((props) => props)(UpdateForm);
