import { TestcaseCreateApi } from '@/pages/Test/service';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { connect, useRequest } from '@umijs/max';
import { Button, message } from 'antd';
import { FC, useCallback, useState } from 'react';
import { optionsWithColor } from './UpdateForm';
interface CreateFormProps {
  projectState: string;
  directoryState: string;
  onOpen: () => boolean;
  reload?: ActionType['reload'];
  dispatch?: any;
  base?: any; // 父组件已dispatch，这里不用在 useEffect 时请求
}
const CreateForm: FC<CreateFormProps> = (props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const [open, setOpen] = useState<boolean>(false);
  const { onOpen, reload, projectState, directoryState, base } = props;
  const handleOpen = async () => {
    // 这里可以进行一些判断逻辑
    const canOpen = onOpen();
    if (canOpen) {
      setOpen(true);
    } else {
      setOpen(false);
    }
  };
  const onCancel = useCallback(() => {
    setOpen(false);
  }, []);
  const { run, loading } = useRequest(TestcaseCreateApi, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Added successfully');
      onCancel();
      reload?.();
    },
    onError: () => {
      messageApi.error('Adding failed, please try again!');
    },
  });
  return (
    <>
      {contextHolder}
      <ModalForm
        title={'新建测试用例'}
        trigger={
          <Button type="primary" icon={<PlusOutlined />} onClick={handleOpen}>
            新建
          </Button>
        }
        width="400px"
        open={open}
        // 关闭后不销毁，保留数据便于多次新增
        modalProps={{
          okButtonProps: {
            loading,
          },
          onCancel: onCancel,
        }}
        onFinish={async (value) => {
          if (directoryState === null) {
            messageApi.error('请选择一个目录再新增');
            return false;
          }
          await run({
            project: projectState,
            directory: directoryState,
            ...value,
          });
          return true;
        }}
      >
        <ProFormText
          label={'用例名称'}
          rules={[
            {
              required: true,
              message: '用例名称为必填项',
            },
          ]}
          width="md"
          name="name"
        />
        <ProFormText label={'描述'} width="md" name="desc" initialValue="" />
        <ProFormSelect
          label={'优先级'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="priority"
          initialValue="P3"
          valueEnum={{
            P0: {
              text: optionsWithColor[0].label,
            },
            P1: {
              text: optionsWithColor[1].label,
            },
            P2: {
              text: optionsWithColor[2].label,
            },
            P3: {
              text: optionsWithColor[3].label,
            },
          }}
        />
        <ProFormSelect
          label={'测试用例标签'}
          mode="multiple"
          width="md"
          name="tags"
          request={async () => Object.values(base.tags)}
        />
      </ModalForm>
    </>
  );
};
export default connect((props) => props)(CreateForm);
