import { Button, Drawer, message } from 'antd';
import yaml from 'js-yaml';
import { useEffect, useState } from 'react';
import { TestcaseDataDetailApi, TestcaseDataUpdateApi, TestcaseRunApi } from '../../service';
import CaseEdit from './CaseEdit';

type CaseDetailDrawerProps = {
  open: boolean;
  onClose: () => void;
  title: string;
  id: string;
  envId: string;
  handleTaskId: (taskId: string) => void;
  handleLogOpen: (open: boolean) => void;
};
const CaseDetailDrawer: React.FC<CaseDetailDrawerProps> = (props) => {
  const [testCaseData, setTestCaseData] = useState<string>('');
  const [yamlContent, setYamlContent] = useState<string>('');
  const [destroy, setDestory] = useState(false);
  const { open, onClose, title, id, envId, handleTaskId, handleLogOpen } = props;

  const fetchTestCaseDetail = async () => {
    console.log('Fetching test case detail');
    message.info('加载测试用例内容');
    try {
      const response = await TestcaseDataDetailApi({ id: id });
      const yamlData = yaml.dump(response.data, {
        // forceQuotes: true,
        flowLevel: 4,
      });
      setTestCaseData(yamlData);
      setYamlContent(yamlData);
    } catch (error) {
      console.error('Error fetching test case detail:', error);
      // 处理错误情况
    }
  };
  useEffect(() => {
    // 首次加载时候拉取
    if (id !== '') {
      fetchTestCaseDetail();
    }
  }, [id]);

  const handleContentChange = async (newValue: string) => {
    setYamlContent(newValue); // 更新状态以保存新的内容
    // 这里你可以做其他的事情，比如发送内容到API等
  };

  const handleSave = async () => {
    const response = await TestcaseDataUpdateApi({ id: id }, { data: yamlContent });
    console.debug(response.data);
    message.success('保存成功');
  };

  const RunTestCase = (testCaseId: string, is_debug: boolean = false) => {
    // 调用运行测试用例的API
    TestcaseRunApi({ id: [testCaseId], env: envId, is_debug: is_debug })
      // runTestCaseApi(testCaseId)
      .then((resp) => {
        // message.info('开始调试运行');
        // API调用成功后，打开弹窗
        handleTaskId(resp.data);
        handleLogOpen(true);
      })
      .catch((error) => {
        // 处理错误情况
        console.error('Error running test case:', error);
      });
  };

  return (
    <Drawer
      title={title}
      extra={
        <>
          <Button
            onClick={() => {
              RunTestCase(id);
            }}
          >
            运行
          </Button>
          <Button type="primary" style={{ margin: '8px' }} onClick={handleSave}>
            保存
          </Button>
          <Button
            onClick={() => {
              RunTestCase(id, true);
            }}
          >
            调试运行
          </Button>
        </>
      }
      open={open}
      destroyOnClose={destroy}
      closable={true}
      maskClosable={true}
      keyboard={true} // 是否支持键盘 esc 关闭
      onClose={(e) => {
        onClose();
        // 检查 e.target 的类名是否包含特定的遮罩层标识
        // 注意: 这里的 'ant-drawer-mask' 是基于当前antd版本的实现，可能会变化
        if (
          e.target.className.includes !== undefined &&
          e.target.className.includes('ant-drawer-mask')
        ) {
          console.log('mask close');
          setDestory(false);
        } else {
          setDestory(true);
          // setShouldFetchData(true);
        }
      }}
      footer={null}
      width={1000}
    >
      <CaseEdit casedata={testCaseData} onContentChange={handleContentChange} />
    </Drawer>
  );
};
export default CaseDetailDrawer;
