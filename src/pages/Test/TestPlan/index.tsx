import { EnvListApi } from '@/pages/Base/service';
import {
  ActionType,
  FooterToolbar,
  PageContainer,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { connect, useLocation, useRequest } from '@umijs/max';
import { Button, Popconfirm, Select, Space, Switch, message } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';
import LogModal from '../TestCaseList/components/LogModal';
import { TestPlanBatchDeleteApi, TestPlanListApi, TestPlanRunApi } from '../service';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
// 定义Select选项接口
interface Option {
  value: string;
  label: string;
}
interface TestPlanListProps {
  dispatch?: any;
  base?: any;
  common?: any;
}
const TestPlanList: React.FC<TestPlanListProps> = (props) => {
  const [envs, setEnvs] = useState<Option[]>([]);
  const [selectedEnvId, setSelectedEnvId] = useState<string>();
  const [selectedRowsState, setSelectedRows] = useState<API.TestCase[]>([]); // TODO: 改为selectedRowKeys
  const actionRef = useRef<ActionType>();
  const [logOpen, setLogOpen] = useState(false);
  const [taskId, setTaskId] = useState<string>('');

  const [messageApi, contextHolder] = message.useMessage();
  const { dispatch } = props;

  // 从 URL 参数中读取状态
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  // const projectIdFromUrl = searchParams.get('project_id');
  // const directoryIdFromUrl = searchParams.get('directory_id');
  const envIdFromUrl = searchParams.get('env_id');
  if (envIdFromUrl) {
    dispatch({
      type: 'common/updateState',
      payload: {
        env_id: envIdFromUrl,
      },
    });
  }

  const { run: fetchEnv } = useRequest(
    () => {
      return EnvListApi({
        pageSize: 1000,
      });
    },
    {
      manual: true,
      onSuccess: (result) => {
        const envOptions: Option[] = result.map((env) => ({
          value: env.id,
          label: `${env.key}(${env.region})`,
        }));
        setEnvs(envOptions);
        const envIdExists = envOptions.some((option) => option.value === envIdFromUrl);
        if (envIdFromUrl && envIdExists) {
          // 如果 url 参数有，并且存在于 envOptions 中，则选择该项目
          setSelectedEnvId(envIdFromUrl);
        } else {
          setSelectedEnvId(envOptions[envOptions.length - 1].value);
        }
      },
    },
  );

  // 挂载时，获取环境列表
  useEffect(() => {
    fetchEnv();
  }, []);

  useEffect(() => {
    if (selectedEnvId) {
      dispatch({
        type: 'common/syncUrlParams',
        payload: {
          env_id: selectedEnvId,
        },
      });
    }
  }, [selectedEnvId]);

  const { run: batchRun } = useRequest(TestPlanRunApi, {
    manual: true,
    onSuccess: (data) => {
      // 这里直接得到的是resp.data
      setTaskId(data);
      messageApi.success('运行成功');
      setLogOpen(true);
    },
    onError: () => {
      messageApi.error('运行失败，请重试');
    },
  });

  const columns: ProColumns<API.TestPlan>[] = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      hideInSearch: true,
      width: 50,
      // hideInTable: true,
    },
    {
      title: '计划名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'desc',
      key: 'desc',
      width: 100,
      ellipsis: true,
    },
    {
      title: '结果通知',
      dataIndex: 'notify_enable',
      key: 'notify_enable',
      width: 100,
      render: (_, record) => (
        <Switch
          checked={record.notify_enable}
          onChange={(checked) => {
            console.log(`switch to ${checked}`);
            // 这里可以添加代码来处理状态改变，比如更新数据源
          }}
        />
      ),
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
      key: 'created_by',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      valueType: 'dateTime',
      width: 160,
    },
    {
      title: '修改人',
      dataIndex: 'updated_by',
      key: 'updated_by',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '修改时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      valueType: 'dateTime',
      width: 160,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      render: (_, record) => [
        <UpdateForm
          key="update"
          trigger={<a>修改</a>}
          onOk={actionRef.current?.reload}
          values={record}
        />,
        <a
          key="run"
          onClick={() => {
            batchRun({
              id: [record.id],
              env: selectedEnvId as string,
            });
          }}
        >
          运行
        </a>,
      ],
    },
  ];

  const handleEnvChange = (selectedOption: string) => {
    console.debug(selectedOption);
    setSelectedEnvId(selectedOption);
  };

  const { run: delRun } = useRequest(TestPlanBatchDeleteApi, {
    manual: true,
    onSuccess: () => {
      setSelectedRows([]);
      actionRef.current?.reloadAndRest?.();
      messageApi.success('Deleted successfully and will refresh soon');
    },
    onError: () => {
      messageApi.error('Delete failed, please try again');
    },
  });
  const handleRemove = useCallback(
    async (selectedRows: API.TestCase[]) => {
      if (!selectedRows?.length) {
        messageApi.warning('请选择删除项');
        return;
      }
      await delRun({
        id: selectedRows.map((row) => row.id),
      });
    },
    [delRun],
  );

  return (
    <PageContainer title={false} breadcrumbRender={false}>
      {contextHolder}
      <ProTable<API.TestCase>
        toolBarRender={() => [
          <Select
            key="env"
            options={envs}
            value={selectedEnvId}
            style={{
              width: 200,
            }}
            onChange={handleEnvChange}
          />,
          <CreateForm key="create" onOpen={() => true} reload={actionRef.current?.reload} />,
        ]}
        rowKey={'id'}
        columns={columns}
        request={TestPlanListApi}
        actionRef={actionRef}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
          selectedRowKeys: selectedRowsState.map((row) => row.id),
          preserveSelectedRowKeys: false,
        }}
        tableAlertRender={false}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              已选择{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              项 &nbsp;&nbsp;
            </div>
          }
        >
          <Space size={16}>
            {/* <Button
              type="primary"
              onClick={async () => {
                handleBatchRun(selectedRowsState);
              }}
            >
              批量运行
            </Button> */}
            <Popconfirm
              title="确定要删除吗?"
              onConfirm={async () => {
                handleRemove(selectedRowsState);
              }}
            >
              <Button danger onClick={async () => {}}>
                批量删除
              </Button>
            </Popconfirm>

            <a
              onClick={async () => {
                actionRef.current?.clearSelected?.();
              }}
            >
              取消选择
            </a>
          </Space>
        </FooterToolbar>
      )}
      {logOpen && <LogModal open={logOpen} task_id={taskId} onCancel={() => setLogOpen(false)} />}
    </PageContainer>
  );
};
export default connect((props) => props)(TestPlanList);
