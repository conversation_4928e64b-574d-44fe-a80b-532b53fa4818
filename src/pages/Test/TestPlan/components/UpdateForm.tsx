import { TestPlanUpdateA<PERSON>, TestcaseListApi } from '@/pages/Test/service';
import { ModalForm, ProFormItem, ProFormSwitch, ProFormText } from '@ant-design/pro-components';
import { connect, useRequest } from '@umijs/max';
import { Table, TablePaginationConfig, message } from 'antd';
import React, { cloneElement, useCallback, useEffect, useState } from 'react';
type UpdateFormProps = {
  trigger?: JSX.Element;
  onOk?: () => void;
  values: Partial<API.TestPlan>;
  dispatch?: any;
};

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '用例名称',
    dataIndex: 'name',
  },
  {
    title: '用例等级',
    dataIndex: 'priority',
  },
];

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { onOk, values, trigger } = props;
  const [open, setOpen] = useState(false);
  const [data, setData] = useState<API.TestCase[]>([]);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
  });
  const [caseIds, setCaseIds] = useState<React.Key[]>(values.test_case_ids || []);

  const [messageApi, contextHolder] = message.useMessage();
  const fetchCases = async (page: number, pageSize: number) => {
    try {
      const response = await TestcaseListApi({
        current: page,
        pageSize: pageSize,
      });
      setData(response.data);
      setPagination({
        ...pagination,
        total: response.total,
      });
    } catch (error) {
      console.error('Fetching data failed:', error);
    }
  };

  useEffect(() => {
    fetchCases(pagination.current || 1, pagination.pageSize || 10);
  }, [pagination.current, pagination.pageSize]);
  console.log(caseIds);

  const { run, loading } = useRequest(TestPlanUpdateApi, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Configuration is successful');
      onOk?.();
    },
    onError: () => {
      messageApi.error('Configuration failed, please try again!');
    },
  });

  const handleTableChange = (current_pagination: TablePaginationConfig) => {
    setPagination({ ...pagination, ...current_pagination });
  };

  const onCancel = useCallback(() => {
    setOpen(false);
  }, []);
  const onOpen = useCallback(() => {
    setOpen(true);
  }, []);
  const onFinish = useCallback(
    async (value?: any) => {
      await run(
        {
          id: value.id,
        },
        {
          ...value,
        },
      );
      onCancel();
    },
    [onCancel, run],
  );
  return (
    <>
      {contextHolder}
      <ModalForm
        title={'修改测试计划'}
        initialValues={values}
        trigger={
          trigger
            ? cloneElement(trigger, {
                onClick: onOpen,
              })
            : undefined
        }
        open={open}
        width="600px"
        modalProps={{
          okButtonProps: {
            loading,
          },
          onCancel: onCancel,
        }}
        onFinish={async (values) => onFinish({ ...values, test_case_ids: caseIds })}
      >
        <ProFormItem name="id" hidden />
        <ProFormText
          label={'计划名称'}
          rules={[
            {
              required: true,
              message: '计划名称为必填项',
            },
          ]}
          width="md"
          name="name"
        />
        <ProFormText label={'描述'} width="md" name="desc" />
        <ProFormSwitch
          label={'结果通知'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="notify_enable"
        />
        <ProFormText name="test_case_ids" label="Testcase IDs" hidden initialValue={caseIds} />
        {/* {open && ( */}
        <Table
          columns={columns}
          rowKey="id"
          size="small"
          dataSource={data}
          pagination={pagination}
          onChange={handleTableChange}
          rowSelection={{
            preserveSelectedRowKeys: true,
            selectedRowKeys: caseIds,
            onChange: (selectedKeys) => {
              setCaseIds(selectedKeys);
            },
          }}
        />
        {/* )} */}
      </ModalForm>
    </>
  );
};
export default connect((props) => props)(UpdateForm);
