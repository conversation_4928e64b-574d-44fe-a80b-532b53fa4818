import { TestPlan<PERSON>reate<PERSON><PERSON>, TestcaseListApi } from '@/pages/Test/service';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ModalForm, ProFormSwitch, ProFormText } from '@ant-design/pro-components';
import { connect, useRequest } from '@umijs/max';
import { Button, Table, TablePaginationConfig, message } from 'antd';
import React, { FC, useCallback, useEffect, useState } from 'react';

interface CreateFormProps {
  onOpen: () => boolean;
  reload?: ActionType['reload'];
  dispatch?: any;
}

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '用例名称',
    dataIndex: 'name',
  },
  {
    title: '用例等级',
    dataIndex: 'priority',
  },
];

const CreateForm: FC<CreateFormProps> = (props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const [open, setOpen] = useState<boolean>(false);
  const [data, setData] = useState<API.TestCase[]>([]);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
  });
  const [caseIds, setCaseIds] = useState<React.Key[]>([]);

  const { onOpen, reload } = props;

  const fetchCases = async (page: number, pageSize: number) => {
    try {
      const response = await TestcaseListApi({
        current: page,
        pageSize: pageSize,
      });
      setData(response.data);
      setPagination({
        ...pagination,
        total: response.total,
      });
    } catch (error) {
      console.error('Fetching data failed:', error);
    }
  };

  useEffect(() => {
    fetchCases(pagination.current || 1, pagination.pageSize || 10);
  }, [pagination.current, pagination.pageSize]);

  const handleTableChange = (current_pagination: TablePaginationConfig) => {
    setPagination({ ...pagination, ...current_pagination });
  };

  const handleOpen = async () => {
    // 这里可以进行一些判断逻辑
    const canOpen = onOpen();
    if (canOpen) {
      setOpen(true);
    } else {
      setOpen(false);
    }
  };
  const onCancel = useCallback(() => {
    setOpen(false);
  }, []);
  const { run, loading } = useRequest(TestPlanCreateApi, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Added successfully');
      onCancel();
      reload?.();
    },
    onError: () => {
      messageApi.error('Adding failed, please try again!');
    },
  });

  return (
    <>
      {contextHolder}
      <ModalForm
        title={'新建测试计划'}
        trigger={
          <Button type="primary" icon={<PlusOutlined />} onClick={handleOpen}>
            新建
          </Button>
        }
        width="600px"
        open={open}
        // 关闭后不销毁，保留数据便于多次新增
        modalProps={{
          okButtonProps: {
            loading,
          },
          onCancel: onCancel,
        }}
        onFinish={async (value: TestPlan) => {
          console.log({ ...value, test_case_ids: caseIds });
          await run({ ...value, test_case_ids: caseIds });
          return true;
        }}
      >
        <ProFormText
          label={'计划名称'}
          rules={[
            {
              required: true,
              message: '计划名称为必填项',
            },
          ]}
          width="md"
          name="name"
        />
        <ProFormText label={'描述'} width="md" name="desc" initialValue="" />
        <ProFormSwitch
          label={'结果通知'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="notify_enable"
          initialValue={false}
        />
        {open && (
          <Table
            columns={columns}
            rowKey="id"
            size="small"
            dataSource={data}
            pagination={pagination}
            onChange={handleTableChange}
            rowSelection={{
              preserveSelectedRowKeys: true,
              selectedRowKeys: caseIds,
              onChange: (selectedKeys) => {
                setCaseIds(selectedKeys);
              },
            }}
          />
        )}
      </ModalForm>
    </>
  );
};
export default connect((props) => props)(CreateForm);
