import { TestResult } from '@/constants';
import { Page<PERSON>ontainer, ProColumns, ProTable } from '@ant-design/pro-components';
import '@umijs/max';
import { Modal, Typography } from 'antd';
import { useState } from 'react';
import { TestreportListApi } from '../service';
const { Text } = Typography;
const TestReportList: React.FC = () => {
  const [open, setOpen] = useState<boolean>(false);
  const [modalContentUrl, setModalContentUrl] = useState('');
  const showModal = (record) => {
    // 假设每个记录都有一个与之关联的URL
    const host = window.location.host;
    const protocol = window.location.protocol;
    const url = `${protocol}//${host}/preview/testreport/${record.id}`;
    setModalContentUrl(url);
    setOpen(true);
    console.log(url);
  };
  type ValueEnumType = {
    [key: number]: {
      text: string;
      status: 'Success' | 'Error' | 'Default' | 'Processing' | 'Warning';
    };
  } & {
    [key: string]: {
      text: string;
      status: 'Success' | 'Error' | 'Default' | 'Processing' | 'Warning';
    };
  };
  const testResultEnum: ValueEnumType = {
    [TestResult.PASS]: {
      text: 'Pass',
      status: 'Success',
    },
    [TestResult.FAIL]: {
      text: 'Fail',
      status: 'Error',
    },
    [TestResult.INTERRUPTED]: {
      text: 'Interrupted',
      status: 'Default',
    },
    [TestResult.INTERNAL_ERROR]: {
      text: 'Internal Error',
      status: 'Error',
    },
    [TestResult.USAGE_ERROR]: {
      text: 'Usage Error',
      status: 'Warning',
    },
    [TestResult.NO_TESTS_COLLECTED]: {
      text: 'No Test',
      status: 'Default',
    },
    null: {
      text: 'Running',
      status: 'Processing',
    },
  };
  const columns: ProColumns<API.TestReport>[] = [
    {
      dataIndex: 'id',
      key: 'id',
      title: 'ID',
      hideInTable: true,
    },
    {
      dataIndex: 'name',
      key: 'name',
      title: '报告名称',
      copyable: true,
    },
    {
      dataIndex: 'env',
      key: 'env',
      title: '环境信息',
    },
    {
      // title: `${intl.formatMessage({
      //   id: 'pages.testcase.list',
      // })}`,
      title: '结果',
      dataIndex: 'result',
      valueEnum: testResultEnum,
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
      key: 'created_by',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      valueType: 'dateTime',
    },
    {
      title: '修改人',
      dataIndex: 'updated_by',
      key: 'updated_by',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '修改时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      valueType: 'dateTime',
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <Text
          key={'log'}
          copyable={{
            text: `${window.location.protocol}//${window.location.host}/preview/testreport/${record.id}`, // 自定义复制的内容
          }}
        >
          <a
            onClick={() => {
              showModal(record);
            }}
          >
            报告
          </a>
        </Text>,
      ],
    },
  ];
  return (
    <PageContainer title={false} breadcrumbRender={false}>
      <ProTable<API.TestReport>
        rowKey={'id'}
        columns={columns}
        request={TestreportListApi}
        // actionRef={actionRef}
      />
      {open && (
        <Modal
          open={open}
          onCancel={() => setOpen(false)}
          footer={null}
          width="80%"
          centered={true}
        >
          <iframe
            src={modalContentUrl}
            style={{
              border: 'none',
              width: '100%',
              height: '600px',
            }}
            title="Modal Content"
          />
        </Modal>
      )}
    </PageContainer>
  );
};
export default TestReportList;
