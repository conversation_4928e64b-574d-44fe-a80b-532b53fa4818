export interface ReportData {
  success: boolean;
  stat: {
    testcases: {
      total: number;
      success: number;
      fail: number;
    };
    teststeps: {
      total: number;
      failures: number;
      successes: number;
    };
  };
  time: {
    start_at: number;
    duration: number;
  };
  platform: {
    httprunner_version: string;
    python_version: string;
    platform: string;
  };
  details: TestCase[];
}
export interface TestCase {
  name: string;
  success: boolean;
  case_id: string;
  time: {
    start_at: number;
    start_at_iso_format: string;
    duration: number;
  };
  in_out: {
    config_vars: object;
    export_vars: object;
  };
  log: string;
  records: TestStep[];
}
export interface TestStep {
  name: string;
  step_type: string;
  success: boolean;
  data: SessionData;
  start_at: number;
  elapsed: number;
  content_size: number;
  export_vars: object;
  attachment: string;
}

export interface SessionData {
  success: boolean;
  req_resps: Array;
  stat: object;
  address: object;
  validators: object;
}
