import { Table, Tag } from 'antd';
import { ColumnsType } from 'antd/es/table';
import moment from 'moment';
import type { FC } from 'react';
import type { TestCase } from '../data';
import TestStepResultTable from './TestStepResult';
import './style.less';
type TestCaseTableProps = {
  testCases: TestCase[];
};
// 测试用例表格
const TestCaseResultTable: FC<TestCaseTableProps> = ({ testCases }) => {
  const columns: ColumnsType = [
    { title: '测试用例名称', dataIndex: 'name', key: 'name' },
    {
      title: '结果',
      dataIndex: 'success',
      key: 'success',
      align: 'right',
      width: '10vw',
      render: (success: boolean) => (
        <Tag color={success ? 'green' : 'red'}>{success ? '成功' : '失败'}</Tag>
      ),
    },
    {
      title: '开始时间',
      dataIndex: ['time', 'start_at'],
      key: 'start_at',
      align: 'right',
      width: '20vw',
      render: (timestamp: number) => moment.unix(timestamp).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '持续时间',
      dataIndex: ['time', 'duration'],
      key: 'duration',
      align: 'right',
      width: '10vw',
      render: (timestamp: number) => `${timestamp.toFixed(2)}s`,
    },
  ];
  return (
    <Table
      style={{ width: '100vw' }}
      columns={columns}
      showHeader={false}
      dataSource={testCases}
      rowKey="case_id"
      pagination={false}
      className="nested-table"
      expandable={{
        expandRowByClick: true,
        expandedRowRender: (record) => <TestStepResultTable steps={record.records} />,
      }}
    />
  );
};

export default TestCaseResultTable;
