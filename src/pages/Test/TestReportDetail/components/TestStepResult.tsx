import { Table, Tag } from 'antd';
import { ColumnsType } from 'antd/es/table';
import moment from 'moment';
import type { FC } from 'react';
import type { TestStep } from '../data';

type TestStepResultTableProps = {
  steps: TestStep[];
};

// 测试步骤的详细日志组件
const StepDetail = ({ log }) => (
  <div style={{ width: '80vw' }}>
    <pre>{log}</pre>
  </div>
);
// 测试步骤表格
const TestStepResultTable: FC<TestStepResultTableProps> = ({ steps }) => {
  const columns: ColumnsType = [
    { title: '测试步骤', dataIndex: 'name', key: 'name' },
    {
      title: '结果',
      dataIndex: 'success',
      key: 'success',
      align: 'right',
      width: '10vw',
      render: (success: boolean) => (
        <Tag color={success ? 'green' : 'red'}>{success ? '成功' : '失败'}</Tag>
      ),
    },
    {
      title: '开始时间',
      dataIndex: 'start_at',
      key: 'start_at',
      align: 'right',
      width: '20vw',
      render: (timestamp: number) => {
        // console.log(timestamp);
        return moment.unix(timestamp).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '持续时间',
      dataIndex: 'elapsed',
      key: 'elapsed',
      align: 'right',
      width: '10vw',
      render: (timestamp: number) => `${timestamp.toFixed(2)}s`,
    },
  ];
  return (
    <Table
      columns={columns}
      showHeader={false}
      dataSource={steps}
      pagination={false}
      rowKey={(record) => `${record.name}-${record.start_at}`}
      expandable={{
        expandRowByClick: true,
        indentSize: 0,
        expandedRowRender: (record) => <StepDetail log={JSON.stringify(record.data, null, 2)} />,
      }}
    />
  );
};

export default TestStepResultTable;
