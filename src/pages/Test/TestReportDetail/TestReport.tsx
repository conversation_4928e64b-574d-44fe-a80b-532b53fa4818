import type { PieConfig } from '@ant-design/plots';
import { Pie } from '@ant-design/plots';
import { useRequest } from '@umijs/max';
import { Col, Descriptions, Row } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { TestreportDetailApi } from '../service';
import TestCaseResultTable from './components/TestCaseResult';
import type { ReportData } from './data';

type TestReportProps = {
  report_id?: string;
};

const TestReport: React.FC<TestReportProps> = (props) => {
  const { report_id } = props;

  const [reportData, setReportData] = useState<ReportData>();
  const [envName, setEnvName] = useState<string>('');

  const [loading, setLoading] = useState<boolean>(true);

  const { run: fetchReportDetail } = useRequest(
    (id: string) => {
      return TestreportDetailApi({
        id: id,
      });
    },
    {
      manual: true,
      onSuccess: (data) => {
        // console.debug(data?.summary);
        try {
          const summary = JSON.parse(data?.summary);
          setEnvName(data?.env);
          setReportData(summary);
          setLoading(false);
          // console.debug(JSON.stringify(summary?.details));
        } catch (e) {}
        // setReportList(result);
      },
    },
  );

  useEffect(() => {
    if (report_id) {
      fetchReportDetail(report_id);
    }
  }, [report_id]);

  const secondsToHms = (d: number) => {
    if (d < 60) {
      return '';
    }
    const h = Math.floor(d / 3600);
    const m = Math.floor((d % 3600) / 60);
    const s = Math.floor(d % 60);

    const hDisplay = h > 0 ? (h < 10 ? '0' + h : h) + ':' : '';
    const mDisplay = (m < 10 ? '0' + m : m) + ':';
    const sDisplay = s < 10 ? '0' + s : s;
    return hDisplay + mDisplay + sDisplay;
  };

  const casePieConfig: PieConfig = {
    width: 300,
    height: 150,
    data: [
      { type: '成功用例', value: reportData?.stat?.testcases?.success || 0 },
      { type: '失败用例', value: reportData?.stat?.testcases?.fail || 0 },
    ],
    angleField: 'value',
    colorField: 'type',
    // color: ['green', 'red'], // 无效
    label: {
      position: 'outside',
      text: 'value',
      // text: (d) => `${d.type}\n ${d.value}`,
    },
    legend: {
      color: {
        title: false,
        position: 'left',
        rowPadding: 5,
      },
    },
  };

  const stepPieConfig: PieConfig = {
    width: 300,
    height: 150,
    data: [
      { type: '成功步骤', value: reportData?.stat?.teststeps?.successes || 0 },
      { type: '失败步骤', value: reportData?.stat?.teststeps?.failures || 0 },
    ],
    angleField: 'value',
    colorField: 'type',
    // color: ['green', 'red'],
    label: {
      position: 'outside',
      text: 'value',
      // text: (d) => `${d.type}\n ${d.value}`,
    },
    legend: {
      color: {
        title: false,
        position: 'left',
        rowPadding: 5,
      },
    },
  };

  return (
    <>
      <Descriptions
        size="middle"
        bordered
        column={2}
        items={[
          {
            label: '环境',
            children: envName,
          },
          {
            label: '开始时间',
            children: moment.unix(reportData?.time?.start_at || 0).format('YYYY-MM-DD HH:mm:ss'),
          },
          {
            label: '持续时间',
            children: `${reportData?.time?.duration.toFixed(2)} 秒 ${secondsToHms(
              reportData?.time?.duration,
            )}`,
          },
          {
            label: 'Python版本',
            children: reportData?.platform?.python_version,
          },
          // {
          //   label: 'HTTPRunner版本',
          //   children: reportData?.platform?.httprunner_version,
          // },
          {
            label: '平台',
            children: reportData?.platform?.platform,
          },
        ]}
      />

      {loading ? (
        <div>Loading...</div>
      ) : (
        <Row gutter={12}>
          <Col span={6}>
            <Pie {...casePieConfig} />
          </Col>
          <Col span={6}>
            <Pie {...stepPieConfig} />
          </Col>
        </Row>
      )}

      <TestCaseResultTable testCases={reportData?.details || []} />
    </>
  );
};
export default TestReport;
