declare namespace API {
  type NullEnum = any;

  type IdParam = {
    /** rest api path param */
    id: string;
  };
  type IdsParam = {
    /** rest api path param */
    id: string[];
  };
  type ProjectIdParam = {
    project_id: string;
  };

  type Pagination = {
    /** A page number within the paginated result set. */
    current?: number;
    /** Which field to use when ordering the results. */
    ordering?: string;
    /** Number of results to return per page. */
    pageSize?: number;
  };

  type TestCaseList = {
    data: TestCase[];
    total?: number;
    success?: boolean;
  };

  type TestCase = API.BaseFields & {
    id: string;
    name: string;
    desc: string;
    priority: string;
    tags: int[];
    project: number;
    directory?: number;
  };

  type TestCaseData = {
    data: string;
  };

  type TestcaseDir = {
    title: string;
    key?: string;
    parent_id?: string;
  };

  type TestCaseDirectory = {
    key: string | null;
    title: string;
    children?: TestCaseDirectory[];
  };

  type RunTestCases = {
    id: string[];
    env: string;
    is_debug?: boolean;
  };

  type RunTestCasesByDir = {
    dir: string;
    env: string;
  };

  type RunTestCasesByProject = {
    project: string;
    env: string;
  };

  type TestCaseUpload = {
    file: RcFile[];
    project_id: string;
    directory: string;
    env: string;
  };

  type ResultEnum = 0 | 1 | 2 | 3 | 4 | 5;

  type TestReport = API.BaseFields & {
    id: string;
    name?: string;
    env: string;
    result?: ResultEnum | NullEnum;
  };

  type TestReportList = {
    data?: TestReport[];
    total?: number;
    success?: boolean;
  };

  type TestReportDetail = {
    id: string;
    env: string;
    summary?: string;
  };

  type TestPlan = API.BaseFields & {
    id: string;
    name?: string;
    desc?: string;
    notify_enable?: boolean;
    test_case_ids?: string[];
  };

  type TestPlanList = {
    data?: TestPlan[];
    total?: number;
    success?: boolean;
  };
}
