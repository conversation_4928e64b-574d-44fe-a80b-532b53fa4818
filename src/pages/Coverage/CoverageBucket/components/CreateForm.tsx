import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ModalForm, ProFormText } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message } from 'antd';
import { FC } from 'react';
import { CoverageBucketCreateApi } from '../../service';
interface CreateFormProps {
  reload?: ActionType['reload'];
}
const CreateForm: FC<CreateFormProps> = (props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const { reload } = props;
  const { run, loading } = useRequest(CoverageBucketCreateApi, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Added successfully');
      reload?.();
    },
    onError: () => {
      messageApi.error('Adding failed, please try again!');
    },
  });
  return (
    <>
      {contextHolder}
      <ModalForm
        title={'新建覆盖率存储桶配置'}
        trigger={
          <Button type="primary" icon={<PlusOutlined />}>
            新建
          </Button>
        }
        width="400px"
        modalProps={{
          okButtonProps: {
            loading,
          },
        }}
        onFinish={async (value) => {
          await run({
            ...(value as API.CoverageBucket),
          });
          return true;
        }}
      >
        <ProFormText
          label={'环境标识'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="env"
        />
        <ProFormText
          label={'类型'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="type"
        />
        <ProFormText
          label={'区域'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="region"
        />
        <ProFormText
          label={'覆盖率存储桶名'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="bucket_name"
        />
      </ModalForm>
    </>
  );
};
export default CreateForm;
