import { ModalForm, ProFormText } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message } from 'antd';
import React, { cloneElement, useCallback, useState } from 'react';
import { CoverageBucketUpdateApi } from '../../service';
export type UpdateFormProps = {
  trigger?: JSX.Element;
  onOk?: () => void;
  values: Partial<API.CoverageBucket>;
};
const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { onOk, values, trigger } = props;
  const [open, setOpen] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const { run, loading } = useRequest(CoverageBucketUpdateApi, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Configuration is successful');
      onOk?.();
    },
    onError: () => {
      messageApi.error('Configuration failed, please try again!');
    },
  });

  // const onCancel = useCallback(() => {
  //   setOpen(false);
  // }, []);

  const onOpen = useCallback(() => {
    setOpen(true);
  }, []);

  // const onFinish = useCallback(
  //   async (values?: any) => {
  //     await run({ data: values });

  //     onCancel();
  //   },
  //   [onCancel, run],
  // );

  return (
    <>
      {contextHolder}
      <ModalForm
        title={'修改AccessKey'}
        initialValues={values}
        trigger={
          trigger
            ? cloneElement(trigger, {
                onClick: onOpen,
                open: open,
              })
            : undefined
        }
        width="400px"
        modalProps={{
          okButtonProps: {
            loading,
          },
        }}
        onFinish={async (value) => {
          await run(
            {
              id: values.id,
            },
            {
              ...(value as Partial<API.CoverageBucket>),
            },
          );
          return true;
        }}
      >
        <ProFormText
          label={'环境标识'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="env"
        />
        <ProFormText
          label={'类型'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="type"
        />
        <ProFormText
          label={'区域'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="region"
        />
        <ProFormText
          label={'覆盖率存储桶名'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="bucket_name"
        />
      </ModalForm>
    </>
  );
};
export default UpdateForm;
