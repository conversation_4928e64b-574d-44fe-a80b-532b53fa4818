import { request } from '@umijs/max';

/** 获取覆盖率配置列表 GET /api/coverage_bucket */
export async function CoverageBucketListApi(
  params: API.Pagination,
  options?: { [key: string]: any },
) {
  return request<API.CoverageBucket>('/api/coverage_bucket', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 新增覆盖率配置 POST /api/coverage_bucket */
export async function CoverageBucketCreateApi(
  body: API.CoverageBucket,
  options?: { [key: string]: any },
) {
  return request<API.CoverageBucket>('/api/coverage_bucket', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除覆盖率配置 DELETE /api/coverage_bucket */
export async function CoverageBucketBatchDeleteApi(
  body: API.IdsParam,
  options?: { [key: string]: any },
) {
  return request<any>('/api/coverage_bucket', {
    method: 'DELETE',
    data: body,
    ...(options || {}),
  });
}

/** 获取单个覆盖率配置 GET /api/coverage_bucket/${param0} */
export async function CoverageBucketDetailApi(
  params: API.IdParam,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.CoverageBucket>(`/api/coverage_bucket/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改单个覆盖率配置 PUT /api/coverage_bucket/${param0} */
export async function CoverageBucketUpdateApi(
  params: API.IdParam,
  body: API.CoverageBucket,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.CoverageBucket>(`/api/coverage_bucket/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除单个覆盖率配置 DELETE /api/coverage_bucket/${param0} */
export async function CoverageBucketDeleteApi(
  params: API.IdParam,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/api/coverage_bucket/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取覆盖率元配置列表 GET /api/covrage_meta */
export async function CoverageMetaListApi(
  params: API.Pagination,
  options?: { [key: string]: any },
) {
  return request<API.CoverageMeta>('/api/covrage_meta', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 新增覆盖率元配置 POST /api/covrage_meta */
export async function CoverageMetaCreateApi(
  body: API.CoverageMeta,
  options?: { [key: string]: any },
) {
  return request<API.CoverageMeta>('/api/covrage_meta', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除覆盖率元配置 DELETE /api/covrage_meta */
export async function CoverageMetaBatchDeleteApi(
  body: API.IdsParam,
  options?: { [key: string]: any },
) {
  return request<any>('/api/covrage_meta', {
    method: 'DELETE',
    data: body,
    ...(options || {}),
  });
}

/** 修改单个覆盖率元配置 PUT /api/covrage_meta/${param0} */
export async function CoverageMetaUpdateApi(
  params: API.IdParam,
  body: API.CoverageMeta,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.CoverageMeta>(`/api/covrage_meta/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 获取覆盖率报告列表 GET /api/coverage_report */
export async function CoverageReportListApi(
  params: API.Pagination,
  options?: { [key: string]: any },
) {
  return request<API.CoverageReport>('/api/coverage_report', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 新增覆盖率报告 POST /api/coverage_report */
export async function CoverageReportCreateApi(
  body: API.CoverageReport,
  options?: { [key: string]: any },
) {
  return request<API.CoverageReport>('/api/coverage_report', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除覆盖率报告 DELETE /api/coverage_report */
export async function CoverageReportBatchDeleteApi(
  body: API.IdsParam,
  options?: { [key: string]: any },
) {
  return request<any>('/api/coverage_report', {
    method: 'DELETE',
    data: body,
    ...(options || {}),
  });
}

/** 修改单个覆盖率报告 PUT /api/coverage_report/${param0} */
export async function CoverageReportUpdateApi(
  params: API.IdParam,
  body: API.CoverageReport,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.CoverageReport>(`/api/coverage_report/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}
