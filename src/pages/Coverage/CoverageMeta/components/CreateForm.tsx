import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ModalForm, ProFormText } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message } from 'antd';
import { FC } from 'react';
import { CoverageMetaCreateApi } from '../../service';
interface CreateFormProps {
  reload?: ActionType['reload'];
}
const CreateForm: FC<CreateFormProps> = (props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const { reload } = props;
  const { run, loading } = useRequest(CoverageMetaCreateApi, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Added successfully');
      reload?.();
    },
    onError: () => {
      messageApi.error('Adding failed, please try again!');
    },
  });
  return (
    <>
      {contextHolder}
      <ModalForm
        title={'新建覆盖率基础配置'}
        trigger={
          <Button type="primary" icon={<PlusOutlined />}>
            新建
          </Button>
        }
        width="400px"
        modalProps={{
          okButtonProps: {
            loading,
          },
        }}
        onFinish={async (value) => {
          await run({
            ...(value as API.CoverageMeta),
          });
          return true;
        }}
      >
        <ProFormText
          label={'应用名称'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="app_name"
        />
        <ProFormText
          label={'git仓库地址'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="git_repo"
        />
        <ProFormText
          label={'gitTag'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="git_tag"
        />
        <ProFormText
          // label={intl.formatMessage({ id: 'pages.coverage.CoverageMeta.gitTagLabel' })}
          width="md"
          name="covmeta"
        />
      </ModalForm>
    </>
  );
};
export default CreateForm;
