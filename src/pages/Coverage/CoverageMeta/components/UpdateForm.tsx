import { ModalForm, ProFormText } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message } from 'antd';
import React, { cloneElement, useCallback, useState } from 'react';
import { CoverageMetaUpdateApi } from '../../service';
export type UpdateFormProps = {
  trigger?: JSX.Element;
  onOk?: () => void;
  values: Partial<API.CoverageMeta>;
};
const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { onOk, values, trigger } = props;
  const [open, setOpen] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const { run, loading } = useRequest(CoverageMetaUpdateApi, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Configuration is successful');
      onOk?.();
    },
    onError: () => {
      messageApi.error('Configuration failed, please try again!');
    },
  });

  // const onCancel = useCallback(() => {
  //   setOpen(false);
  // }, []);

  const onOpen = useCallback(() => {
    setOpen(true);
  }, []);

  // const onFinish = useCallback(
  //   async (values?: any) => {
  //     await run({ data: values });

  //     onCancel();
  //   },
  //   [onCancel, run],
  // );

  return (
    <>
      {contextHolder}
      <ModalForm
        title={'修改覆盖率基础配置'}
        initialValues={values}
        trigger={
          trigger
            ? cloneElement(trigger, {
                onClick: onOpen,
                open: open,
              })
            : undefined
        }
        width="400px"
        modalProps={{
          okButtonProps: {
            loading,
          },
        }}
        onFinish={async (value) => {
          await run(
            {
              id: values.id,
            },
            {
              ...(value as Partial<API.CoverageMeta>),
            },
          );
          return true;
        }}
      >
        <ProFormText
          label={'应用名称'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="app_name"
        />
        <ProFormText
          label={'git仓库地址'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="git_repo"
        />
        <ProFormText
          label={'gitTag'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="git_tag"
        />
        <ProFormText
          // label={intl.formatMessage({ id: 'pages.coverage.CoverageMeta.gitTagLabel' })}
          width="md"
          name="covmeta"
        />
      </ModalForm>
    </>
  );
};
export default UpdateForm;
