declare namespace API {
  type KeyParams = {
    key: string;
  };

  type BaseFields = {
    created_by: string;
    updated_by: string;
    created_at: string;
    updated_at: string;
  };

  type EnvironmentList = {
    success: boolean;
    data: Environment[];
  };

  type Environment = API.BaseFields & {
    id: string;
    key: string;
    region: string;
  };

  type EnvironmentVariableList = {
    success: boolean;
    data: EnvironmentVariable[];
  };

  type EnvironmentVariable = {
    key: string;
    desc?: string;
    values: Record<string, any>;
  };

  type ScheduledTaskList = {
    success: boolean;
    data: ScheduledTask[];
  };

  type ScheduledTask = {
    id: number;
    task_type: number;
    task_args: Record<string, any>;
    crontab: number;
    enabled?: boolean;
    expression: string;
  };

  type TagList = {
    success: boolean;
    total?: number;
    data: Tag[];
  };

  type Tag = API.BaseFields & {
    id: string;
    name: string;
  };

  type AwsAccessKey = API.BaseFields & {
    id: string;
    env: string;
    desc: string;
    account_id: string;
    access_key_id?: string;
    secret_access_key?: string;
  };

  type AwsAccessKeyList = {
    success: boolean;
    total?: number;
    data: AwsAccessKey[];
  };

  type CoverageBucket = API.BaseFields & {
    id: string;
    type: string;
    region: string;
    bucket_name: string;
  };

  type CoverageBucketList = {
    success: boolean;
    total?: number;
    data: CoverageBucket[];
  };

  type CoverageMeta = API.BaseFields & {
    id: string;
    app_name: string;
    git_repo: string;
    git_tag: string;
    covmeta?: string;
  };

  type CoverageMetaList = {
    success: boolean;
    total?: number;
    data: CoverageMeta[];
  };

  type CoverageReport = API.BaseFields & {
    id: string;
    app_name: string;
    env: string;
    region: string;
    start_time: string;
    end_time: string;
    covcount?: number;
    cov?: string;
    covhtml?: string;
  };

  type CoverageReportList = {
    success: boolean;
    total?: number;
    data: CoverageReport[];
  };
}
