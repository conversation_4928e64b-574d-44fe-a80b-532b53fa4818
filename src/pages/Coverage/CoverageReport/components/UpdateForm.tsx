import { ModalForm, ProFormDateTimeRangePicker, ProFormText } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message } from 'antd';
import dayjs from 'dayjs';
import React, { cloneElement, useCallback, useState } from 'react';
import { CoverageReportUpdateApi } from '../../service';
export type UpdateFormProps = {
  trigger?: JSX.Element;
  onOk?: () => void;
  values: Partial<API.CoverageReport>;
};
const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { onOk, values, trigger } = props;
  const [open, setOpen] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const { run, loading } = useRequest(CoverageReportUpdateApi, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Configuration is successful');
      onOk?.();
    },
    onError: () => {
      messageApi.error('Configuration failed, please try again!');
    },
  });

  // const onCancel = useCallback(() => {
  //   setOpen(false);
  // }, []);

  const onOpen = useCallback(() => {
    setOpen(true);
  }, []);

  // const onFinish = useCallback(
  //   async (values?: any) => {
  //     await run({ data: values });

  //     onCancel();
  //   },
  //   [onCancel, run],
  // );

  return (
    <>
      {contextHolder}
      <ModalForm
        title={'修改覆盖率报告'}
        initialValues={{
          ...values,
          timeRange: [values.start_time, values.end_time],
        }}
        trigger={
          trigger
            ? cloneElement(trigger, {
                onClick: onOpen,
                open: open,
              })
            : undefined
        }
        width="400px"
        modalProps={{
          okButtonProps: {
            loading,
          },
        }}
        onFinish={async (value) => {
          const rangeValue = value['timeRange'];
          const startTime = dayjs(rangeValue[0]).format('YYYY-MM-DD HH:mm:ssZZ');
          const endTime = dayjs(rangeValue[1]).format('YYYY-MM-DD HH:mm:ssZZ');
          const submitValue = {
            ...value,
            start_time: startTime,
            end_time: endTime,
          };
          delete submitValue.timeRange;
          await run(
            {
              id: values.id,
            },
            {
              ...(submitValue as API.CoverageReport),
            },
          );
          return true;
        }}
      >
        <ProFormText
          label={'应用名称'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="app_name"
        />
        <ProFormText
          label={'环境标识'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="env"
        />
        <ProFormText
          label={'区域'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="region"
        />
        <ProFormDateTimeRangePicker
          label={'时间范围'}
          rules={[
            {
              required: true,
            },
          ]}
          width="lg"
          name="timeRange"
        />
      </ModalForm>
    </>
  );
};
export default UpdateForm;
