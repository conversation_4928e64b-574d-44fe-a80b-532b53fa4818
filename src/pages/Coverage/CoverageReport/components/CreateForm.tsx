import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  ModalForm,
  ProFormDateTimeRangePicker,
  ProFormText,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message } from 'antd';
import dayjs from 'dayjs';
import { FC } from 'react';
import { CoverageReportCreateApi } from '../../service';
interface CreateFormProps {
  reload?: ActionType['reload'];
}
const CreateForm: FC<CreateFormProps> = (props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const { reload } = props;
  const { run, loading } = useRequest(CoverageReportCreateApi, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Added successfully');
      reload?.();
    },
    onError: () => {
      messageApi.error('Adding failed, please try again!');
    },
  });
  return (
    <>
      {contextHolder}
      <ModalForm
        title={'新建覆盖率报告'}
        trigger={
          <Button type="primary" icon={<PlusOutlined />}>
            新建
          </Button>
        }
        width="430px"
        modalProps={{
          okButtonProps: {
            loading,
          },
        }}
        onFinish={async (value) => {
          const rangeValue = value['timeRange'];
          const startTime = dayjs(rangeValue[0]).format('YYYY-MM-DD HH:mm:ssZZ');
          const endTime = dayjs(rangeValue[1]).format('YYYY-MM-DD HH:mm:ssZZ');
          const submitValue = {
            ...value,
            start_time: startTime,
            end_time: endTime,
          };
          delete submitValue.timeRange;
          await run({
            ...(submitValue as API.CoverageReport),
          });
          return true;
        }}
      >
        <ProFormText
          label={'应用名称'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="app_name"
        />
        <ProFormText
          label={'环境标识'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="env"
        />
        <ProFormText
          label={'区域'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="region"
        />
        <ProFormDateTimeRangePicker
          label={'时间范围'}
          rules={[
            {
              required: true,
            },
          ]}
          width="lg"
          name="timeRange"
        />
        {/* <ProFormDateTimePicker
          label={intl.formatMessage({ id: 'pages.coverage.CoverageReport.startTimeLabel' })}
          rules={[{ required: true }]}
          width="md"
          name="start_time"
         />
         <ProFormDateTimePicker
          label={intl.formatMessage({ id: 'pages.coverage.CoverageReport.endTimeLabel' })}
          rules={[{ required: true }]}
          width="md"
          name="end_time"
         /> */}
      </ModalForm>
    </>
  );
};
export default CreateForm;
