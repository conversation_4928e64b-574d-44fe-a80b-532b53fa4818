import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import {
  FooterToolbar,
  PageContainer,
  ProDescriptions,
  ProTable,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { <PERSON><PERSON>, Drawer, Modal, Popconfirm, Space, message } from 'antd';
import React, { useCallback, useRef, useState } from 'react';
import { CoverageReportBatchDeleteApi, CoverageReportListApi } from '../service';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
const CoverageReport: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.CoverageReport>();
  const [selectedRowsState, setSelectedRows] = useState<API.CoverageReport[]>([]);
  const [open, setOpen] = useState<boolean>(false);
  const [coverageReport, setCoverageReport] = useState<string>('');
  /**
   * @en-US International configuration
   * @zh-CN 国际化配置
   * */

  const [messageApi, contextHolder] = message.useMessage();
  const { run: delRun } = useRequest(CoverageReportBatchDeleteApi, {
    manual: true,
    onSuccess: () => {
      setSelectedRows([]);
      actionRef.current?.reloadAndRest?.();
      messageApi.success('Deleted successfully and will refresh soon');
    },
    onError: () => {
      messageApi.error('Delete failed, please try again');
    },
  });
  const columns: ProColumns<API.CoverageReport>[] = [
    {
      title: '应用名称',
      dataIndex: 'app_name',
      tip: '这是唯一标识',
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '环境标识',
      dataIndex: 'env',
    },
    {
      title: '区域',
      dataIndex: 'region',
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      valueType: 'dateTime',
    },
    {
      title: '结束时间',
      dataIndex: 'end_time',
      valueType: 'dateTime',
    },
    {
      title: '覆盖率文件数量',
      dataIndex: 'covcount',
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '创建时间',
      // sorter: true,
      dataIndex: 'created_at',
      valueType: 'dateTime',
    },
    {
      title: '修改人',
      dataIndex: 'updated_by',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '修改时间',
      // sorter: true,
      dataIndex: 'updated_at',
      valueType: 'dateTime',
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="report"
          onClick={() => {
            setCoverageReport(record.covhtml);
            setOpen(true);
          }}
        >
          查看
        </a>,
        <UpdateForm
          key="update"
          trigger={<a>修改</a>}
          onOk={actionRef.current?.reload}
          values={record}
        />,
      ],
    },
  ];

  /**
   *  Delete node
   * @zh-CN 删除节点
   *
   * @param selectedRows
   */
  const handleRemove = useCallback(
    async (selectedRows: API.CoverageReport[]) => {
      if (!selectedRows?.length) {
        messageApi.warning('请选择删除项');
        return;
      }
      await delRun({
        id: selectedRows.map((row) => row.id),
      });
    },
    [delRun],
  );
  return (
    <PageContainer title={false} breadcrumbRender={false}>
      {contextHolder}
      <ProTable<API.CoverageReport>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [<CreateForm key="create" reload={actionRef.current?.reload} />]}
        request={CoverageReportListApi}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        tableAlertRender={false}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              已选择{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              项 &nbsp;&nbsp;
            </div>
          }
        >
          <Space size={16}>
            <Popconfirm
              title="确定要删除吗?"
              onConfirm={async () => {
                handleRemove(selectedRowsState);
              }}
            >
              <Button danger onClick={async () => {}}>
                批量删除
              </Button>
            </Popconfirm>

            <a
              onClick={async () => {
                actionRef.current?.clearSelected?.();
              }}
            >
              取消选择
            </a>
          </Space>
        </FooterToolbar>
      )}

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<API.CoverageReport>
            column={2}
            title={currentRow?.id}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<API.CoverageReport>[]}
          />
        )}
      </Drawer>

      <Modal
        title="覆盖率报告"
        open={open}
        onCancel={() => setOpen(false)}
        footer={null}
        style={{
          top: 0,
          padding: 0,
        }}
        styles={{
          body: {
            height: '90vh',
            padding: 0,
          },
        }}
        width="80%"
      >
        <iframe
          srcDoc={coverageReport}
          style={{
            width: '100%',
            height: '100%',
          }}
        />
      </Modal>
    </PageContainer>
  );
};
export default CoverageReport;
