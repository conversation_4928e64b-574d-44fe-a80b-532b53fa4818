import Footer from '@/components/Footer';
import { LoginForm } from '@ant-design/pro-components';
import { useEmotionCss } from '@ant-design/use-emotion-css';
import { NewLark } from '@icon-park/react';
import { Helmet, useLocation } from '@umijs/max';
import React from 'react';

import Settings from '../../../../config/defaultSettings';

const Login: React.FC = () => {
  const containerClassName = useEmotionCss(() => {
    return {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage:
        "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
      backgroundSize: '100% 100%',
    };
  });

  const ActionIcons = () => {
    const langClassName = useEmotionCss(({ token }) => {
      return {
        color: 'rgba(0, 0, 0, 0.2)',
        fontSize: '30px',
        verticalAlign: 'middle',
        cursor: 'pointer',
        transition: 'color 0.3s',
        '&:hover': {
          color: token.colorPrimaryActive,
        },
      };
    });
    const base_uri = window.location.protocol + '//' + window.location.host;
    const callback_url = base_uri + '/user/login';
    const redirect_uri = base_uri + '/welcome';
    const location = useLocation();
    const params = new URLSearchParams(location.search);
    const code = params.get('code');
    const state = params.get('state');
    if (code !== null) {
      let login_url = `/api/accounts/feishu/login?code=${code}&callback_url=${callback_url}&next=${redirect_uri}&state=${state}`;
      window.location.href = login_url;
    }
    return (
      <>
        {/*<AlipayCircleOutlined key="AlipayCircleOutlined" className={langClassName} />*/}
        <NewLark
          className={langClassName}
          fill="currentColor"
          key="LarkCircleOutlined"
          onClick={() => {
            window.location.href = `https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=${callback_url}&app_id=${FEISHU_APP_ID}`;
          }}
        />
      </>
    );
  };

  return (
    <div className={containerClassName}>
      <Helmet>
        <title>
          {'登录'}- {Settings.title}
        </title>
      </Helmet>
      <div
        style={{
          flex: '1',
          padding: '32px 0',
        }}
      >
        <LoginForm
          contentStyle={{
            minWidth: 280,
            maxWidth: '75vw',
          }}
          logo={<img alt="logo" src="/icons/favicon-192x192.png" />}
          title={'Switchbot测试平台'}
          subTitle={'为了测试自动化的统一管理和执行'}
          initialValues={{
            autoLogin: true,
          }}
          submitter={false}
          actions={['使用飞书登录：', <ActionIcons key="icons" />]}
        />
      </div>
      <Footer />
    </div>
  );
};
export default Login;
