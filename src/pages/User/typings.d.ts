declare namespace API {
  type Token = {
    key: string;
  };

  type Login = {
    username?: string;
    email?: string;
    password: string;
  };

  type User = {
    /** ID */
    pk: number;
    /** Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only. */
    username: string;
    /** Email address */
    email: string;
    first_name?: string;
    last_name?: string;
    extra_data: Record<string, any>;
  };

  type RestAuthDetail = {
    detail: string;
  };

  type PasswordChange = {
    new_password1: string;
    new_password2: string;
  };

  type PasswordReset = {
    email: string;
  };

  type PasswordResetConfirm = {
    new_password1: string;
    new_password2: string;
    uid: string;
    token: string;
  };

  type PatchedUser = {
    /** ID */
    pk?: number;
    /** Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only. */
    username?: string;
    /** Email address */
    email?: string;
    first_name?: string;
    last_name?: string;
    extra_data?: Record<string, any>;
  };
}
