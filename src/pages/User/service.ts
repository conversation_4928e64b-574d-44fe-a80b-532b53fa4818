import { request } from '@umijs/max';

/** 用户账号密码登录 POST /api/accounts/login */
export async function AccountsLoginApi(body: API.Login, options?: { [key: string]: any }) {
  return request<API.Token>('/api/accounts/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 用户账号密码登出 POST /api/accounts/logout */
export async function AccountsLogoutApi(options?: { [key: string]: any }) {
  return request<API.RestAuthDetail>('/api/accounts/logout', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 用户密码修改 POST /api/accounts/password/change */
export async function AccountsPasswordChangeApi(
  body: API.PasswordChange,
  options?: { [key: string]: any },
) {
  return request<API.RestAuthDetail>('/api/accounts/password/change', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 用户请求密码重置 POST /api/accounts/password/reset */
export async function AccountsPasswordResetApi(
  body: API.PasswordReset,
  options?: { [key: string]: any },
) {
  return request<API.RestAuthDetail>('/api/accounts/password/reset', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 用户密码重置确认 POST /api/accounts/password/reset/confirm */
export async function AccountsPasswordResetConfirmApi(
  body: API.PasswordResetConfirm,
  options?: { [key: string]: any },
) {
  return request<API.RestAuthDetail>('/api/accounts/password/reset/confirm', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取用户信息 GET /api/accounts/user */
export async function AccountsUserApi(options?: { [key: string]: any }) {
  return request<API.User>('/api/accounts/user', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 用户信息修改 PUT /api/accounts/user */
export async function AccountsUserUpdateApi(body: API.User, options?: { [key: string]: any }) {
  return request<API.User>('/api/accounts/user', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 用户信息部分修改 PATCH /api/accounts/user */
export async function AccountsUserPartialUpdateApi(
  body: API.PatchedUser,
  options?: { [key: string]: any },
) {
  return request<API.User>('/api/accounts/user', {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
