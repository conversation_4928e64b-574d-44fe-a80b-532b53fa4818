declare namespace API {
  type apiSimulateCallbackConfigDestroy2Params = {
    id: string;
  };

  type apiSimulateCallbackConfigListParams = {
    /** A page number within the paginated result set. */
    current?: number;
    /** Which field to use when ordering the results. */
    ordering?: string;
    /** Number of results to return per page. */
    pageSize?: number;
  };

  type apiSimulateCallbackConfigRetrieveParams = {
    id: string;
  };

  type apiSimulateCallbackConfigUpdateParams = {
    id: string;
  };

  type apiSimulateDeviceDestroy2Params = {
    mac: string;
  };

  type apiSimulateDeviceListParams = {
    /** A page number within the paginated result set. */
    current?: number;
    /** Which field to use when ordering the results. */
    ordering?: string;
    /** Number of results to return per page. */
    pageSize?: number;
  };

  type apiSimulateDeviceRetrieveParams = {
    mac: string;
  };

  type apiSimulateDeviceUpdateParams = {
    mac: string;
  };

  type apiSimulateSupportStatusRetrieveParams = {
    alias: string;
  };

  type apiSimulateSupportTypeDestroy2Params = {
    alias: string;
  };

  type apiSimulateSupportTypeListParams = {
    /** A page number within the paginated result set. */
    current?: number;
    /** Which field to use when ordering the results. */
    ordering?: string;
    /** Number of results to return per page. */
    pageSize?: number;
  };

  type apiSimulateSupportTypeRetrieveParams = {
    alias: string;
  };

  type apiSimulateSupportTypeUpdateParams = {
    alias: string;
  };

  type EnvEnum = 'test' | 'prod';

  type PaginatedSimulateDeviceCallbackConfigList = {
    count?: number;
    next?: string;
    previous?: string;
    results?: SimulateDeviceCallbackConfig[];
  };

  type PaginatedSimulateDeviceList = {
    count?: number;
    next?: string;
    previous?: string;
    results?: SimulateDevice[];
  };

  type PaginatedSimulateDeviceTypeList = {
    count?: number;
    next?: string;
    previous?: string;
    results?: SimulateDeviceType[];
  };

  type ProductNameEnum = 'Floor Cleaning Robot S10' | 'Hub 2' | 'Bot';

  type RegionEnum = 'us-east-1' | 'ap-northeast-1' | 'eu-northeast-1';

  type S3PolicyRequest = {
    access_key_id: string;
    secret_access_key: string;
    session_token: string;
    action: string;
    destination?: string;
  };

  type S3UploadMapRequest = {
    mac: string;
    role_alias_url: string;
    thingname: string;
    room: number;
    bucket: string;
    map_id: string;
    label?: boolean;
  };

  type SimulateDevice = {
    mac?: string;
    created_by: string;
    updated_by: string;
    type?: string;
    product_name?: string;
    created_at: string;
    updated_at: string;
    /** 设备所在区域

* `us-east-1` - Us
* `ap-northeast-1` - Ap
* `eu-northeast-1` - Eu */
    region?: RegionEnum;
    /** 设备环境

* `test` - Test
* `prod` - Prod */
    env?: EnvEnum;
    /** 是否是子设备 */
    is_sub?: boolean;
    /** 设备状态

* `on` - Online
* `off` - Offline */
    status?: StatusEnum;
    /** 设备类型别名 */
    alias_type: string;
    /** 物模型设备所属用户id, client_id使用 */
    belong_user?: string;
    /** iot私钥 */
    cert_pem?: string;
    /** iot公钥 */
    key_pem?: string;
  };

  type SimulateDeviceCallbackConfig = {
    id: string;
    created_by: string;
    updated_by: string;
    created_at: string;
    updated_at: string;
    /** 配置名称 */
    name: string;
    /** 设备mac地址 */
    mac: string;
    /** 物模型方法id */
    function_id: number;
    /** 物模型回复方法模板 */
    callback_template: Record<string, any>;
  };

  type SimulateDeviceType = {
    /** 设备类型别名 */
    alias: string;
    created_by: string;
    updated_by: string;
    created_at: string;
    updated_at: string;
    /** 设备类型

* `WoSweeperOrigin` - WoSweeperOrigin
* `Hub2` - Hub2
* `Bot` - Bot */
    type: TypeEnum;
    /** 产品名称

* `Floor Cleaning Robot S10` - WoSweeperOrigin
* `Hub 2` - Hub2
* `Bot` - Bot */
    product_name: ProductNameEnum;
    /** 设备物模型属性 */
    properties: Record<string, any>;
  };

  type StatusCommandRequest = {
    mac: string;
    props: any[];
  };

  type StatusEnum = 'on' | 'off';

  type TypeEnum = 'WoSweeperOrigin' | 'Hub2' | 'Bot';
}
