import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import {
  <PERSON>er<PERSON>oolbar,
  PageContainer,
  ProDescriptions,
  ProTable,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { <PERSON><PERSON>, Drawer, Popconfirm, Space, message } from 'antd';
import React, { useCallback, useRef, useState } from 'react';
import { apiSimulateSupportTypeDestroy, apiSimulateSupportTypeList } from '../service';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
const TypeManage: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.SimulateDeviceType>();
  const [selectedRowsState, setSelectedRows] = useState<API.SimulateDeviceType[]>([]);

  /**
   * @en-US International configuration
   * @zh-CN 国际化配置
   * */

  const [messageApi, contextHolder] = message.useMessage();
  const { run: delRun } = useRequest(apiSimulateSupportTypeDestroy, {
    manual: true,
    onSuccess: () => {
      setSelectedRows([]);
      actionRef.current?.reloadAndRest?.();
      messageApi.success('Deleted successfully and will refresh soon');
    },
    onError: () => {
      messageApi.error('Delete failed, please try again');
    },
  });
  const columns: ProColumns<API.SimulateDeviceType>[] = [
    {
      title: '设备类型别名',
      dataIndex: 'alias',
      tip: '这是唯一标识',
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '设备类型',
      dataIndex: 'type',
      // valueType: 'textarea',
    },
    {
      title: '产品名称',
      dataIndex: 'product_name',
    },
    {
      title: '设备物模型属性',
      dataIndex: 'properties',
      valueType: 'jsonCode',
      renderText: (dom, entity) => {
        return JSON.stringify(entity.properties);
      },
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '创建时间',
      // sorter: true,
      dataIndex: 'created_at',
      valueType: 'dateTime',
    },
    {
      title: '修改人',
      dataIndex: 'updated_by',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '修改时间',
      // sorter: true,
      dataIndex: 'updated_at',
      valueType: 'dateTime',
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <UpdateForm
          key="update"
          trigger={<a>修改</a>}
          onOk={actionRef.current?.reload}
          values={record}
        />,
      ],
    },
  ];

  /**
   *  Delete node
   * @zh-CN 删除节点
   *
   * @param selectedRows
   */
  const handleRemove = useCallback(
    async (selectedRows: API.SimulateDeviceType[]) => {
      if (!selectedRows?.length) {
        messageApi.warning('请选择删除项');
        return;
      }
      await delRun({
        data: {
          alias: selectedRows.map((row) => row.alias),
        },
      });
    },
    [delRun],
  );
  return (
    <PageContainer title={false} breadcrumbRender={false}>
      {contextHolder}
      <ProTable<API.SimulateDeviceType, API.apiSimulateSupportTypeListParams>
        actionRef={actionRef}
        rowKey="alias"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [<CreateForm key="create" reload={actionRef.current?.reload} />]}
        request={apiSimulateSupportTypeList}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        tableAlertRender={false}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              已选择{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              项 &nbsp;&nbsp;
            </div>
          }
        >
          <Space size={16}>
            <Popconfirm
              title="确定要删除吗?"
              onConfirm={async () => {
                handleRemove(selectedRowsState);
              }}
            >
              <Button danger onClick={async () => {}}>
                批量删除
              </Button>
            </Popconfirm>

            <a
              onClick={async () => {
                actionRef.current?.clearSelected?.();
              }}
            >
              取消选择
            </a>
          </Space>
        </FooterToolbar>
      )}

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.alias && (
          <ProDescriptions<API.SimulateDeviceType>
            column={1}
            title={currentRow?.alias}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.alias,
            }}
            columns={columns as ProDescriptionsItemProps<API.SimulateDeviceType>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};
export default TypeManage;
