import { SimulatorProductEnum } from '@/constants';
import { apiSimulateSupportTypeUpdate } from '@/pages/Simulator/service';
import { ModalForm, ProFormSelect, ProFormTextArea } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message } from 'antd';
import React, { cloneElement, useCallback, useState } from 'react';
export type UpdateFormProps = {
  trigger?: JSX.Element;
  onOk?: () => void;
  values: Partial<API.SimulateDeviceType>;
};
const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { onOk, values, trigger } = props;
  const [open, setOpen] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const { run, loading } = useRequest(apiSimulateSupportTypeUpdate, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Configuration is successful');
      onOk?.();
    },
    onError: () => {
      messageApi.error('Configuration failed, please try again!');
    },
  });

  // const onCancel = useCallback(() => {
  //   setOpen(false);
  // }, []);

  const onOpen = useCallback(() => {
    setOpen(true);
  }, []);

  // const onFinish = useCallback(
  //   async (values?: any) => {
  //     await run({ data: values });

  //     onCancel();
  //   },
  //   [onCancel, run],
  // );

  return (
    <>
      {contextHolder}
      <ModalForm
        title={'修改虚拟设备类型'}
        initialValues={values}
        trigger={
          trigger
            ? cloneElement(trigger, {
                onClick: onOpen,
                open: open,
              })
            : undefined
        }
        width="400px"
        modalProps={{
          okButtonProps: {
            loading,
          },
        }}
        onFinish={async (value) => {
          await run(
            {
              alias: values.alias,
            },
            {
              ...(value as Partial<API.SimulateDeviceType>),
            },
          );
          return true;
        }}
      >
        <ProFormSelect
          label={'产品名称'}
          rules={[
            {
              required: true,
              message: '产品名称为必填项',
            },
          ]}
          width="md"
          name="product_name"
          valueEnum={SimulatorProductEnum}
        />
        <ProFormTextArea
          label={'设备物模型属性'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="properties"
          convertValue={(value) => {
            if (typeof value === 'object') {
              return JSON.stringify(value, null, 2);
            }
            return value;
          }}
        />
      </ModalForm>
    </>
  );
};
export default UpdateForm;
