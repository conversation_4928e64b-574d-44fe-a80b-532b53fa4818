import { SimulatorProductEnum } from '@/constants';
import { apiSimulateSupportTypeCreate } from '@/pages/Simulator/service';
import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  ModalForm,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message } from 'antd';
import { FC } from 'react';
interface CreateFormProps {
  reload?: ActionType['reload'];
}
const CreateForm: FC<CreateFormProps> = (props) => {
  const { reload } = props;
  const [messageApi, contextHolder] = message.useMessage();
  /**
   * @en-US International configuration
   * @zh-CN 国际化配置
   * */

  const { run, loading } = useRequest(apiSimulateSupportTypeCreate, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Added successfully');
      reload?.();
    },
    onError: () => {
      messageApi.error('Adding failed, please try again!');
    },
  });
  return (
    <>
      {contextHolder}
      <ModalForm
        title={'新建虚拟设备类型'}
        trigger={
          <Button type="primary" icon={<PlusOutlined />}>
            新建
          </Button>
        }
        width="400px"
        modalProps={{
          okButtonProps: {
            loading,
          },
        }}
        onFinish={async (value) => {
          await run({
            ...(value as API.SimulateDeviceType),
          });
          return true;
        }}
      >
        <ProFormSelect
          label={'产品名称'}
          rules={[
            {
              required: true,
              message: '产品名称为必填项',
            },
          ]}
          width="md"
          name="product_name"
          valueEnum={SimulatorProductEnum}
        />
        <ProFormText label={'设备类型别名'} width="md" name="alias" />
        <ProFormTextArea
          label={'设备物模型属性'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="properties"
        />
      </ModalForm>
    </>
  );
};
export default CreateForm;
