import { SimulatorEnvEnum } from '@/constants';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import {
  FooterToolbar,
  PageContainer,
  ProDescriptions,
  ProTable,
} from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { But<PERSON>, Drawer, Popconfirm, Space, message } from 'antd';
import React, { useCallback, useRef, useState } from 'react';
import { apiSimulateDeviceDestroy, apiSimulateDeviceList } from '../service';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
const SimulateDevice: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.SimulateDevice>();
  const [selectedRowsState, setSelectedRows] = useState<API.SimulateDevice[]>([]);

  /**
   * @en-US International configuration
   * @zh-CN 国际化配置
   * */

  const [messageApi, contextHolder] = message.useMessage();
  const { run: delRun } = useRequest(apiSimulateDeviceDestroy, {
    manual: true,
    onSuccess: () => {
      setSelectedRows([]);
      actionRef.current?.reloadAndRest?.();
      messageApi.success('Deleted successfully and will refresh soon');
    },
    onError: () => {
      // messageApi.error('Delete failed, please try again');
    },
  });
  const columns: ProColumns<API.SimulateDevice>[] = [
    {
      title: '蓝牙Mac',
      dataIndex: 'mac',
      copyable: true,
      tip: '这是唯一标识',
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '类型别名',
      hideInTable: true,
      hideInForm: true,
      dataIndex: 'alias_type',
    },
    {
      title: '设备类型',
      dataIndex: 'type',
    },
    {
      title: '产品名称',
      dataIndex: 'product_name',
    },
    {
      title: '环境',
      dataIndex: 'env',
      valueEnum: SimulatorEnvEnum,
    },
    {
      title: '区域',
      dataIndex: 'region',
    },
    {
      title: '子设备',
      dataIndex: 'is_sub',
      hideInSearch: true,
      hideInForm: true,
      valueEnum: {
        true: {
          text: '是',
        },
        false: {
          text: '否',
        },
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      hideInForm: true,
      valueEnum: {
        on: {
          text: '在线',
          status: 'success',
        },
        off: {
          text: '离线',
          status: 'error',
        },
      },
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '创建时间',
      // sorter: true,
      dataIndex: 'created_at',
      valueType: 'dateTime',
    },
    {
      title: '修改人',
      dataIndex: 'updated_by',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '修改时间',
      // sorter: true,
      dataIndex: 'updated_at',
      valueType: 'dateTime',
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            history.push(`/simulator/detail?mac=${record.mac}`);
          }}
        >
          调试设备
        </a>,
        <UpdateForm
          key="update"
          trigger={<a>修改</a>}
          onOk={actionRef.current?.reload}
          values={record}
        />,
      ],
    },
  ];

  /**
   *  Delete node
   * @zh-CN 删除节点
   *
   * @param selectedRows
   */
  const handleRemove = useCallback(
    async (selectedRows: API.SimulateDevice[]) => {
      if (!selectedRows?.length) {
        messageApi.warning('请选择删除项');
        return;
      }
      await delRun({
        data: {
          mac: selectedRows.map((row) => row.mac),
        },
      });
    },
    [delRun],
  );
  return (
    <PageContainer title={false} breadcrumbRender={false}>
      {contextHolder}
      <ProTable<API.SimulateDevice, API.apiSimulateSupportTypeListParams>
        actionRef={actionRef}
        rowKey="mac"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [<CreateForm key="create" reload={actionRef.current?.reload} />]}
        request={apiSimulateDeviceList}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        tableAlertRender={false}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              已选择{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              项 &nbsp;&nbsp;
            </div>
          }
        >
          <Space size={16}>
            <Popconfirm
              title="确定要删除吗?"
              onConfirm={async () => {
                handleRemove(selectedRowsState);
              }}
            >
              <Button danger onClick={async () => {}}>
                批量删除
              </Button>
            </Popconfirm>

            <a
              onClick={async () => {
                actionRef.current?.clearSelected?.();
              }}
            >
              取消选择
            </a>
          </Space>
        </FooterToolbar>
      )}

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.mac && (
          <ProDescriptions<API.SimulateDevice>
            column={1}
            title={currentRow?.mac}
            request={async () => ({
              data: currentRow || {},
            })}
            // params={{
            //   mac: currentRow?.mac,
            // }}
            columns={columns as ProDescriptionsItemProps<API.SimulateDevice>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};
export default SimulateDevice;
