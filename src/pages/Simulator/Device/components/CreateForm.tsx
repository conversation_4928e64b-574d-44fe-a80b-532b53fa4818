import { SimulatorEnvEnum, SimulatorRegionEnum, SimulatorStatusEnum } from '@/constants';
import { apiSimulateDeviceCreate } from '@/pages/Simulator/service';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ModalForm, ProFormSelect } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { connect } from '@umijs/plugins/libs/dva';
import { Button, message } from 'antd';
import { useEffect } from 'react';
// import { connect } from 'umi';

import { FC } from 'react';
interface CreateFormProps {
  reload?: ActionType['reload'];
  dispatch?: any;
  simulator?: any;
}
const CreateForm: FC<CreateFormProps> = (props) => {
  const { reload, dispatch, simulator } = props;
  const [messageApi, contextHolder] = message.useMessage();
  // const [selectedType, setSelectedType] = useState<ProductNameEnum>();
  // const [selectedProductName, setelectedProductName] = useState<SimulatorProductEnum>();
  /**
   * @en-US International configuration
   * @zh-CN 国际化配置
   * */

  const { run, loading } = useRequest(apiSimulateDeviceCreate, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Added successfully');
      reload?.();
    },
    onError: () => {
      // messageApi.error('Adding failed, please try again!');
    },
  });
  useEffect(() => {
    dispatch({
      type: 'simulator/fetchAliasType',
    });
  }, [dispatch]);
  return (
    <>
      {contextHolder}
      <ModalForm
        title={'新建虚拟设备'}
        trigger={
          <Button type="primary" icon={<PlusOutlined />}>
            新建
          </Button>
        }
        width="400px"
        modalProps={{
          okButtonProps: {
            loading,
          },
        }}
        onFinish={async (value) => {
          await run({
            ...(value as API.SimulateDevice),
          });
          return true;
        }}
      >
        <ProFormSelect
          label={'设备类型别名'}
          rules={[
            {
              required: true,
              message: '设备类型别名',
            },
          ]}
          width="md"
          name="alias_type"
          request={async () => simulator.aliasTypes}
        />
        <ProFormSelect
          label={'环境'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="env"
          initialValue="test"
          valueEnum={SimulatorEnvEnum}
        />
        <ProFormSelect
          label={'区域'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="region"
          initialValue={SimulatorRegionEnum['us-east-1']}
          valueEnum={SimulatorRegionEnum}
        />
        <ProFormSelect
          label={'状态'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="status"
          initialValue={SimulatorStatusEnum.on}
          valueEnum={SimulatorStatusEnum}
        />
      </ModalForm>
    </>
  );
};
export default connect((props) => props)(CreateForm);
