import { SimulatorEnvEnum, SimulatorRegionEnum } from '@/constants';
import { apiSimulateDeviceUpdate } from '@/pages/Simulator/service';
import { ModalForm, ProFormSelect } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { connect } from '@umijs/plugins/libs/dva';
import { message } from 'antd';
import React, { cloneElement, useCallback, useEffect, useState } from 'react';
export type UpdateFormProps = {
  trigger?: JSX.Element;
  onOk?: () => void;
  values: Partial<API.SimulateDevice>;
  dispatch?: any;
  simulator?: any;
};
const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { onOk, values, trigger, dispatch, simulator } = props;
  const [open, setOpen] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  useEffect(() => {
    dispatch({
      type: 'simulator/fetchAliasType',
    });
  }, [dispatch]);
  const { run, loading } = useRequest(apiSimulateDeviceUpdate, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Configuration is successful');
      onOk?.();
    },
    onError: () => {
      messageApi.error('Configuration failed, please try again!');
    },
  });
  const onCancel = useCallback(() => {
    setOpen(false);
  }, []);
  const onOpen = useCallback(() => {
    setOpen(true);
  }, []);

  // const onFinish = useCallback(
  //   async (values?: any) => {
  //     await run({ data: values });

  //     onCancel();
  //   },
  //   [onCancel, run],
  // );

  return (
    <>
      {contextHolder}
      <ModalForm
        title={'修改虚拟设备' + values.mac}
        initialValues={values}
        trigger={
          trigger
            ? cloneElement(trigger, {
                onClick: onOpen,
                open: open,
              })
            : undefined
        }
        width="400px"
        modalProps={{
          okButtonProps: {
            loading,
          },
          onCancel: onCancel,
        }}
        onFinish={async (value) => {
          await run(
            {
              mac: values.mac,
            },
            {
              ...(value as Partial<API.SimulateDevice>),
            },
          );
          return true;
        }}
      >
        <ProFormSelect
          label={'设备类型别名'}
          rules={[
            {
              required: true,
              message: '设备类型别名',
            },
          ]}
          width="md"
          name="alias_type"
          request={async () => simulator.aliasTypes}
        />
        <ProFormSelect
          label={'环境'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="env"
          valueEnum={SimulatorEnvEnum}
        />
        <ProFormSelect
          label={'区域'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="region"
          valueEnum={SimulatorRegionEnum}
        />
        <ProFormSelect
          label={'子设备'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="is_sub"
          options={[
            {
              value: true,
              label: '是',
            },
            {
              value: false,
              label: '否',
            },
          ]}
        />
        <ProFormSelect
          label={'状态'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="status"
          options={[
            {
              value: 'on',
              label: '在线',
            },
            {
              value: 'off',
              label: '离线',
            },
          ]}
        />
      </ModalForm>
    </>
  );
};
export default connect((props) => props)(UpdateForm);
