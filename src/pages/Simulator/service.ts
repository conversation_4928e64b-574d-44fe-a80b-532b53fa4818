import { request } from '@umijs/max';

/** 已创建的设备类型别名
:param request:
:param args:
:param kwargs:
:return: GET /api/simulate/alias_type */
export async function apiSimulateAliasTypeRetrieve(options?: { [key: string]: any }) {
  return request<any>('/api/simulate/alias_type', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 虚拟设备callback配置 列表、新增、批量删除 GET /api/simulate/callback_config */
export async function apiSimulateCallbackConfigList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.apiSimulateCallbackConfigListParams,
  options?: { [key: string]: any },
) {
  return request<API.PaginatedSimulateDeviceCallbackConfigList>('/api/simulate/callback_config', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 虚拟设备callback配置 列表、新增、批量删除 POST /api/simulate/callback_config */
export async function apiSimulateCallbackConfigCreate(
  body: API.SimulateDeviceCallbackConfig,
  options?: { [key: string]: any },
) {
  return request<API.SimulateDeviceCallbackConfig>('/api/simulate/callback_config', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除接口 DELETE /api/simulate/callback_config */
export async function apiSimulateCallbackConfigDestroy(options?: { [key: string]: any }) {
  return request<any>('/api/simulate/callback_config', {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 虚拟设备callback配置 单条 详情、修改、删除 GET /api/simulate/callback_config/${param0} */
export async function apiSimulateCallbackConfigRetrieve(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.apiSimulateCallbackConfigRetrieveParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.SimulateDeviceCallbackConfig>(`/api/simulate/callback_config/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 虚拟设备callback配置 单条 详情、修改、删除 PUT /api/simulate/callback_config/${param0} */
export async function apiSimulateCallbackConfigUpdate(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.apiSimulateCallbackConfigUpdateParams,
  body: API.SimulateDeviceCallbackConfig,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.SimulateDeviceCallbackConfig>(`/api/simulate/callback_config/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 虚拟设备callback配置 单条 详情、修改、删除 DELETE /api/simulate/callback_config/${param0} */
export async function apiSimulateCallbackConfigDestroy2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.apiSimulateCallbackConfigDestroy2Params,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/api/simulate/callback_config/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/simulate/command */
export async function apiSimulateCommandCreate(
  body: API.StatusCommandRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/simulate/command', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 虚拟设备 列表、新增、批量删除 GET /api/simulate/device */
export async function apiSimulateDeviceList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.apiSimulateDeviceListParams,
  options?: { [key: string]: any },
) {
  return request<API.PaginatedSimulateDeviceList>('/api/simulate/device', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 虚拟设备生成
1. 使用参数中的类型，生成对应类型的设备，产品名称复制
2. 随机生成mac(特定格式)，插入防重表，避免线上撞mac
3. 默认online状态，发送消息到任务服务，启动mqtt客户端监听 POST /api/simulate/device */
export async function apiSimulateDeviceCreate(
  body: API.SimulateDevice,
  options?: { [key: string]: any },
) {
  return request<API.SimulateDevice>('/api/simulate/device', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除接口 DELETE /api/simulate/device */
export async function apiSimulateDeviceDestroy(options?: { [key: string]: any }) {
  return request<any>('/api/simulate/device', {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 虚拟设备单条 详情、修改、删除

虚拟设备信息修改
1. 不允许让用户修改mac

虚拟设备删除
1. 软删除，其他信息保留
2. 发送消息，通知mqtt客户端下线 GET /api/simulate/device/${param0} */
export async function apiSimulateDeviceRetrieve(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.apiSimulateDeviceRetrieveParams,
  options?: { [key: string]: any },
) {
  const { mac: param0, ...queryParams } = params;
  return request<API.SimulateDevice>(`/api/simulate/device/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 虚拟设备单条 详情、修改、删除

虚拟设备信息修改
1. 不允许让用户修改mac

虚拟设备删除
1. 软删除，其他信息保留
2. 发送消息，通知mqtt客户端下线 PUT /api/simulate/device/${param0} */
export async function apiSimulateDeviceUpdate(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.apiSimulateDeviceUpdateParams,
  body: API.SimulateDevice,
  options?: { [key: string]: any },
) {
  const { mac: param0, ...queryParams } = params;
  return request<API.SimulateDevice>(`/api/simulate/device/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 虚拟设备单条 详情、修改、删除

虚拟设备信息修改
1. 不允许让用户修改mac

虚拟设备删除
1. 软删除，其他信息保留
2. 发送消息，通知mqtt客户端下线 DELETE /api/simulate/device/${param0} */
export async function apiSimulateDeviceDestroy2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.apiSimulateDeviceDestroy2Params,
  options?: { [key: string]: any },
) {
  const { mac: param0, ...queryParams } = params;
  return request<any>(`/api/simulate/device/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/simulate/funcs */
export async function apiSimulateFuncsRetrieve(options?: { [key: string]: any }) {
  return request<any>('/api/simulate/funcs', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 产品类型枚举
:param request:
:param args:
:param kwargs:
:return: GET /api/simulate/product */
export async function apiSimulateProductRetrieve(options?: { [key: string]: any }) {
  return request<any>('/api/simulate/product', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/simulate/props */
export async function apiSimulatePropsRetrieve(options?: { [key: string]: any }) {
  return request<any>('/api/simulate/props', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/simulate/s3_policy */
export async function apiSimulateS3PolicyCreate(
  body: API.S3PolicyRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/simulate/s3_policy', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/simulate/support_status/${param0} */
export async function apiSimulateSupportStatusRetrieve(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.apiSimulateSupportStatusRetrieveParams,
  options?: { [key: string]: any },
) {
  const { alias: param0, ...queryParams } = params;
  return request<any>(`/api/simulate/support_status/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 虚拟设备类型 列表、新增、批量删除 GET /api/simulate/support_type */
export async function apiSimulateSupportTypeList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.apiSimulateSupportTypeListParams,
  options?: { [key: string]: any },
) {
  return request<API.PaginatedSimulateDeviceTypeList>('/api/simulate/support_type', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 虚拟设备类型 列表、新增、批量删除 POST /api/simulate/support_type */
export async function apiSimulateSupportTypeCreate(
  body: API.SimulateDeviceType,
  options?: { [key: string]: any },
) {
  return request<API.SimulateDeviceType>('/api/simulate/support_type', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除接口 DELETE /api/simulate/support_type */
export async function apiSimulateSupportTypeDestroy(options?: { [key: string]: any }) {
  return request<any>('/api/simulate/support_type', {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 虚拟设备类型单条 详情、修改、删除 GET /api/simulate/support_type/${param0} */
export async function apiSimulateSupportTypeRetrieve(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.apiSimulateSupportTypeRetrieveParams,
  options?: { [key: string]: any },
) {
  const { alias: param0, ...queryParams } = params;
  return request<API.SimulateDeviceType>(`/api/simulate/support_type/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 虚拟设备类型单条 详情、修改、删除 PUT /api/simulate/support_type/${param0} */
export async function apiSimulateSupportTypeUpdate(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.apiSimulateSupportTypeUpdateParams,
  body: API.SimulateDeviceType,
  options?: { [key: string]: any },
) {
  const { alias: param0, ...queryParams } = params;
  return request<API.SimulateDeviceType>(`/api/simulate/support_type/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 虚拟设备类型单条 详情、修改、删除 DELETE /api/simulate/support_type/${param0} */
export async function apiSimulateSupportTypeDestroy2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.apiSimulateSupportTypeDestroy2Params,
  options?: { [key: string]: any },
) {
  const { alias: param0, ...queryParams } = params;
  return request<any>(`/api/simulate/support_type/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/simulate/upload_map */
export async function apiSimulateUploadMapCreate(
  body: API.S3UploadMapRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/simulate/upload_map', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
