import PropertiesForm from '@/components/PropertiesForm';
import type { ActionType } from '@ant-design/pro-components';
import { <PERSON>ridContent, PageContainer } from '@ant-design/pro-components';
import { history, useLocation } from '@umijs/max';
import { connect } from '@umijs/plugins/libs/dva';
import type { TabsProps } from 'antd';
import { Descriptions, Menu, Tabs } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import useStyles from './style.style';

const SimulateDeviceDetail: React.FC = (props) => {
  const { styles } = useStyles();
  const { dispatch, simulator } = props;

  const actionRef = useRef<ActionType>();

  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.SimulateDevice>();
  const [selectedRowsState, setSelectedRows] = useState<API.SimulateDevice[]>([]);
  const [selectKey, setSelectedKey] = useState<string>('');

  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const mac = params.get('mac');

  // const menuMap: Record<string, React.ReactNode> = {
  //   base: '基本设置',
  //   security: '安全设置',
  //   binding: '账号绑定',
  //   notification: '新消息通知',
  // };
  const getMenu = () => {
    // console.debug(simulator.currentDevice.alias_type);

    return simulator.devices.map((item: API.SimulateDevice) => ({
      key: item.mac,
      label: `${item.type}[${item.mac}]`,
    }));
  };

  useEffect(() => {
    dispatch({
      type: 'simulator/fetchDeviceList',
    });
    if (mac) {
      setSelectedKey(mac); // 用url直接进入时，标记已选中
      dispatch({
        type: 'simulator/fetchCurrentDeviceInfo',
        params: { mac: mac },
      });
    }
  }, [location.search]);

  // const [messageApi, contextHolder] = message.useMessage();
  const onChange = (key: string) => {
    console.log(key);
  };
  const itemToLabel = (deviceItem: Record<string, any>) =>
    Object.keys(deviceItem).map((key) => {
      return { label: key, children: deviceItem[key] };
    });

  const baseInfoSection = (
    <Descriptions title="DeviceInfo" items={itemToLabel(simulator.currentDevice)} />
  );

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '基础信息',
      children: baseInfoSection,
    },
    {
      key: '2',
      label: '模拟设备上报',
      children: <PropertiesForm type={simulator.currentDevice.alias_type} />,
    },
    {
      key: '3',
      label: '动作执行',
      children: 'Content of Tab Pane 3',
    },
  ];

  return (
    <PageContainer title={false} breadcrumbRender={false}>
      {/* {contextHolder} */}
      <GridContent>
        <div
          className={styles.main}
          // ref={(ref) => {
          //   if (ref) {
          //     dom.current = ref;
          //   }
          // }}
        >
          <div className={styles.leftMenu}>
            <Menu
              mode={'inline'}
              selectedKeys={[selectKey]}
              onClick={({ key }) => {
                history.push(`/simulator/detail?mac=${key}`);
                setSelectedKey(key); // 点击侧栏时，标记已选中
              }}
              items={getMenu()}
            />
          </div>
          <div className={styles.right}>
            {/* <div className={styles.title}>{simulator.devices[mac]}</div> */}
            {/* {renderChildren()} */}
            <Tabs defaultActiveKey="1" items={items} onChange={onChange} />
          </div>
        </div>
      </GridContent>
    </PageContainer>
  );
};

export default connect((props) => props)(SimulateDeviceDetail);
