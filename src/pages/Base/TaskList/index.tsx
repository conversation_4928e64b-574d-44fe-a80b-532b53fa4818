import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import {
  FooterToolbar,
  PageContainer,
  ProDescriptions,
  ProTable,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { <PERSON><PERSON>, Drawer, Popconfirm, Space, Switch, message } from 'antd';
import React, { useCallback, useRef, useState } from 'react';
import { ScheduledTasksBatchDeleteApi, ScheduledTasksListApi } from '../service';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
const TaskList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.Tag>();
  const [selectedRowsState, setSelectedRows] = useState<API.Tag[]>([]);

  const [messageApi, contextHolder] = message.useMessage();
  const { run: delRun } = useRequest(ScheduledTasksBatchDeleteApi, {
    manual: true,
    onSuccess: () => {
      setSelectedRows([]);
      actionRef.current?.reloadAndRest?.();
      messageApi.success('Deleted successfully and will refresh soon');
    },
    onError: () => {
      messageApi.error('Delete failed, please try again');
    },
  });
  const columns: ProColumns[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 50,
    },
    {
      title: '任务名称',
      dataIndex: 'name',
      width: 100,
    },
    {
      title: 'crontab',
      dataIndex: 'crontab',
      width: 100,
    },
    {
      title: '任务周期描述',
      dataIndex: 'crontab_desc',
      width: 100,
    },
    {
      title: '任务标识',
      dataIndex: 'task',
      width: 60,
      valueEnum: {
        'app.testcase.tasks.run_test_cases_by_project': 'project',
        'app.testcase.tasks.run_test_cases_by_dir': 'directory',
        'app.testcase.tasks.run_test_cases_by_plan': 'plan',
      },
    },
    {
      title: '任务参数',
      dataIndex: 'kwargs',
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      width: 60,
      render: (_, record) => <Switch checked={record.enabled} />,
    },
    {
      title: '修改时间',
      // sorter: true,
      dataIndex: 'updated_at',
      valueType: 'dateTime',
      width: 160,
    },
    {
      title: '上次运行时间',
      // sorter: true,
      dataIndex: 'last_run_at',
      valueType: 'dateTime',
      width: 160,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      width: 50,
      render: (_, record) => [
        <UpdateForm
          key="update"
          trigger={<a>修改</a>}
          onOk={actionRef.current?.reload}
          values={record}
        />,
      ],
    },
  ];

  /**
   *  Delete node
   * @zh-CN 删除节点
   *
   * @param selectedRows
   */
  const handleRemove = useCallback(
    async (selectedRows: API.Tag[]) => {
      if (!selectedRows?.length) {
        messageApi.warning('请选择删除项');
        return;
      }
      await delRun({
        id: selectedRows.map((row) => row.id),
      });
    },
    [delRun],
  );
  return (
    <PageContainer title={false} breadcrumbRender={false}>
      {contextHolder}
      <ProTable<API.ScheduledTask>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [<CreateForm key="create" reload={actionRef.current?.reload} />]}
        request={ScheduledTasksListApi}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        tableAlertRender={false}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              已选择{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              项 &nbsp;&nbsp;
            </div>
          }
        >
          <Space size={16}>
            <Popconfirm
              title="确定要删除吗?"
              onConfirm={async () => {
                handleRemove(selectedRowsState);
              }}
            >
              <Button danger onClick={async () => {}}>
                批量删除
              </Button>
            </Popconfirm>

            <a
              onClick={async () => {
                actionRef.current?.clearSelected?.();
              }}
            >
              取消选择
            </a>
          </Space>
        </FooterToolbar>
      )}

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<API.Tag>
            column={2}
            title={currentRow?.id}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<API.Tag>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};
export default TaskList;
