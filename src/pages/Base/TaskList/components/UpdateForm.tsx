import { ModalForm, ProFormSelect, ProFormSwitch, ProFormText } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message } from 'antd';
import React, { cloneElement, useCallback, useState } from 'react';
import { ScheduledTasksUpdateApi } from '../../service';
export type UpdateFormProps = {
  trigger?: JSX.Element;
  onOk?: () => void;
  values: Partial<API.Tag>;
};
const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { onOk, values, trigger } = props;
  const [open, setOpen] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const { run, loading } = useRequest(ScheduledTasksUpdateApi, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Configuration is successful');
      onOk?.();
    },
    onError: () => {
      messageApi.error('Configuration failed, please try again!');
    },
  });

  // const onCancel = useCallback(() => {
  //   setOpen(false);
  // }, []);

  const onOpen = useCallback(() => {
    setOpen(true);
  }, []);

  // const onFinish = useCallback(
  //   async (values?: any) => {
  //     await run({ data: values });

  //     onCancel();
  //   },
  //   [onCancel, run],
  // );

  return (
    <>
      {contextHolder}
      <ModalForm
        title={'修改测试用例标签'}
        initialValues={values}
        trigger={
          trigger
            ? cloneElement(trigger, {
                onClick: onOpen,
                open: open,
              })
            : undefined
        }
        width="400px"
        modalProps={{
          okButtonProps: {
            loading,
          },
        }}
        onFinish={async (value) => {
          await run(
            {
              id: values.id,
            },
            {
              ...(value as Partial<API.ScheduledTask>),
            },
          );
          return true;
        }}
      >
        <ProFormText label={'任务名称'} required={true} width="md" name="name" />
        <ProFormText label={'crontab'} required={true} width="md" name="crontab" />
        <ProFormSelect
          label={'任务标识'}
          required={true}
          width="md"
          name="task"
          valueEnum={{
            'app.testcase.tasks.run_test_cases_by_project': 'project',
            'app.testcase.tasks.run_test_cases_by_dir': 'directory',
            'app.testcase.tasks.run_test_cases_by_plan': 'plan',
          }}
        />
        <ProFormText
          name="kwargs"
          label="任务参数"
          rules={[
            {
              required: true,
              message: '请输入任务参数',
            },
            {
              validator: async (_, value) => {
                try {
                  JSON.parse(value);
                } catch (e) {
                  throw new Error('输入的文本不是有效的JSON格式');
                }
              },
            },
          ]}
        />
        <ProFormSwitch name="enabled" label="启用状态" required={true} />
      </ModalForm>
    </>
  );
};
export default UpdateForm;
