import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  ModalForm,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message } from 'antd';
import { FC } from 'react';
import { ScheduledTasksCreateApi } from '../../service';
interface CreateFormProps {
  reload?: ActionType['reload'];
}
const CreateForm: FC<CreateFormProps> = (props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const { reload } = props;
  const { run, loading } = useRequest(ScheduledTasksCreateApi, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Added successfully');
      reload?.();
    },
    onError: () => {
      messageApi.error('Adding failed, please try again!');
    },
  });
  return (
    <>
      {contextHolder}
      <ModalForm
        title={'新建定时任务'}
        trigger={
          <Button type="primary" icon={<PlusOutlined />}>
            新建
          </Button>
        }
        width="400px"
        modalProps={{
          okButtonProps: {
            loading,
          },
        }}
        onFinish={async (value) => {
          await run({
            ...(value as API.ScheduledTask),
          });
          return true;
        }}
      >
        <ProFormText label={'任务名称'} required={true} width="md" name="name" />
        <ProFormText label={'crontab'} required={true} width="md" name="crontab" />
        <ProFormSelect
          label={'任务标识'}
          required={true}
          width="md"
          name="task"
          valueEnum={{
            'app.testcase.tasks.run_test_cases_by_project': 'project',
            'app.testcase.tasks.run_test_cases_by_dir': 'directory',
            'app.testcase.tasks.run_test_cases_by_plan': 'plan',
          }}
        />
        <ProFormText
          name="kwargs"
          label="任务参数"
          rules={[
            {
              required: true,
              message: '请输入任务参数',
            },
            {
              validator: async (_, value) => {
                try {
                  JSON.parse(value);
                } catch (e) {
                  throw new Error('输入的文本不是有效的JSON格式');
                }
              },
            },
          ]}
        />
        <ProFormSwitch name="enabled" label="启用状态" required={true} />
      </ModalForm>
    </>
  );
};
export default CreateForm;
