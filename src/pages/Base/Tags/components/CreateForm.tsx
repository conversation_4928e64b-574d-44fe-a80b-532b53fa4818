import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ModalForm, ProFormText } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message } from 'antd';
import { FC } from 'react';
import { TagCreateApi } from '../../service';
interface CreateFormProps {
  reload?: ActionType['reload'];
}
const CreateForm: FC<CreateFormProps> = (props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const { reload } = props;
  const { run, loading } = useRequest(TagCreateApi, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Added successfully');
      reload?.();
    },
    onError: () => {
      messageApi.error('Adding failed, please try again!');
    },
  });
  return (
    <>
      {contextHolder}
      <ModalForm
        title={'新建测试用例标签'}
        trigger={
          <Button type="primary" icon={<PlusOutlined />}>
            新建
          </Button>
        }
        width="400px"
        modalProps={{
          okButtonProps: {
            loading,
          },
        }}
        onFinish={async (value) => {
          await run({
            ...(value as API.Tag),
          });
          return true;
        }}
      >
        <ProFormText
          label={'标签名称'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="name"
        />
      </ModalForm>
    </>
  );
};
export default CreateForm;
