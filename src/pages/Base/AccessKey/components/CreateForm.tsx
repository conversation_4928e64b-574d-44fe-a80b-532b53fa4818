import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ModalForm, ProFormText } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message } from 'antd';
import { FC } from 'react';
import { AwsAccessKeyCreateApi } from '../../service';
interface CreateFormProps {
  reload?: ActionType['reload'];
}
const CreateForm: FC<CreateFormProps> = (props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const { reload } = props;
  const { run, loading } = useRequest(AwsAccessKeyCreateApi, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Added successfully');
      reload?.();
    },
    onError: () => {
      messageApi.error('Adding failed, please try again!');
    },
  });
  return (
    <>
      {contextHolder}
      <ModalForm
        title={'新建AccessKey'}
        trigger={
          <Button type="primary" icon={<PlusOutlined />}>
            新建
          </Button>
        }
        width="400px"
        modalProps={{
          okButtonProps: {
            loading,
          },
        }}
        onFinish={async (value) => {
          await run({
            ...(value as API.AwsAccessKey),
          });
          return true;
        }}
      >
        <ProFormText
          label={'环境标识'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="env"
        />
        <ProFormText label={'描述'} width="md" name="desc" />
        <ProFormText
          label={'账户 ID'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="account_id"
        />
        <ProFormText
          label={'AWS_ACCESS_KEY_ID'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="access_key_id"
        />
        <ProFormText
          label={'AWS_SECRET_ACCESS_KEY'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="secret_access_key"
        />
      </ModalForm>
    </>
  );
};
export default CreateForm;
