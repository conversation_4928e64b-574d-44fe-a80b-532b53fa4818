import { ModalForm, ProFormText } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message } from 'antd';
import React, { cloneElement, useCallback, useState } from 'react';
import { AwsAccessKeyUpdateApi } from '../../service';
export type UpdateFormProps = {
  trigger?: JSX.Element;
  onOk?: () => void;
  values: Partial<API.AwsAccessKey>;
};
const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { onOk, values, trigger } = props;
  const [open, setOpen] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const { run, loading } = useRequest(AwsAccessKeyUpdateApi, {
    manual: true,
    onSuccess: () => {
      messageApi.success('Configuration is successful');
      onOk?.();
    },
    onError: () => {
      messageApi.error('Configuration failed, please try again!');
    },
  });

  // const onCancel = useCallback(() => {
  //   setOpen(false);
  // }, []);

  const onOpen = useCallback(() => {
    setOpen(true);
  }, []);

  // const onFinish = useCallback(
  //   async (values?: any) => {
  //     await run({ data: values });

  //     onCancel();
  //   },
  //   [onCancel, run],
  // );

  return (
    <>
      {contextHolder}
      <ModalForm
        title={'修改覆盖率存储桶配置'}
        initialValues={values}
        trigger={
          trigger
            ? cloneElement(trigger, {
                onClick: onOpen,
                open: open,
              })
            : undefined
        }
        width="400px"
        modalProps={{
          okButtonProps: {
            loading,
          },
        }}
        onFinish={async (value) => {
          await run(
            {
              id: values.id,
            },
            {
              ...(value as Partial<API.AwsAccessKey>),
            },
          );
          return true;
        }}
      >
        <ProFormText
          label={'环境标识'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="env"
        />
        <ProFormText label={'描述'} width="md" name="desc" />
        <ProFormText
          label={'账户 ID'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="account_id"
        />
        <ProFormText
          label={'AWS_ACCESS_KEY_ID'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="access_key_id"
        />
        <ProFormText
          label={'AWS_SECRET_ACCESS_KEY'}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="secret_access_key"
        />
      </ModalForm>
    </>
  );
};
export default UpdateForm;
