declare namespace API {
  type KeyParams = {
    key: string;
  };

  type BaseFields = {
    created_by: string;
    updated_by: string;
    created_at: string;
    updated_at: string;
  };

  type EnvironmentList = {
    success: boolean;
    data: Environment[];
  };

  type Environment = API.BaseFields & {
    id: string;
    key: string;
    region: string;
  };

  type EnvironmentVariableList = {
    success: boolean;
    data: EnvironmentVariable[];
  };

  type EnvironmentVariable = {
    key: string;
    desc?: string;
    values: Record<string, any>;
  };

  type ScheduledTaskList = {
    success: boolean;
    data: ScheduledTask[];
  };

  type ScheduledTask = {
    id: number;
    name: number;
    crontab: string;
    task: string;
    kwargs: Record<string, any>;
    enabled?: boolean;
  };

  type TagList = {
    success: boolean;
    total?: number;
    data: Tag[];
  };

  type Tag = API.BaseFields & {
    id: string;
    name: string;
  };

  type AwsAccessKey = API.BaseFields & {
    id: string;
    env: string;
    desc: string;
    account_id: string;
    access_key_id?: string;
    secret_access_key?: string;
  };

  type AwsAccessKeyList = {
    success: boolean;
    total?: number;
    data: AwsAccessKey[];
  };
}
