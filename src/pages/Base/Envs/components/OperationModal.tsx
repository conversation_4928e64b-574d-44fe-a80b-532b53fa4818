import { AwsRegionEnum } from '@/constants';
import { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import type { FC } from 'react';
import useStyles from '../style.style';
type OperationModalProps = {
  done: boolean;
  open: boolean;
  current: API.Environment;
  onDone: () => void;
  onSubmit: (values: API.Environment) => void;
  children?: React.ReactNode;
};
const OperationModal: FC<OperationModalProps> = (props) => {
  const { styles } = useStyles();
  const { done, open, current, onDone, onSubmit, children } = props;
  if (!open) {
    return null;
  }
  return (
    <ModalForm<API.Environment>
      open={open}
      title={done ? null : `${current ? '编辑' : '新增'}环境`}
      className={styles.standardListForm}
      width={640}
      onFinish={async (values) => {
        onSubmit(values);
        // 删除了提交后的 Result, 这里直接完成
        onDone();
      }}
      initialValues={current}
      submitter={{
        render: (_, dom) => (done ? null : dom),
      }}
      trigger={<>{children}</>}
      modalProps={{
        onCancel: () => onDone(),
        destroyOnClose: true,
        styles: {
          body: done ? { padding: '72px 0' } : {},
        },
      }}
    >
      {!done ? (
        <>
          <ProFormText name="id" hidden />
          <ProFormText
            name="key"
            label="环境标识"
            rules={[
              {
                required: true,
                message: '请输入环境环境标识',
              },
            ]}
            placeholder="请输入"
          />
          <ProFormSelect name="region" label="区域" required={true} valueEnum={AwsRegionEnum} />
        </>
      ) : (
        <></>
      )}
    </ModalForm>
  );
};
export default OperationModal;
