import { DownOutlined, PlusOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Avatar, Button, Card, Dropdown, Input, List, Modal, PaginationProps, message } from 'antd';
import dayjs from 'dayjs';
import type { FC } from 'react';
import React, { useEffect, useState } from 'react';
import { EnvCreateApi, EnvDeleteApi, EnvListApi, EnvUpdateApi } from '../service';
import OperationModal from './components/OperationModal';
import useStyles from './style.style';
const { Search } = Input;
const ListContent = ({ data: { created_at } }: { data: API.Environment }) => {
  const { styles } = useStyles();
  return (
    <div>
      {/* <div className={styles.listContentItem}>
        <span>Owner</span>
        <p>{owner}</p>
       </div> */}
      <div className={styles.listContentItem}>
        <span>创建时间</span>
        <p>{dayjs(created_at).format('YYYY-MM-DD HH:mm:ss')}</p>
      </div>
      {/* <div className={styles.listContentItem}></div> */}
    </div>
  );
};
export const Envs: FC = () => {
  const { styles } = useStyles();
  const [done, setDone] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const [pageSize, setPageSize] = useState<number>(5);
  const [current, setCurrent] = useState<API.Environment>();
  const [listData, setListData] = useState([]);
  const {
    run: fetchList,
    loading,
    // mutate,
  } = useRequest(
    () => {
      return EnvListApi({
        pageSize: 50,
      });
    },
    {
      manual: true,
      onSuccess: (result) => {
        setListData(result);
      },
    },
  );
  useEffect(() => {
    fetchList();
  }, []);
  const postRun = async (method: string, params: Partial<API.Environment>) => {
    const hide = message.loading('开始处理');
    try {
      if (method === 'remove') {
        await EnvDeleteApi(params);
      } else if (method === 'update') {
        await EnvUpdateApi(params, params);
      } else {
        await EnvCreateApi(params);
      }
      hide();
      message.success('操作成功');
      fetchList();
    } catch (error) {
      hide();
      message.error('操作失败，请重试！');
    }
  };
  const onShowSizeChange: PaginationProps['onShowSizeChange'] = (current, pageSize) => {
    setPageSize(pageSize);
  };
  const paginationProps = {
    showSizeChanger: true,
    showQuickJumper: false,
    pageSize: pageSize,
    total: listData?.length,
    onShowSizeChange: onShowSizeChange,
  };
  const showEditModal = (item: API.Environment) => {
    setOpen(true);
    setCurrent(item);
  };
  const deleteItem = (id: string) => {
    postRun('remove', {
      id: id,
    });
  };
  const editAndDelete = (key: string | number, currentItem: API.Environment) => {
    if (key === 'edit') showEditModal(currentItem);
    else if (key === 'delete') {
      Modal.confirm({
        title: '删除环境',
        content: '确定删除该环境吗？',
        okText: '确认',
        cancelText: '取消',
        onOk: () => deleteItem(currentItem.id),
      });
    }
  };
  const extraContent = (
    <div>
      <Search className={styles.extraContentSearch} placeholder="请输入" onSearch={() => ({})} />
    </div>
  );
  const MoreBtn: React.FC<{
    item: API.Environment;
  }> = ({ item }) => (
    <Dropdown
      menu={{
        onClick: ({ key }) => editAndDelete(key, item),
        items: [
          {
            key: 'delete',
            label: '删除',
          },
        ],
      }}
    >
      <a>
        更多 <DownOutlined />
      </a>
    </Dropdown>
  );
  const handleDone = () => {
    setDone(false);
    setOpen(false);
    setCurrent(undefined);
  };
  const handleSubmit = (values: API.Environment) => {
    const method = values?.id ? 'update' : 'add';
    postRun(method, values);
  };
  return (
    <div>
      <PageContainer title={false} breadcrumbRender={false}>
        <div className={styles.standardList}>
          <Card
            className={styles.listCard}
            bordered={false}
            title={`${'环境列表'}`}
            style={{
              marginTop: 24,
            }}
            styles={{
              body: {
                padding: '0 32px 40px 32px',
              },
            }}
            extra={extraContent}
          >
            <List
              size="large"
              rowKey="id"
              loading={loading}
              pagination={paginationProps}
              dataSource={listData}
              renderItem={(item: API.Environment) => (
                <List.Item
                  actions={[
                    <a
                      key="edit"
                      onClick={(e) => {
                        e.preventDefault();
                        showEditModal(item);
                      }}
                    >
                      编辑
                    </a>,
                    <MoreBtn key="more" item={item} />,
                  ]}
                >
                  <List.Item.Meta
                    avatar={
                      <Avatar shape="square" size="large">
                        {item.key.toUpperCase()}
                      </Avatar>
                    }
                    title={`环境标识: ${item.key}`}
                    description={`区域: ${item.region}`}
                  />
                  <ListContent data={item} />
                </List.Item>
              )}
            />
          </Card>
        </div>
        <Button
          type="dashed"
          onClick={() => {
            setOpen(true);
          }}
          style={{
            width: '100%',
            marginBottom: 8,
          }}
        >
          <PlusOutlined />
          新增
        </Button>
        <OperationModal
          done={done}
          open={open}
          current={current}
          onDone={handleDone}
          onSubmit={handleSubmit}
        />
      </PageContainer>
    </div>
  );
};
export default Envs;
