import {
  ActionType,
  ModalForm,
  PageContainer,
  ProColumns,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, Modal, message } from 'antd';
import type { FC } from 'react';
import { useEffect, useRef, useState } from 'react';
import { EnvListApi, VarCreateApi, VarDeleteApi, VarListApi, VarUpdateApi } from '../service';
interface Environment {
  id: string;
  name: string;
}
interface Variable {
  key: string;
  name: string;
  [environment: string]: string;
}
export const Vars: FC = () => {
  const [done, setDone] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const [current, setCurrent] = useState<Variable>();
  const [environments, setEnvironments] = useState<Environment[]>([]);
  const actionRef = useRef<ActionType>();
  const { run: fetchEnvList } = useRequest(
    () => {
      return EnvListApi({
        pageSize: 100,
      });
    },
    {
      manual: true,
      onSuccess: (result) => {
        const envs = result.map((item) => {
          return {
            id: item.id,
            name: `${item.key}(${item.region})`,
          };
        });
        setEnvironments(envs);
      },
    },
  );
  useEffect(() => {
    fetchEnvList();
  }, []);
  const handleDone = () => {
    setDone(false);
    setOpen(false);
    setCurrent(undefined);
  };
  const postRun = async (method: string, params: Partial<API.EnvironmentVariable>) => {
    const hide = message.loading('开始处理');
    console.debug(params);
    try {
      if (method === 'remove') {
        await VarDeleteApi(params);
      } else if (method === 'update') {
        await VarUpdateApi(params, params);
      } else {
        await VarCreateApi(params);
      }
      hide();
      message.success('操作成功');
      handleDone();
      fetchEnvList();
      actionRef.current?.reload?.();
    } catch (error) {
      hide();
      message.error('操作失败，请重试！');
    }
  };
  const showEditModal = (item: Variable) => {
    setOpen(true);
    setCurrent(item);
  };
  const deleteItem = (key: string) => {
    postRun('remove', {
      key: key,
    });
  };
  const handleDelete = (currentItem: Variable) => {
    Modal.confirm({
      title: '删除变量',
      content: '确定删除该变量吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => deleteItem(currentItem.key),
    });
  };
  const columns: ProColumns[] = [
    {
      title: '变量名',
      dataIndex: 'key',
      fixed: 'left',
      width: '200px',
    },
    {
      title: '描述说明',
      dataIndex: 'desc',
      fixed: 'left',
      width: '100px',
    },
    ...environments.map((env) => ({
      title: env.name,
      dataIndex: ['values', env.id],
      key: env.id,
    })),
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (text, record) => [
        <Button
          key="edit"
          onClick={() => {
            showEditModal(record);
          }}
        >
          编辑
        </Button>,
        <Button
          key="delete"
          onClick={() => {
            handleDelete(record);
          }}
        >
          删除
        </Button>,
      ],
    },
  ];
  const handleSubmit = (values: Variable) => {
    const method = current ? 'update' : 'add';
    postRun(method, values);
  };
  return (
    <div>
      <PageContainer title={false} breadcrumbRender={false} contentWidth="100vw">
        <ProTable<API.EnvironmentVariable>
          scroll={{
            x: '100',
          }}
          columns={columns}
          request={VarListApi}
          actionRef={actionRef}
          rowKey="key"
          search={false}
          dateFormatter="string"
          headerTitle={`${'变量列表'}`}
          toolBarRender={() => [
            <Button
              key="button"
              type="primary"
              onClick={() => {
                // 打开添加变量的模态框
                setOpen(true);
              }}
            >
              新增变量
            </Button>,
          ]}
        />
      </PageContainer>
      <ModalForm
        open={open}
        title={`${current ? '编辑' : '新增'}变量`}
        onFinish={async (values) => {
          handleSubmit(values);
        }}
        initialValues={current}
        modalProps={{
          onCancel: () => handleDone(),
          destroyOnClose: true,
        }}
      >
        {!done ? (
          <>
            <ProFormText
              name="key"
              label="变量名"
              rules={[
                {
                  required: true,
                  message: '请输入变量名',
                },
              ]}
              placeholder="请输入"
            />
            <ProFormText
              name="desc"
              label="描述"
              rules={[
                {
                  // required: true,
                  message: '请输入描述',
                },
              ]}
              placeholder="请输入"
            />
            {environments.map((env) => (
              <ProFormText
                name={['values', `${env.id}`.toString()]}
                key={env.id}
                label={`环境(${env.name})的值`}
                placeholder="请输入"
              />
            ))}
          </>
        ) : (
          <></>
        )}
      </ModalForm>
    </div>
  );
};
export default Vars;
