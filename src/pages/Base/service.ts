import { request } from '@umijs/max';

/** 获取环境列表 GET /api/env */
export async function EnvListApi(params: API.Pagination, options?: { [key: string]: any }) {
  return request<API.EnvironmentList>('/api/env', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 新增环境 POST /api/env */
export async function EnvCreateApi(body: API.Environment, options?: { [key: string]: any }) {
  return request<API.Environment>('/api/env', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除环境 DELETE /api/env */
export async function EnvBatchDeleteApi(body: API.IdsParam, options?: { [key: string]: any }) {
  return request<any>('/api/env', {
    method: 'DELETE',
    data: body,
    ...(options || {}),
  });
}

/** 获取单个环境 GET /api/env/${param0} */
export async function EnvDetailApi(params: API.IdParam, options?: { [key: string]: any }) {
  const { id: param0, ...queryParams } = params;
  return request<API.Environment>(`/api/env/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改单个环境 PUT /api/env/${param0} */
export async function EnvUpdateApi(
  params: API.IdParam,
  body: API.Environment,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Environment>(`/api/env/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除单个环境 DELETE /api/env/${param0} */
export async function EnvDeleteApi(params: API.IdParam, options?: { [key: string]: any }) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/api/env/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取变量列表 GET /api/var */
export async function VarListApi(options?: { [key: string]: any }) {
  return request<API.EnvironmentVariableList>('/api/var', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 修改变量 PUT /api/var */
export async function VarUpdateApi(
  body: API.EnvironmentVariable,
  options?: { [key: string]: any },
) {
  return request<any>('/api/var', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增变量 POST /api/var */
export async function VarCreateApi(
  body: API.EnvironmentVariable,
  options?: { [key: string]: any },
) {
  return request<any>('/api/var', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除指定变量 DELETE /api/var/${key} */
export async function VarDeleteApi(params: API.KeyParams, options?: { [key: string]: any }) {
  const { key: key, ...queryParams } = params;
  return request<any>(`/api/var/${key}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取定时任务列表 GET /api/scheduled-tasks */
export async function ScheduledTasksListApi(
  params: API.Pagination,
  options?: { [key: string]: any },
) {
  return request<API.ScheduledTaskList>('/api/scheduled-tasks', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 新增定时任务 POST /api/scheduled-tasks */
export async function ScheduledTasksCreateApi(
  body: API.ScheduledTask,
  options?: { [key: string]: any },
) {
  return request<API.ScheduledTask>('/api/scheduled-tasks', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除定时任务 DELETE /api/scheduled-tasks */
export async function ScheduledTasksBatchDeleteApi(
  body: API.IdsParam,
  options?: { [key: string]: any },
) {
  return request<any>('/api/scheduled-tasks', {
    method: 'DELETE',
    data: body,
    ...(options || {}),
  });
}

/** 获取单个定时任务 GET /api/scheduled-tasks/${param0}/ */
export async function ScheduledTasksDetailApi(
  params: API.IdParam,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ScheduledTask>(`/api/scheduled-tasks/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改单个定时任务 PUT /api/scheduled-tasks/${param0}/ */
export async function ScheduledTasksUpdateApi(
  params: API.IdParam,
  body: API.ScheduledTask,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ScheduledTask>(`/api/scheduled-tasks/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除单个定时任务 DELETE /api/scheduled-tasks/${param0}/ */
export async function ScheduledTasksDeleteApi(
  params: API.IdParam,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/api/scheduled-tasks/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取测试用例标签列表 GET /api/tag */
export async function TagListApi(params: API.Pagination, options?: { [key: string]: any }) {
  return request<API.TagList>('/api/tag', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 新增测试用例标签 POST /api/tag */
export async function TagCreateApi(body: API.Tag, options?: { [key: string]: any }) {
  return request<API.Tag>('/api/tag', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除测试用例标签 DELETE /api/tag */
export async function TagBatchDeleteApi(body: API.IdsParam, options?: { [key: string]: any }) {
  return request<any>('/api/tag', {
    method: 'DELETE',
    data: body,
    ...(options || {}),
  });
}

/** 获取单个测试用例标签 GET /api/tag/${param0} */
export async function TagDetailApi(params: API.IdParam, options?: { [key: string]: any }) {
  const { id: param0, ...queryParams } = params;
  return request<API.Tag>(`/api/tag/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改单个测试用例标签 PUT /api/tag/${param0} */
export async function TagUpdateApi(
  params: API.IdParam,
  body: API.Tag,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Tag>(`/api/tag/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除单个测试用例标签 DELETE /api/tag/${param0} */
export async function TagDeleteApi(params: API.IdParam, options?: { [key: string]: any }) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/api/tag/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取 AwsAccessKey 列表 GET /api/access_key */
export async function AwsAccessKeyListApi(
  params: API.Pagination,
  options?: { [key: string]: any },
) {
  return request<API.AwsAccessKeyList>('/api/access_key', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 新增 AwsAccessKey POST /api/access_key */
export async function AwsAccessKeyCreateApi(
  body: API.AwsAccessKey,
  options?: { [key: string]: any },
) {
  return request<API.AwsAccessKey>('/api/access_key', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除 AwsAccessKey DELETE /api/access_key */
export async function AwsAccessKeyBatchDeleteApi(
  body: API.IdsParam,
  options?: { [key: string]: any },
) {
  return request<any>('/api/access_key', {
    method: 'DELETE',
    data: body,
    ...(options || {}),
  });
}

/** 获取单个 AwsAccessKey GET /api/access_key/${param0} */
export async function AwsAccessKeyDetailApi(params: API.IdParam, options?: { [key: string]: any }) {
  const { id: param0, ...queryParams } = params;
  return request<API.AwsAccessKey>(`/api/access_key/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改单个 AwsAccessKey PUT /api/access_key/${param0} */
export async function AwsAccessKeyUpdateApi(
  params: API.IdParam,
  body: API.AwsAccessKey,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.AwsAccessKey>(`/api/access_key/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除单个 AwsAccessKey DELETE /api/access_key/${param0} */
export async function AwsAccessKeyDeleteApi(params: API.IdParam, options?: { [key: string]: any }) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/api/access_key/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}
