import type { DvaModel, Effect, Reducer } from '@umijs/max';

interface state {
  project_id: Record<string, never>;
  directory_id: Record<string, never>;
  env_id: Record<string, never>;
}
interface reducers {
  save: Reducer<state>;
}
interface effects {
  fetchTags: Effect;
}

const commonModel: DvaModel<state, reducers, effects> = {
  namespace: 'common',
  state: {
    project_id: null,
    directory_id: null,
    env_id: null,
  },
  reducers: {
    save(state: state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
  effects: {
    *syncUrlParams({ payload }, { put }) {
      yield put({ type: 'updateState', payload });
      const { project_id, directory_id, env_id } = payload;
      const searchParams = new URLSearchParams(window.location.search);
      if (project_id) {
        searchParams.set('project_id', project_id);
      }
      if (directory_id) {
        searchParams.set('directory_id', directory_id);
      }
      if (env_id) {
        searchParams.set('env_id', env_id);
      }
      window.history.pushState(null, '', `${window.location.pathname}?${searchParams.toString()}`);
    },
  },
};
export default commonModel;
