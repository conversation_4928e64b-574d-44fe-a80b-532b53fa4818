import { TagListApi } from '@/pages/Base/service';

import type { DvaModel, Effect, Reducer } from '@umijs/max';

interface state {
  tags: Record<string, never>;
}
interface reducers {
  save: Reducer<state>;
}
interface effects {
  fetchTags: Effect;
}

const baseModel: DvaModel<state, reducers, effects> = {
  namespace: 'base',
  state: {
    tags: {},
  },
  reducers: {
    save(state: state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
  effects: {
    *fetchTags({ payload }, { call, put }) {
      try {
        const res = yield call(TagListApi, payload);
        const tags = res.data.reduce((obj, item) => {
          obj[item.id] = { label: item.name, value: item.id };
          return obj;
        }, {});
        yield put({
          type: 'save',
          payload: {
            tags: tags,
          },
        });
        return true;
      } catch (error) {
        return false;
      }
    },
  },
};
export default baseModel;
