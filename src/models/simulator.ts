import {
  apiSimulateAliasTypeRetrieve,
  apiSimulateDeviceList,
  apiSimulateDeviceRetrieve,
  apiSimulateProductRetrieve,
  apiSimulateSupportStatusRetrieve,
} from '@/pages/Simulator/service';

import type { DvaModel, Effect, Reducer } from '@umijs/max';

interface state {
  productTypes: any[];
  aliasTypes: any[];
  devices: any[];
  currentDevice: {};
  commandFields: any[];
}
interface reducers {
  save: Reducer<state>;
}
interface effects {
  fetchProductList: Effect;
  fetchAliasType: Effect;
  fetchDeviceList: Effect;
  fetchCurrentDeviceInfo: Effect;
  fetchCurrentCommandList: Effect;
}

const simulatorModel: DvaModel<state, reducers, effects> = {
  namespace: 'simulator',
  state: {
    productTypes: [],
    aliasTypes: [],
    devices: [],
    currentDevice: {},
    commandFields: [],
  },
  reducers: {
    save(state: state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
  effects: {
    *fetchProductList({ payload }, { call, put }) {
      try {
        const res = yield call(apiSimulateProductRetrieve, payload);
        const products = res.data.map((product_name) => {
          return {
            label: product_name,
            value: product_name,
          };
        });
        yield put({
          type: 'save',
          payload: {
            productType: products,
          },
        });
        return true;
      } catch (error) {
        return false;
      }
    },
    *fetchAliasType({ payload }, { call, put }) {
      try {
        const res = yield call(apiSimulateAliasTypeRetrieve, payload);
        const aliasTypes = res.data.map((aliasType) => {
          return {
            label: aliasType,
            value: aliasType,
          };
        });
        yield put({
          type: 'save',
          payload: {
            aliasTypes: aliasTypes,
          },
        });
        return true;
      } catch (error) {
        return false;
      }
    },
    *fetchDeviceList({ payload }, { call, put }) {
      try {
        const res = yield call(apiSimulateDeviceList, payload);
        const devices = res.data.map((device) => {
          return {
            mac: device.mac,
            type: device.type,
          };
        });
        yield put({
          type: 'save',
          payload: {
            devices: devices,
          },
        });
        return true;
      } catch (error) {
        return false;
      }
    },
    *fetchCurrentDeviceInfo({ params, payload }, { call, put }) {
      try {
        const res = yield call(apiSimulateDeviceRetrieve, params, payload);
        if (res.data) {
          yield put({
            type: 'save',
            payload: {
              currentDevice: res.data,
            },
          });
        }
        return true;
      } catch (error) {
        return false;
      }
    },
    *fetchCurrentCommandList({ params }, { call, put }) {
      try {
        const res = yield call(apiSimulateSupportStatusRetrieve, params);
        console.log(JSON.stringify(res.data));
        if (res.data) {
          yield put({
            type: 'save',
            payload: {
              commandFields: res.data,
            },
          });
        }
        return true;
      } catch (error) {
        return false;
      }
    },
  },
};
export default simulatorModel;
