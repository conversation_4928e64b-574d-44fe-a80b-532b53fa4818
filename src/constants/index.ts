// export enum SimulatorTypeEnum {
//     SweeperOrigin = 'Floor Cleaning Robot S10',
//     Hub2 = 'Hub 2',
// }

export enum SimulatorProductEnum {
  'Floor Cleaning Robot S10' = 'Floor Cleaning Robot S10',
  'Hub 2' = 'Hub 2',
}
export enum SimulatorRegionEnum {
  'us-east-1' = 'us-east-1',
  'ap-northeast-1' = 'ap-northeast-1',
  'eu-northeast-1' = 'eu-northeast-1',
}
export enum SimulatorStatusEnum {
  on = 'on',
  off = 'off',
}

export enum SimulatorEnvEnum {
  'test' = '测试环境',
  'prod' = '生产环境',
}

export enum TestResult {
  PASS = 0,
  FAIL = 1,
  INTERRUPTED = 2,
  INTERNAL_ERROR = 3,
  USAGE_ERROR = 4,
  NO_TESTS_COLLECTED = 5,
}

export enum AwsRegionEnum {
  'us-east-1' = 'us-east-1',
  'ap-northeast-1' = 'ap-northeast-1',
  'eu-central-1' = 'eu-central-1',
  'cn-north-1' = 'cn-north-1',
}
