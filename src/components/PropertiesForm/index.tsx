import { apiSimulateCommandCreate } from '@/pages/Simulator/service';
import {
  ProForm,
  ProFormDigit,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@ant-design/pro-form';
import { useRequest } from '@umijs/max';
import { connect } from '@umijs/plugins/libs/dva';
import { message } from 'antd';
import { useEffect, useState } from 'react';
export type PropertiesFormProps = {
  // fields: Array<Record<string, any>>;
  type: string;
  dispatch?: any;
  simulator?: any;
};

const PropertiesForm = (props: PropertiesFormProps) => {
  const { type, dispatch, simulator } = props;
  const [dynamicFormType, setDynamicFormType] = useState({});
  const [messageApi, contextHolder] = message.useMessage();
  const [groups, setGroups] = useState([
    { id: 0, selectValue: 'inOfflineDetect', textValue: 'true' },
    // ... 可以有更多的初始组
  ]);

  const handlePropertySelectChange = (value, groupId) => {
    if (groupId >= 0 && groupId < groups.length) {
      setGroups(
        groups.map((group) => {
          // 存在这个group时候更新
          if (group.id === groupId) {
            return { ...group, selectValue: value };
          }
          return group;
        }),
      );
    } else {
      setGroups([...groups, { id: groupId, selectValue: value, textValue: '' }]);
    }
  };

  const handlePropertyValueChange = (value, groupId) => {
    setGroups(
      groups.map((group) => {
        if (group.id === groupId) {
          return { ...group, textValue: value };
        }
        return group;
      }),
    );
  };

  useEffect(() => {
    if (type !== undefined) {
      dispatch({
        type: 'simulator/fetchCurrentCommandList',
        params: {
          alias: type,
        },
      });
    }
  }, [type]);

  const { run, loading } = useRequest(apiSimulateCommandCreate, {
    manual: true,
    onSuccess: (data) => {
      messageApi.success(data);
    },
    onError: () => {},
  });

  return (
    <>
      {contextHolder}
      <ProForm
        submitter={{
          searchConfig: {
            submitText: '上报',
            resetText: '重置',
          },
        }}
        onFinish={async (values) => {
          // {props: [{onlineStatus: 1}]}
          console.log(JSON.stringify(values)); // FIXME: {"props":[{"inOfflineDetect":"true"},{"syncProperty":"","inOfflineDetect":"","onlineStatus":0}]}
          // [{"batteryStatus":"true"}]
          console.log(
            JSON.stringify(
              groups.map((item) => {
                return {
                  [item.selectValue]: item.textValue,
                };
              }),
            ),
          );
          run({
            mac: simulator.currentDevice.mac,
            props: values.props,
          });
        }}
      >
        <ProFormList
          name="props"
          itemRender={({ listDom, action }) => {
            // TODO: copy 按钮复制后，表单有问题
            return (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{ flex: 1 }}>{listDom}</div>
                <div>{action}</div>
              </div>
            );
          }}
        >
          {(field, index, action) => {
            // console.log(field, index);
            console.log(simulator.commandFields[groups[index]?.selectValue]?.type === 'string');
            return (
              <ProFormGroup>
                <ProFormSelect
                  label="属性"
                  options={Object.keys(simulator.commandFields).map((key) => {
                    // 所有key 作为属性列表，看选择哪个属性
                    return { label: key, value: key };
                  })}
                  // value={group.selectValue}
                  onChange={(value) => handlePropertySelectChange(value, index)}
                />
                {simulator.commandFields[groups[index]?.selectValue]?.type === 'bool' ? (
                  <ProFormSwitch
                    label="属性值"
                    name={groups[index]?.selectValue || null}
                    initialValue={groups[index]?.textValue}
                    transform={(value) => !!value} // 转换为bool类型
                    // onChange={(value) => handlePropertyValueChange(value.target.value, 1)}
                  />
                ) : simulator.commandFields[groups[index]?.selectValue]?.type === 'int' ? (
                  <ProFormDigit
                    label="属性值"
                    name={groups[index]?.selectValue || null}
                    initialValue={groups[index]?.textValue}
                    min={simulator.commandFields[groups[index]?.selectValue]?.min}
                    max={simulator.commandFields[groups[index]?.selectValue]?.max}
                    // onChange={(value) => handlePropertyValueChange(value.target.value, 1)}
                  />
                ) : simulator.commandFields[groups[index]?.selectValue]?.type === 'enum' ? (
                  <ProFormSelect
                    label="属性值"
                    name={groups[index]?.selectValue || null}
                    options={simulator.commandFields[groups[index]?.selectValue]?.options}
                  />
                ) : (
                  // undefined string array
                  <ProFormText
                    label="属性值"
                    name={groups[index]?.selectValue || null}
                    initialValue={groups[index]?.textValue}
                    // onChange={(value) => handlePropertyValueChange(value.target.value, 1)}
                  />
                )}
              </ProFormGroup>
            );
          }}
        </ProFormList>
      </ProForm>
    </>
  );
};
export default connect((props) => props)(PropertiesForm);
