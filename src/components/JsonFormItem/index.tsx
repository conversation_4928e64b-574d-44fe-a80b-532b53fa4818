import { ProForm, ProFormTextArea } from '@ant-design/pro-form';
const JsonInputForm = () => {
  const onFinish = (values) => {
    try {
      const jsonValue = JSON.parse(values.jsonInput);
      console.log(jsonValue);
      // 在这里处理解析后的 JSON
    } catch (error) {
      console.error('Invalid JSON', error);
      // 在这里处理 JSON 解析错误
    }
  };
  return (
    <ProForm onFinish={onFinish}>
      <ProFormTextArea name="jsonInput" label="JSON Input" />
    </ProForm>
  );
};
export default JsonInputForm;
