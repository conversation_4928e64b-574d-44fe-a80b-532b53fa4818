FROM python:3.13-slim AS builder
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources && \
	apt-get update && apt-get install -y --no-install-recommends \
    libssl-dev libffi-dev build-essential && \
    rm -rf /var/lib/apt/lists/*
WORKDIR /data

ENV UV_COMPILE_BYTECODE=1 \
    UV_NO_CACHE=1 \
    UV_LINK_MODE=copy \
    UV_PROJECT_ENVIRONMENT=/data/runner_venv
# Install dependencies
RUN --mount=type=cache,target=/root/.cache/uv \
    --mount=type=bind,source=uv.lock,target=uv.lock \
	--mount=type=bind,source=pyproject.toml,target=pyproject.toml \
	uv sync --frozen --no-install-project --no-editable --no-dev

# Copy the project into the intermediate image
ADD . /data

# Sync and install the project
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen --no-editable --no-dev


FROM python:3.13-slim

# Copy the environment, but not the source code
COPY --from=builder /data/runner_venv /data/runner_venv
