# FROM python:3.13-slim AS builder
# COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/
FROM ghcr.io/astral-sh/uv:python3.13-bookworm-slim AS builder

RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources && \
	apt-get update && apt-get install -y --no-install-recommends \
    libpq-dev && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /data

ENV UV_COMPILE_BYTECODE=1 \
    UV_NO_CACHE=1 \
    UV_LINK_MODE=copy \
    UV_PROJECT_ENVIRONMENT=/usr/local
# Install dependencies
RUN --mount=type=cache,target=/root/.cache/uv \
    --mount=type=bind,source=uv.lock,target=uv.lock \
    --mount=type=bind,source=pyproject.toml,target=pyproject.toml \
	uv sync --frozen --no-install-project --group prod --no-dev


FROM python:3.13-slim
COPY --from=builder /usr/local /usr/local
# httprunner pydantic v1 python:3.12.3-slim


COPY *.sh /
RUN chmod a+x /*.sh && \
	sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources && \
    apt-get update && apt-get install -y --no-install-recommends procps telnet iproute2 dnsutils \
	git openssh-client vim \
    libpq-dev && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /data/autoswitchbot
COPY . .

CMD ["python", "main.py"]
