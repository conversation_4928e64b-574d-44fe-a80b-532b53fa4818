development: # 本地运行
  DEBUG: True
  FRONTEND_HOST: http://localhost:8001
  ALLOWED_HOSTS:
    - "*"
  DATABASES:
    default:
      ENGINE: django.db.backends.postgresql
      NAME: autotest
      USER: postgres
      PASSWORD: 123456
      HOST: localhost
      PORT: 5432
      TEST:
        SERIALIZE: False
        #        MIRROR: default
        NAME: test_autotest
        MIGRATE: True # default true
        IMPORT_DATA: True
  SOCIALACCOUNT_PROVIDERS:
    feishu:
      APP:
        client_id: cli_a598ffbb89bb900c
        secret: AGz4JJSrc3sMI3vBlKJJOc7b53GVnPtN
  REST_AUTH:
    USER_DETAILS_SERIALIZER: app.accounts.serializers.UserSerializer
  EMAIL_BACKEND: django.core.mail.backends.console.EmailBackend
  CACHES:
    default:
      BACKEND: django.core.cache.backends.redis.RedisCache
      LOCATION: redis://localhost:6379
  CELERY_BROKER_URL: redis://localhost:6379/1
  CELERY_RESULT_BACKEND: redis://localhost:6379/1
  SESSION_ENGINE: django.contrib.sessions.backends.cache
  SESSION_COOKIE_NAME: autoswitchbot_dev_session_id
  CHANNEL_LAYERS:
    default:
      BACKEND: channels_redis.core.RedisChannelLayer
      CONFIG:
        hosts:
          - - localhost
            - 6379
  PUSH_GATEWAY_URL: http://localhost:9091

docker: # docker-dev
  DEBUG: True
  FRONTEND_HOST: http://localhost:8001
  ALLOWED_HOSTS:
    - "*"
  DATABASES:
    default:
      ENGINE: django.db.backends.postgresql
      NAME: autotest
      USER: postgres
      PASSWORD: 123456
      HOST: pg
      PORT: 5432
  SOCIALACCOUNT_PROVIDERS:
    feishu:
      APP:
        client_id: cli_a598ffbb89bb900c
        secret: AGz4JJSrc3sMI3vBlKJJOc7b53GVnPtN
  REST_AUTH:
    USER_DETAILS_SERIALIZER: app.accounts.serializers.UserSerializer
  EMAIL_BACKEND: django.core.mail.backends.console.EmailBackend
  CACHES:
    default:
      BACKEND: django.core.cache.backends.redis.RedisCache
      LOCATION: redis://redis:6379
  CELERY_BROKER_URL: redis://redis:6379/1
  CELERY_RESULT_BACKEND: redis://redis:6379/1
  SESSION_ENGINE: django.contrib.sessions.backends.cache
  SESSION_COOKIE_NAME: autoswitchbot_dev_session_id
  CHANNEL_LAYERS:
    default:
      BACKEND: channels_redis.core.RedisChannelLayer
      CONFIG:
        hosts:
          - - redis
            - 6379
  # Amazon S3 settings
  AWS_ACCESS_KEY_ID: ********************
  AWS_SECRET_ACCESS_KEY: 9ZQYGhnAk/2I5DYLzMwFM19HUXoHkdmW6bqCaTvQ
  PUSH_GATEWAY_URL: http://localhost:9091

production:
  FRONTEND_HOST: https://test.switchbot.work
  ALLOWED_HOSTS:
    - "*"
  DATABASES:
    default:
      ENGINE: django.db.backends.postgresql
      NAME: autotest
      USER: autotest
      PASSWORD: uFdF8lwV2LB5
      HOST: postgresql-pub-postgresql-ha-pgpool.postgresql-pub
      PORT: 5432
      OPTIONS:
        pool:
          min_size: 2
          max_size: 10
          timeout: 10
  SOCIALACCOUNT_PROVIDERS:
    feishu:
      APP:
        client_id: cli_a598ffbb89bb900c
        secret: AGz4JJSrc3sMI3vBlKJJOc7b53GVnPtN
  REST_AUTH:
    USER_DETAILS_SERIALIZER: app.accounts.serializers.UserSerializer
  EMAIL_BACKEND: django.core.mail.backends.console.EmailBackend
  CACHES:
    default:
      BACKEND: django.core.cache.backends.redis.RedisCache
      LOCATION: redis://:switchbot123456@autotest-redis-master:6379
  CELERY_BROKER_URL: redis://:switchbot123456@autotest-redis-master:6379/1
  CELERY_RESULT_BACKEND: redis://:switchbot123456@autotest-redis-master:6379/1
  SESSION_ENGINE: django.contrib.sessions.backends.cache
  SESSION_COOKIE_NAME: autoswitchbot_session_id
  CHANNEL_LAYERS:
    default:
      BACKEND: channels_redis.core.RedisChannelLayer
      CONFIG:
        hosts:
          - redis://:switchbot123456@autotest-redis-master:6379/1
  # Amazon S3 settings
  AWS_ACCESS_KEY_ID: ********************
  AWS_SECRET_ACCESS_KEY: 9ZQYGhnAk/2I5DYLzMwFM19HUXoHkdmW6bqCaTvQ
  PUSH_GATEWAY_URL: http://prometheus-pushgateway.monitor.svc.cluster.local:9091
  CELERY_WORKER_POOL: threads
  CELERY_WORKER_MAX_TASKS_PER_CHILD: 2
  CELERY_WORKER_CONCURRENCY: 1
  CELERY_WORKER_HIJACK_ROOT_LOGGER: true
