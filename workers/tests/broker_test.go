package tests

import (
	"autoswitchbot/common"
	"autoswitchbot/internal/tasks"
	"autoswitchbot/util"
	"testing"

	"github.com/hibiken/asynq"
)

func TestBrokerAPI(t *testing.T) {
	config := common.LoadConfig(common.GetFilePath())
	client := asynq.NewClient(util.FixedRedisClientOpt{
		Addr:     config.Redis.Addr,
		Password: config.Redis.Password,
		DB:       config.Redis.DB,
	})
	defer client.Close()

	task, err := tasks.NewBrokerApiCheckTask("http://k8s-emqx-39a22a1c56-1730912896.us-east-1.elb.amazonaws.com", "2e04c978070fb975", "fVlkRAPMMBXXwb9BQUyz0gPeRZBkPUwJDNqPfM4gwlxM")
	if err != nil {
		t.Fatalf("could not create task: %v", err)
	}
	info, err := client.Enqueue(task, asynq.Queue("at_high"))
	if err != nil {
		t.Fatalf("could not enqueue task: %v", err)
	}
	t.Logf("enqueued task: id=%s queue=%s", info.ID, info.Queue)
}

func TestBrokerProxy(t *testing.T) {
	config := common.LoadConfig(common.GetFilePath())
	client := asynq.NewClient(util.FixedRedisClientOpt{
		Addr:     config.Redis.Addr,
		Password: config.Redis.Password,
		DB:       config.Redis.DB,
	})
	defer client.Close()

	task, err := tasks.NewBrokerProxyCheckTask("http://emqxproxy-fbcac15268eae392.elb.us-east-1.amazonaws.com:8000", "")
	if err != nil {
		t.Fatalf("could not create task: %v", err)

	}
	info, err := client.Enqueue(task, asynq.Queue("at_high"))
	if err != nil {
		t.Fatalf("could not enqueue task: %v", err)
	}
	t.Logf("enqueued task: id=%s queue=%s", info.ID, info.Queue)
}
