package tasks

import (
	"autoswitchbot/common"
	"autoswitchbot/util"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"github.com/pkg/errors"
	"golang.org/x/exp/rand"
	"golang.org/x/time/rate"

	"github.com/hibiken/asynq"
)

type BrokerApiPayload struct {
	Host   string `json:"host"`
	ApiKey string `json:"api_key"`
	Secret string `json:"secret"`
}

type BrokerApiProcessor struct{}

func NewBrokerApiCheckTask(params map[string]string) (*asynq.Task, error) {
	payload, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}
	// task options can be passed to NewTask, which can be overridden at enqueue time.
	return asynq.NewTask(common.TypeBrokerApiCheck, payload, asynq.MaxRetry(3), asynq.Timeout(30*time.Second), asynq.Queue("at_high")), nil
}

// 只允许每秒有一个，如果worker 掉线很久，上线后最多一秒请求一次
var limiter = rate.NewLimiter(1, 1)

func (processor *BrokerApiProcessor) ProcessTask(ctx context.Context, t *asynq.Task) error {
	if !limiter.Allow() {
		return &util.RateLimitError{
			RetryIn: time.Duration(rand.Intn(5)) * time.Second,
		}
	}
	log.Printf("receive task: %s %s", t.Type(), t.ResultWriter().TaskID())
	var p BrokerApiPayload
	if err := json.Unmarshal(t.Payload(), &p); err != nil {
		return fmt.Errorf("json.Unmarshal failed: %v: %w", err, asynq.SkipRetry)
	}
	log.Printf("broker api check: %s %s", p.Host, p.ApiKey)

	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	c := make(chan error, 1)
	go func() {
		select {
		case c <- GetBrokerApiKeys(ctx, p.Host, p.ApiKey, p.Secret):
		case <-ctx.Done():
			// if cancel, Goroutine exit
			return
		}
	}()

	select {
	case <-ctx.Done():
		// cancelation signal received, abandon this work.
		return ctx.Err()
	case res := <-c:
		return res
	}
}

func GetBrokerApiKeys(ctx context.Context, host, apiKey, secret string) error {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	// https://docs.emqx.com/zh/emqx/v5.6/admin/api-docs.html
	url := host + "/api/v5/monitor_current"
	log.Printf("%s\n", url)
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return errors.Wrap(err, "Error creating request")
	}
	auth := fmt.Sprintf("%s:%s", apiKey, secret)
	basicAuth := "Basic " + base64.StdEncoding.EncodeToString([]byte(auth))
	req.Header.Add("Authorization", basicAuth)
	resp, err := httpClient.Do(req)
	if err != nil {
		return errors.Wrap(err, "Error sending request")
	}
	defer resp.Body.Close() // 确保关闭响应的Body
	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return errors.Wrap(err, "Error reading response body")
	}
	log.Printf("Response status: %s\n", resp.Status)
	log.Printf("Response body: %s\n", body)
	if resp.StatusCode >= 400 {
		return errors.Errorf("Response status: %s", resp.Status)
	}
	return nil
}

func NewBrokerApiProcessor() *BrokerApiProcessor {
	return &BrokerApiProcessor{}
}
