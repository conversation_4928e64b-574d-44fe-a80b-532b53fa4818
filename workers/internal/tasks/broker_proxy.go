package tasks

import (
	"autoswitchbot/common"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"github.com/hibiken/asynq"
	"github.com/pkg/errors"
)

type BrokerCertGetPayload struct {
	Host   string `json:"host"`
	UserID string `json:"userID"`
}

type BrokerProxyProcessor struct{}

func NewBrokerProxyCheckTask(params map[string]string) (*asynq.Task, error) {
	payload, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}
	// task options can be passed to NewTask, which can be overridden at enqueue time.
	return asynq.NewTask(common.TypeBrokerProxyCheck, payload, asynq.MaxRetry(3), asynq.Timeout(30*time.Second), asynq.Queue("at_high")), nil
}

func (processor *BrokerProxyProcessor) ProcessTask(ctx context.Context, t *asynq.Task) error {
	log.Printf("receive task: %s %s", t.Type(), t.ResultWriter().TaskID())
	var p BrokerCertGetPayload
	if err := json.Unmarshal(t.Payload(), &p); err != nil {
		return fmt.Errorf("json.Unmarshal failed: %v: %w", err, asynq.SkipRetry)
	}
	log.Printf("broker proxy check: %+v", p)
	return GetBrokerUserCert(ctx, p.Host, p.UserID)
}

func GetBrokerUserCert(ctx context.Context, host, userID string) error {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	url := host + "/v1/emqx/usercert/get"
	log.Println(url)
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return errors.Wrap(err, "Error creating request")
	}
	req.Header.Add("userID", userID)
	resp, err := httpClient.Do(req)
	if err != nil {
		return errors.Wrap(err, "Error sending request")
	}
	defer resp.Body.Close() // 确保关闭响应的Body
	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return errors.Wrap(err, "Error reading response body")
	}
	log.Printf("Response status: %s\n", resp.Status)
	log.Printf("Response body: %s\n", body)
	if resp.StatusCode >= 400 {
		return errors.Errorf("Response status: %s", resp.Status)
	}
	return nil
}

func NewBrokerProxyProcessor() *BrokerProxyProcessor {
	return &BrokerProxyProcessor{}
}
