package config

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"

	"github.com/spf13/viper"
)

type Config struct {
	Redis struct {
		Addr     string `mapstructure:"addr"`
		Password string `mapstructure:"password"`
		DB       int    `mapstructure:"db"`
	} `mapstructure:"redis"`
	Tasks []Task `mapstructure:"tasks"`
}

type Task struct {
	Type     string            `mapstructure:"type"`
	Priority string            `mapstructure:"priority;omitempty"`
	CronSpec string            `mapstructure:"cron"`
	Params   map[string]string `mapstructure:"params"`
}

var config Config

func GetFilePath() string {
	// 获取当前函数的文件和行号信息
	_, filename, _, ok := runtime.Caller(0)
	if !ok {
		log.Println("无法获取当前代码的文件路径")
		return ""
	}
	// 获取文件所在目录
	dir := filepath.Dir(filename)
	log.Println("当前文件所在目录:", dir)
	return dir
}
func GetExecPath() string {
	// 获取当前执行文件的路径
	exePath, err := os.Executable()
	if err != nil {
		fmt.Println("获取执行文件路径失败:", err)
		return ""
	}
	// 获取文件所在目录
	dir := filepath.Dir(exePath)
	log.Println("当前文件所在目录:", dir)
	return dir
}

func LoadConfig() *Config {
	v := viper.New()
	v.SetConfigName("config") // name of config file (without extension)
	v.SetConfigType("yaml")   // REQUIRED if the config file does not have the extension in the name
	v.AddConfigPath(".")      // optionally look for config in the working directory
	if err := v.ReadInConfig(); err != nil {
		panic(fmt.Errorf("read config failed: %s \n", err))
	}
	if err := v.Unmarshal(&config); err != nil {
		panic(fmt.Errorf("unmarshal config failed: %s \n", err))
	}
	return &config
}
