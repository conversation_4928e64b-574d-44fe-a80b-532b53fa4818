package main

import (
	"autoswitchbot/common"
	"autoswitchbot/config"
	"autoswitchbot/internal/tasks"
	"autoswitchbot/util"
	"log"

	"github.com/hibiken/asynq"
)

// ./asynqmon --redis-url=redis://:switchbot123456@192.168.2.7:16379/0
func main() {
	config := config.LoadConfig()
	srv := asynq.NewServer(
		util.FixedRedisClientOpt{
			Addr:     config.Redis.Addr,
			Password: config.Redis.Password,
			DB:       config.Redis.DB,
		},
		asynq.Config{
			Concurrency: 10,
			Queues: map[string]int{
				"at_high":    6,
				"at_default": 3,
				"at_low":     1,
			},
			StrictPriority: true, // strict mode!
			// If error is due to rate limit, don't count the error as a failure.
			IsFailure:      func(err error) bool { return !util.IsRateLimitError(err) },
			RetryDelayFunc: util.RetryDelay,
		},
	)
	mux := asynq.NewServeMux()
	mux.Handle(common.TypeBrokerApiCheck, tasks.NewBrokerApiProcessor())
	mux.Handle(common.TypeBrokerProxyCheck, tasks.NewBrokerProxyProcessor())
	// ...注册其他处理程序...
	if err := srv.Run(mux); err != nil {
		log.Fatalf("could not run server: %v", err)
	}
}
