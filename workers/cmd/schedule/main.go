package main

import (
	"autoswitchbot/common"
	"autoswitchbot/config"
	"autoswitchbot/internal/tasks"
	"autoswitchbot/util"
	"errors"
	"log"
	"os"
	"time"

	"github.com/hibiken/asynq"
	"gopkg.in/yaml.v2"
)

type TaskCreator func(map[string]string) (*asynq.Task, error)

var taskCreators = map[string]TaskCreator{
	common.TypeBrokerApiCheck:     tasks.NewBrokerApiCheckTask,
	common.TypeBrokerKinesisCheck: tasks.NewBrokerKinesisCheckTask,
	common.TypeBrokerProxyCheck:   tasks.NewBrokerProxyCheckTask,
}

func GetTaskCreator(taskType string) (TaskCreator, error) {
	creator, ok := taskCreators[taskType]
	if !ok {
		return nil, errors.New("no task creator found for the given task type")
	}
	return creator, nil
}

func main() {
	config := config.LoadConfig()
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		panic(err)
	}
	// scheduler := asynq.NewScheduler(util.FixedRedisClientOpt{
	// 	Addr:     config.Redis.Addr,
	// 	Password: config.Redis.Password,
	// 	DB:       config.Redis.DB,
	// }, &asynq.SchedulerOpts{
	// 	Location: loc,
	// })

	// Register tasks from the configuration.
	// for _, taskConfig := range config.Tasks {
	// 	// log.Printf("%+v\n", taskConfig)
	// 	log.Printf("Task: %s Cron: %s\n", taskConfig.Type, taskConfig.CronSpec)
	// 	taskCreater, err := GetTaskCreator(taskConfig.Type)
	// 	task, err := taskCreater(taskConfig.Params)
	// 	entryID, err := scheduler.Register(taskConfig.CronSpec, task)
	// 	if err != nil {
	// 		log.Printf("registe task err: %v\n", err)
	// 	}
	// 	log.Printf("registered an entry: %q\n", entryID)
	// }

	// if err := scheduler.Run(); err != nil {
	// 	log.Fatal(err)
	// }

	provider := &FileBasedConfigProvider{filename: "./config.yaml"}

	mgr, err := asynq.NewPeriodicTaskManager(
		asynq.PeriodicTaskManagerOpts{
			RedisConnOpt: util.FixedRedisClientOpt{
				Addr:     config.Redis.Addr,
				Password: config.Redis.Password,
				DB:       config.Redis.DB,
			},
			PeriodicTaskConfigProvider: provider,         // this provider object is the interface to your config source
			SyncInterval:               60 * time.Second, // this field specifies how often sync should happen
			SchedulerOpts: &asynq.SchedulerOpts{
				Location: loc,
			},
		})
	if err != nil {
		log.Fatal(err)
	}

	if err := mgr.Run(); err != nil {
		log.Fatal(err)
	}

}

// FileBasedConfigProvider implements asynq.PeriodicTaskConfigProvider interface.
type FileBasedConfigProvider struct {
	filename string
}

// Parses the yaml file and return a list of PeriodicTaskConfigs.
func (p *FileBasedConfigProvider) GetConfigs() ([]*asynq.PeriodicTaskConfig, error) {
	log.Printf("Reload configs\n")
	data, err := os.ReadFile(p.filename)
	if err != nil {
		return nil, err
	}
	var c PeriodicTaskConfigContainer
	if err := yaml.Unmarshal(data, &c); err != nil {
		return nil, err
	}
	var configs []*asynq.PeriodicTaskConfig
	for _, cfg := range c.Tasks {
		// log.Printf("Task: %s Cron: %s\n", cfg.TaskType, cfg.Cronspec)
		taskCreater, err := GetTaskCreator(cfg.TaskType)
		if err != nil {
			log.Printf("%v\n", err)
		}
		task, err := taskCreater(cfg.Params)
		configs = append(configs, &asynq.PeriodicTaskConfig{Cronspec: cfg.Cronspec, Task: task})
	}
	return configs, nil
}

type PeriodicTaskConfigContainer struct {
	Tasks []*Task `yaml:"tasks"`
}

type Task struct {
	Cronspec string            `yaml:"cron"`
	TaskType string            `yaml:"type"`
	Params   map[string]string `yaml:"params"`
}
