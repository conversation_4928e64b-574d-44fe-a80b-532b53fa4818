FROM public.ecr.aws/docker/library/golang:1.23-alpine AS builder

LABEL authors="huyuwei"

WORKDIR /app
COPY . .

ENV GOPROXY=https://goproxy.cn,direct

RUN GOOS=linux go build -o worker cmd/worker/main.go \
    && GOOS=linux go build -o schedule cmd/schedule/main.go

# bookworm-slim stable-slim 12-slim  => debian12
FROM public.ecr.aws/docker/library/debian:stable-slim

RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources
RUN apt-get update; \
	apt-get install -y --no-install-recommends \
		ca-certificates \
		netbase \
		tzdata \
		procps \
		telnet \
		iproute2 \
        dnsutils \
	; \
	rm -rf /var/lib/apt/lists/*
WORKDIR /app
COPY --from=builder /app/worker .
COPY --from=builder /app/schedule .

CMD ["./worker"]
