.PHONY: build

build:
	if ! aws ecr describe-repositories --repository-names switchbot/autotest-worker --region us-east-1 --profile prod --no-cli-pager; then \
		aws ecr create-repository --repository-name switchbot/autotest-worker --image-tag-mutability MUTABLE --region us-east-1 --profile prod --cli-auto-prompt; \
	fi;\
	aws ecr get-login-password --region us-east-1 --profile prod | docker login --username AWS --password-stdin 443283509441.dkr.ecr.us-east-1.amazonaws.com;\
	docker buildx build --platform linux/arm64 -t 443283509441.dkr.ecr.us-east-1.amazonaws.com/switchbot/autotest-worker:latest --push .
