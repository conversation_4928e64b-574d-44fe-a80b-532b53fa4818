export default [
  {
    path: '/user',
    layout: false,
    routes: [{ name: '登录', path: '/user/login', component: './User/Login' }],
  },
  { path: '/preview/testreport/:id', layout: false, component: './Test/TestReportDetail' },
  { path: '/welcome', name: '欢迎', icon: 'smile', component: './Welcome' },
  {
    path: '/admin',
    name: '管理页',
    icon: 'crown',
    access: 'canAdmin',
    routes: [
      { path: '/admin', redirect: '/admin/sub-page' },
      { path: '/admin/sub-page', name: '二级管理页', component: './Admin' },
    ],
  },
  { name: '项目管理', icon: 'project', path: '/project', component: './Project' },
  {
    name: '基础配置',
    icon: 'setting',
    path: '/base',
    routes: [
      { path: '/base', redirect: '/base/vars' },
      { path: '/base/accessKey', name: '密钥管理', component: './Base/AccessKey' },
      { path: '/base/envs', name: '环境管理', component: './Base/Envs' },
      { path: '/base/vars', name: '变量管理', component: './Base/Vars' },
      { path: '/base/tags', name: '标签管理', component: './Base/Tags' },
      { path: '/base/task', name: '定时任务', component: './Base/TaskList' },
    ],
  },
  {
    name: '覆盖率管理',
    icon: 'setting',
    path: '/coverage',
    routes: [
      {
        path: '/coverage/bucket',
        name: '覆盖率存储桶配置',
        component: './Coverage/CoverageBucket',
      },
      { path: '/coverage/meta', name: '覆盖率基础管理', component: './Coverage/CoverageMeta' },
      { path: '/coverage/report', name: '覆盖率报告管理', component: './Coverage/CoverageReport' },
    ],
  },
  {
    name: '测试管理',
    icon: 'robot',
    path: '/test',
    routes: [
      { path: '/test', redirect: '/test/testcase' },
      { path: '/test/testcase', name: '测试用例', component: './Test/TestCaseList' },
      { path: '/test/testplan', name: '测试计划', component: './Test/TestPlan' },
      { path: '/test/testreport', name: '测试报告', component: './Test/TestReportList' },
    ],
  },
  {
    name: '虚拟设备管理',
    icon: 'mobile',
    path: '/simulator',
    routes: [
      { path: '/simulator', redirect: '/simulator/device' },
      { path: '/simulator/typemanage', name: '设备类型', component: './Simulator/TypeManage' },
      { path: '/simulator/device', name: '设备管理', component: './Simulator/Device' },
      { path: '/simulator/detail', name: '设备调试', component: './Simulator/Detail' },
    ],
  },
  { path: '/', redirect: '/welcome' },
  { path: '*', layout: false, component: './404' },
];
