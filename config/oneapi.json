{"openapi": "3.0.3", "info": {"title": "自动化测试平台", "version": "1.0.0", "description": "为了自动化测试"}, "paths": {"/api/accounts/login": {"post": {"operationId": "api_accounts_login_create", "description": "Check the credentials and return the REST Token\nif the credentials are valid and authenticated.\nCalls Django Auth login method to register User ID\nin Django session framework\n\nAccept the following POST parameters: username, password\nReturn the REST Framework Token Object's key.", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Login"}}}, "required": true}, "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}, "description": ""}}}}, "/api/accounts/logout": {"post": {"operationId": "api_accounts_logout_create", "description": "Calls Django logout method and delete the Token object\nassigned to the current User object.\n\nAccepts/Returns nothing.", "tags": ["api"], "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RestAuthDetail"}}}, "description": ""}}}}, "/api/accounts/password/change": {"post": {"operationId": "api_accounts_password_change_create", "description": "Calls <PERSON><PERSON><PERSON> Auth SetPasswordForm save method.\n\nAccepts the following POST parameters: new_password1, new_password2\nReturns the success/fail message.", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordChange"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RestAuthDetail"}}}, "description": ""}}}}, "/api/accounts/password/reset": {"post": {"operationId": "api_accounts_password_reset_create", "description": "Calls <PERSON><PERSON><PERSON> Auth PasswordResetForm save method.\n\nAccepts the following POST parameters: email\nReturns the success/fail message.", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordReset"}}}, "required": true}, "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RestAuthDetail"}}}, "description": ""}}}}, "/api/accounts/password/reset/confirm": {"post": {"operationId": "api_accounts_password_reset_confirm_create", "description": "Password reset e-mail link is confirmed, therefore\nthis resets the user's password.\n\nAccepts the following POST parameters: token, uid,\n    new_password1, new_password2\nReturns the success/fail message.", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordResetConfirm"}}}, "required": true}, "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RestAuthDetail"}}}, "description": ""}}}}, "/api/accounts/user": {"get": {"operationId": "api_accounts_user_retrieve", "description": "Reads and updates UserModel fields\nAccepts GET, PUT, PATCH methods.\n\nDefault accepted fields: username, first_name, last_name\nDefault display fields: pk, username, email, first_name, last_name\nRead-only fields: pk, email\n\nReturns UserModel fields.", "tags": ["api"], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "description": ""}}}, "put": {"operationId": "api_accounts_user_update", "description": "Reads and updates UserModel fields\nAccepts GET, PUT, PATCH methods.\n\nDefault accepted fields: username, first_name, last_name\nDefault display fields: pk, username, email, first_name, last_name\nRead-only fields: pk, email\n\nReturns UserModel fields.", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "description": ""}}}, "patch": {"operationId": "api_accounts_user_partial_update", "description": "Reads and updates UserModel fields\nAccepts GET, PUT, PATCH methods.\n\nDefault accepted fields: username, first_name, last_name\nDefault display fields: pk, username, email, first_name, last_name\nRead-only fields: pk, email\n\nReturns UserModel fields.", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchedUser"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "description": ""}}}}, "/api/env": {"get": {"operationId": "api_env_list", "parameters": [{"name": "current", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}, {"name": "ordering", "required": false, "in": "query", "description": "Which field to use when ordering the results.", "schema": {"type": "string"}}, {"name": "pageSize", "required": false, "in": "query", "description": "Number of results to return per page.", "schema": {"type": "integer"}}], "tags": ["api"], "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedEnvironmentListList"}}}, "description": ""}}}, "post": {"operationId": "api_env_create", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnvironmentList"}}}, "required": true}, "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnvironmentList"}}}, "description": ""}}}, "delete": {"operationId": "api_env_destroy", "description": "批量删除接口", "tags": ["api"], "responses": {"204": {"description": "No response body"}}}}, "/api/env/{id}": {"get": {"operationId": "api_env_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnvironmentList"}}}, "description": ""}}}, "put": {"operationId": "api_env_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnvironmentList"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnvironmentList"}}}, "description": ""}}}, "delete": {"operationId": "api_env_destroy_2", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "responses": {"204": {"description": "No response body"}}}}, "/api/project": {"get": {"operationId": "api_project_list", "parameters": [{"name": "current", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}, {"name": "ordering", "required": false, "in": "query", "description": "Which field to use when ordering the results.", "schema": {"type": "string"}}, {"name": "pageSize", "required": false, "in": "query", "description": "Number of results to return per page.", "schema": {"type": "integer"}}], "tags": ["api"], "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedProjectList"}}}, "description": ""}}}, "post": {"operationId": "api_project_create", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Project"}}}, "required": true}, "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Project"}}}, "description": ""}}}, "delete": {"operationId": "api_project_destroy", "description": "批量删除接口", "tags": ["api"], "responses": {"204": {"description": "No response body"}}}}, "/api/project/{id}": {"get": {"operationId": "api_project_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Project"}}}, "description": ""}}}, "put": {"operationId": "api_project_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Project"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Project"}}}, "description": ""}}}, "delete": {"operationId": "api_project_destroy_2", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "responses": {"204": {"description": "No response body"}}}}, "/api/scheduled-tasks": {"get": {"operationId": "api_scheduled_tasks_list", "parameters": [{"name": "current", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}, {"name": "ordering", "required": false, "in": "query", "description": "Which field to use when ordering the results.", "schema": {"type": "string"}}, {"name": "pageSize", "required": false, "in": "query", "description": "Number of results to return per page.", "schema": {"type": "integer"}}], "tags": ["api"], "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedScheduledTaskList"}}}, "description": ""}}}, "post": {"operationId": "api_scheduled_tasks_create", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduledTask"}}}, "required": true}, "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduledTask"}}}, "description": ""}}}, "delete": {"operationId": "api_scheduled_tasks_destroy", "description": "批量删除接口", "tags": ["api"], "responses": {"204": {"description": "No response body"}}}}, "/api/scheduled-tasks/{id}/": {"get": {"operationId": "api_scheduled_tasks_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true}], "tags": ["api"], "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduledTask"}}}, "description": ""}}}, "put": {"operationId": "api_scheduled_tasks_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true}], "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduledTask"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduledTask"}}}, "description": ""}}}, "delete": {"operationId": "api_scheduled_tasks_destroy_2", "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true}], "tags": ["api"], "responses": {"204": {"description": "No response body"}}}}, "/api/simulate/alias_type": {"get": {"operationId": "api_simulate_alias_type_retrieve", "description": "已创建的设备类型别名\n:param request:\n:param args:\n:param kwargs:\n:return:", "tags": ["api"], "security": [{}], "responses": {"200": {"description": "No response body"}}}}, "/api/simulate/callback_config": {"get": {"operationId": "api_simulate_callback_config_list", "description": "虚拟设备callback配置 列表、新增、批量删除", "parameters": [{"name": "current", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}, {"name": "ordering", "required": false, "in": "query", "description": "Which field to use when ordering the results.", "schema": {"type": "string"}}, {"name": "pageSize", "required": false, "in": "query", "description": "Number of results to return per page.", "schema": {"type": "integer"}}], "tags": ["api"], "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedSimulateDeviceCallbackConfigList"}}}, "description": ""}}}, "post": {"operationId": "api_simulate_callback_config_create", "description": "虚拟设备callback配置 列表、新增、批量删除", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulateDeviceCallbackConfig"}}}, "required": true}, "security": [{}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulateDeviceCallbackConfig"}}}, "description": ""}}}, "delete": {"operationId": "api_simulate_callback_config_destroy", "description": "批量删除接口", "tags": ["api"], "security": [{}], "responses": {"204": {"description": "No response body"}}}}, "/api/simulate/callback_config/{id}": {"get": {"operationId": "api_simulate_callback_config_retrieve", "description": "虚拟设备callback配置 单条 详情、修改、删除", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulateDeviceCallbackConfig"}}}, "description": ""}}}, "put": {"operationId": "api_simulate_callback_config_update", "description": "虚拟设备callback配置 单条 详情、修改、删除", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulateDeviceCallbackConfig"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulateDeviceCallbackConfig"}}}, "description": ""}}}, "delete": {"operationId": "api_simulate_callback_config_destroy_2", "description": "虚拟设备callback配置 单条 详情、修改、删除", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "responses": {"204": {"description": "No response body"}}}}, "/api/simulate/command": {"post": {"operationId": "api_simulate_command_create", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatusCommandRequest"}}}, "required": true}, "security": [{}], "responses": {"200": {"description": "No response body"}}}}, "/api/simulate/device": {"get": {"operationId": "api_simulate_device_list", "description": "虚拟设备 列表、新增、批量删除", "parameters": [{"name": "current", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}, {"name": "ordering", "required": false, "in": "query", "description": "Which field to use when ordering the results.", "schema": {"type": "string"}}, {"name": "pageSize", "required": false, "in": "query", "description": "Number of results to return per page.", "schema": {"type": "integer"}}], "tags": ["api"], "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedSimulateDeviceList"}}}, "description": ""}}}, "post": {"operationId": "api_simulate_device_create", "description": "虚拟设备生成\n1. 使用参数中的类型，生成对应类型的设备，产品名称复制\n2. 随机生成mac(特定格式)，插入防重表，避免线上撞mac\n3. 默认online状态，发送消息到任务服务，启动mqtt客户端监听", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulateDevice"}}}, "required": true}, "security": [{}], "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulateDevice"}}}, "description": ""}}}, "delete": {"operationId": "api_simulate_device_destroy", "description": "批量删除接口", "tags": ["api"], "responses": {"204": {"description": "No response body"}}}}, "/api/simulate/device/{mac}": {"get": {"operationId": "api_simulate_device_retrieve", "description": "虚拟设备单条 详情、修改、删除\n\n虚拟设备信息修改\n1. 不允许让用户修改mac\n\n虚拟设备删除\n1. 软删除，其他信息保留\n2. 发送消息，通知mqtt客户端下线", "parameters": [{"in": "path", "name": "mac", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulateDevice"}}}, "description": ""}}}, "put": {"operationId": "api_simulate_device_update", "description": "虚拟设备单条 详情、修改、删除\n\n虚拟设备信息修改\n1. 不允许让用户修改mac\n\n虚拟设备删除\n1. 软删除，其他信息保留\n2. 发送消息，通知mqtt客户端下线", "parameters": [{"in": "path", "name": "mac", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulateDevice"}}}, "required": true}, "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulateDevice"}}}, "description": ""}}}, "delete": {"operationId": "api_simulate_device_destroy_2", "description": "虚拟设备单条 详情、修改、删除\n\n虚拟设备信息修改\n1. 不允许让用户修改mac\n\n虚拟设备删除\n1. 软删除，其他信息保留\n2. 发送消息，通知mqtt客户端下线", "parameters": [{"in": "path", "name": "mac", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "responses": {"204": {"description": "No response body"}}}}, "/api/simulate/funcs": {"get": {"operationId": "api_simulate_funcs_retrieve", "tags": ["api"], "security": [{}], "responses": {"200": {"description": "No response body"}}}}, "/api/simulate/product": {"get": {"operationId": "api_simulate_product_retrieve", "description": "产品类型枚举\n:param request:\n:param args:\n:param kwargs:\n:return:", "tags": ["api"], "security": [{}], "responses": {"200": {"description": "No response body"}}}}, "/api/simulate/props": {"get": {"operationId": "api_simulate_props_retrieve", "tags": ["api"], "security": [{}], "responses": {"200": {"description": "No response body"}}}}, "/api/simulate/s3_policy": {"post": {"operationId": "api_simulate_s3_policy_create", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/S3PolicyRequest"}}}, "required": true}, "security": [{}], "responses": {"200": {"description": "No response body"}}}}, "/api/simulate/support_status/{alias}": {"get": {"operationId": "api_simulate_support_status_retrieve", "parameters": [{"in": "path", "name": "alias", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "security": [{}], "responses": {"200": {"description": "No response body"}}}}, "/api/simulate/support_type": {"get": {"operationId": "api_simulate_support_type_list", "description": "虚拟设备类型 列表、新增、批量删除", "parameters": [{"name": "current", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}, {"name": "ordering", "required": false, "in": "query", "description": "Which field to use when ordering the results.", "schema": {"type": "string"}}, {"name": "pageSize", "required": false, "in": "query", "description": "Number of results to return per page.", "schema": {"type": "integer"}}], "tags": ["api"], "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedSimulateDeviceTypeList"}}}, "description": ""}}}, "post": {"operationId": "api_simulate_support_type_create", "description": "虚拟设备类型 列表、新增、批量删除", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulateDeviceType"}}}, "required": true}, "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulateDeviceType"}}}, "description": ""}}}, "delete": {"operationId": "api_simulate_support_type_destroy", "description": "批量删除接口", "tags": ["api"], "responses": {"204": {"description": "No response body"}}}}, "/api/simulate/support_type/{alias}": {"get": {"operationId": "api_simulate_support_type_retrieve", "description": "虚拟设备类型单条 详情、修改、删除", "parameters": [{"in": "path", "name": "alias", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulateDeviceType"}}}, "description": ""}}}, "put": {"operationId": "api_simulate_support_type_update", "description": "虚拟设备类型单条 详情、修改、删除", "parameters": [{"in": "path", "name": "alias", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulateDeviceType"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimulateDeviceType"}}}, "description": ""}}}, "delete": {"operationId": "api_simulate_support_type_destroy_2", "description": "虚拟设备类型单条 详情、修改、删除", "parameters": [{"in": "path", "name": "alias", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "responses": {"204": {"description": "No response body"}}}}, "/api/simulate/upload_map": {"post": {"operationId": "api_simulate_upload_map_create", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/S3UploadMapRequest"}}}, "required": true}, "security": [{}], "responses": {"200": {"description": "No response body"}}}}, "/api/testcase": {"get": {"operationId": "api_testcase_list", "parameters": [{"name": "current", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}, {"name": "ordering", "required": false, "in": "query", "description": "Which field to use when ordering the results.", "schema": {"type": "string"}}, {"name": "pageSize", "required": false, "in": "query", "description": "Number of results to return per page.", "schema": {"type": "integer"}}], "tags": ["api"], "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedTestCaseList"}}}, "description": ""}}}, "post": {"operationId": "api_testcase_create", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestCase"}}}, "required": true}, "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestCase"}}}, "description": ""}}}, "delete": {"operationId": "api_testcase_destroy", "description": "批量删除接口", "tags": ["api"], "responses": {"204": {"description": "No response body"}}}}, "/api/testcase/{id}": {"get": {"operationId": "api_testcase_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "security": [{}], "responses": {"200": {"description": "No response body"}}}, "put": {"operationId": "api_testcase_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestCaseDetail"}}}, "required": true}, "responses": {"200": {"description": "No response body"}}}}, "/api/testcase/dir/{project_id}": {"get": {"operationId": "api_testcase_dir_retrieve", "parameters": [{"in": "path", "name": "project_id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestCaseDirectory"}}}, "description": ""}}}, "post": {"operationId": "api_testcase_dir_create", "parameters": [{"in": "path", "name": "project_id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestCaseDirectoryUpdate"}}}, "required": true}, "responses": {"200": {"description": "No response body"}}}, "put": {"operationId": "api_testcase_dir_update", "parameters": [{"in": "path", "name": "project_id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestCaseDirectoryUpdate"}}}, "required": true}, "responses": {"200": {"description": "No response body"}}}}, "/api/testcase/dir/{project_id}/{key}": {"delete": {"operationId": "api_testcase_dir_destroy", "parameters": [{"in": "path", "name": "key", "schema": {"type": "string"}, "required": true}, {"in": "path", "name": "project_id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "responses": {"204": {"description": "No response body"}}}}, "/api/testcase/run": {"post": {"operationId": "api_testcase_run_create", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RunTestCases"}}}, "required": true}, "responses": {"200": {"description": "No response body"}}}}, "/api/testcase/runbydir": {"post": {"operationId": "api_testcase_runbydir_create", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RunTestCasesByDir"}}}, "required": true}, "responses": {"200": {"description": "No response body"}}}}, "/api/testcase/runbyproject": {"post": {"operationId": "api_testcase_runbyproject_create", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RunTestCasesByProject"}}}, "required": true}, "responses": {"200": {"description": "No response body"}}}}, "/api/testcase/upload": {"post": {"operationId": "api_testcase_upload_create", "tags": ["api"], "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/TestCaseUpload"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestCaseUpload"}}}, "description": ""}}}, "put": {"operationId": "api_testcase_upload_update", "tags": ["api"], "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/TestCaseUpload"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestCaseUpload"}}}, "description": ""}}}}, "/api/testreport": {"get": {"operationId": "api_testreport_list", "parameters": [{"name": "current", "required": false, "in": "query", "description": "A page number within the paginated result set.", "schema": {"type": "integer"}}, {"name": "ordering", "required": false, "in": "query", "description": "Which field to use when ordering the results.", "schema": {"type": "string"}}, {"name": "pageSize", "required": false, "in": "query", "description": "Number of results to return per page.", "schema": {"type": "integer"}}], "tags": ["api"], "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedTestReportList"}}}, "description": ""}}}, "post": {"operationId": "api_testreport_create", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestReport"}}}, "required": true}, "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestReport"}}}, "description": ""}}}, "delete": {"operationId": "api_testreport_destroy", "description": "批量删除接口", "tags": ["api"], "responses": {"204": {"description": "No response body"}}}}, "/api/testreport/{id}": {"get": {"operationId": "api_testreport_retrieve", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "security": [{}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestReportDetail"}}}, "description": ""}}}, "put": {"operationId": "api_testreport_update", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestReportDetail"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestReportDetail"}}}, "description": ""}}}, "delete": {"operationId": "api_testreport_destroy_2", "parameters": [{"in": "path", "name": "id", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "responses": {"204": {"description": "No response body"}}}}, "/api/var": {"get": {"operationId": "api_var_retrieve", "tags": ["api"], "security": [{}], "responses": {"200": {"description": "No response body"}}}, "post": {"operationId": "api_var_create", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnvironmentVariableCreate"}}}, "required": true}, "responses": {"200": {"description": "No response body"}}}, "put": {"operationId": "api_var_update", "tags": ["api"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnvironmentVariableCreate"}}}, "required": true}, "responses": {"200": {"description": "No response body"}}}}, "/api/var/{key}": {"delete": {"operationId": "api_var_destroy", "parameters": [{"in": "path", "name": "key", "schema": {"type": "string"}, "required": true}], "tags": ["api"], "responses": {"204": {"description": "No response body"}}}}, "/reset_confirm/{uidb64}/{token}": {"get": {"operationId": "reset_confirm_retrieve", "description": "proxy for front-end password reset page", "parameters": [{"in": "path", "name": "token", "schema": {"type": "string"}, "required": true}, {"in": "path", "name": "uidb64", "schema": {"type": "string"}, "required": true}], "tags": ["reset_confirm"], "security": [{}], "responses": {"200": {"description": "No response body"}}}}}, "components": {"schemas": {"EnvEnum": {"enum": ["test", "prod"], "type": "string", "description": "* `test` - Test\n* `prod` - Prod"}, "EnvironmentList": {"type": "object", "properties": {"id": {"type": "string", "readOnly": true}, "created_by": {"type": "string", "readOnly": true}, "updated_by": {"type": "string", "readOnly": true}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}, "name": {"type": "string", "maxLength": 255}, "desc": {"type": "string"}}, "required": ["created_at", "created_by", "desc", "id", "name", "updated_at", "updated_by"]}, "EnvironmentVariableCreate": {"type": "object", "properties": {"key": {"type": "string", "maxLength": 255}, "desc": {"type": "string"}, "values": {"type": "object", "additionalProperties": {}}}, "required": ["key", "values"]}, "Login": {"type": "object", "properties": {"username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "password": {"type": "string"}}, "required": ["password"]}, "NullEnum": {"enum": [null]}, "PaginatedEnvironmentListList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?current=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?current=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/EnvironmentList"}}}}, "PaginatedProjectList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?current=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?current=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/Project"}}}}, "PaginatedScheduledTaskList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?current=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?current=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/ScheduledTask"}}}}, "PaginatedSimulateDeviceCallbackConfigList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?current=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?current=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/SimulateDeviceCallbackConfig"}}}}, "PaginatedSimulateDeviceList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?current=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?current=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/SimulateDevice"}}}}, "PaginatedSimulateDeviceTypeList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?current=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?current=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/SimulateDeviceType"}}}}, "PaginatedTestCaseList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?current=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?current=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/TestCase"}}}}, "PaginatedTestReportList": {"type": "object", "properties": {"count": {"type": "integer", "example": 123}, "next": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?current=4"}, "previous": {"type": "string", "nullable": true, "format": "uri", "example": "http://api.example.org/accounts/?current=2"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/TestReport"}}}}, "PasswordChange": {"type": "object", "properties": {"new_password1": {"type": "string", "maxLength": 128}, "new_password2": {"type": "string", "maxLength": 128}}, "required": ["new_password1", "new_password2"]}, "PasswordReset": {"type": "object", "description": "Serializer for requesting a password reset e-mail.", "properties": {"email": {"type": "string", "format": "email"}}, "required": ["email"]}, "PasswordResetConfirm": {"type": "object", "description": "Serializer for confirming a password reset attempt.", "properties": {"new_password1": {"type": "string", "maxLength": 128}, "new_password2": {"type": "string", "maxLength": 128}, "uid": {"type": "string"}, "token": {"type": "string"}}, "required": ["new_password1", "new_password2", "token", "uid"]}, "PatchedUser": {"type": "object", "properties": {"pk": {"type": "integer", "readOnly": true, "title": "ID"}, "username": {"type": "string", "description": "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.", "pattern": "^[\\w.@+-]+$", "maxLength": 150}, "email": {"type": "string", "format": "email", "readOnly": true, "title": "Email address"}, "first_name": {"type": "string", "maxLength": 150}, "last_name": {"type": "string", "maxLength": 150}, "extra_data": {"type": "object", "additionalProperties": {}, "readOnly": true}}}, "ProductNameEnum": {"enum": ["Floor Cleaning Robot S10", "Hub 2", "Bot"], "type": "string", "description": "* `Floor Cleaning Robot S10` - WoSweeper<PERSON><PERSON>in\n* `Hub 2` - Hub2\n* `<PERSON><PERSON>` - <PERSON><PERSON>"}, "Project": {"type": "object", "properties": {"id": {"type": "string", "readOnly": true}, "created_by": {"type": "string", "readOnly": true}, "updated_by": {"type": "string", "readOnly": true}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}, "name": {"type": "string", "maxLength": 255}, "desc": {"type": "string"}}, "required": ["created_at", "created_by", "desc", "id", "name", "updated_at", "updated_by"]}, "RegionEnum": {"enum": ["us-east-1", "ap-northeast-1", "eu-northeast-1"], "type": "string", "description": "* `us-east-1` - Us\n* `ap-northeast-1` - Ap\n* `eu-northeast-1` - Eu"}, "RestAuthDetail": {"type": "object", "properties": {"detail": {"type": "string", "readOnly": true}}, "required": ["detail"]}, "ResultEnum": {"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "description": "* `0` - Pass\n* `1` - Fail\n* `2` - Interrupted\n* `3` - Internal Error\n* `4` - Usage Error\n* `5` - No Test"}, "RunTestCases": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "integer"}, "writeOnly": true}, "env": {"type": "integer"}}, "required": ["env", "id"]}, "RunTestCasesByDir": {"type": "object", "properties": {"dir": {"type": "integer"}, "env": {"type": "integer"}}, "required": ["dir", "env"]}, "RunTestCasesByProject": {"type": "object", "properties": {"project": {"type": "integer"}, "env": {"type": "integer"}}, "required": ["env", "project"]}, "S3PolicyRequest": {"type": "object", "description": "请求参数", "properties": {"access_key_id": {"type": "string"}, "secret_access_key": {"type": "string"}, "session_token": {"type": "string"}, "action": {"type": "string"}, "destination": {"type": "string"}}, "required": ["access_key_id", "action", "secret_access_key", "session_token"]}, "S3UploadMapRequest": {"type": "object", "description": "请求参数", "properties": {"mac": {"type": "string"}, "role_alias_url": {"type": "string"}, "thingname": {"type": "string"}, "room": {"type": "integer"}, "bucket": {"type": "string"}, "map_id": {"type": "string"}, "label": {"type": "boolean"}}, "required": ["bucket", "mac", "map_id", "role_alias_url", "room", "thingname"]}, "ScheduledTask": {"type": "object", "properties": {"id": {"type": "integer", "readOnly": true}, "task_type": {"type": "integer"}, "task_args": {"type": "object", "additionalProperties": {}}, "crontab": {"type": "integer", "readOnly": true}, "enabled": {"type": "boolean"}, "expression": {"type": "string", "writeOnly": true}}, "required": ["crontab", "expression", "id", "task_args", "task_type"]}, "SimulateDevice": {"type": "object", "properties": {"mac": {"type": "string", "maxLength": 64}, "created_by": {"type": "string", "readOnly": true}, "updated_by": {"type": "string", "readOnly": true}, "type": {"type": "string", "maxLength": 64}, "product_name": {"type": "string", "maxLength": 255}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}, "region": {"allOf": [{"$ref": "#/components/schemas/RegionEnum"}], "description": "设备所在区域\n\n* `us-east-1` - Us\n* `ap-northeast-1` - Ap\n* `eu-northeast-1` - Eu"}, "env": {"allOf": [{"$ref": "#/components/schemas/EnvEnum"}], "description": "设备环境\n\n* `test` - Test\n* `prod` - Prod"}, "is_sub": {"type": "boolean", "description": "是否是子设备"}, "status": {"allOf": [{"$ref": "#/components/schemas/StatusEnum"}], "description": "设备状态\n\n* `on` - Online\n* `off` - Offline"}, "alias_type": {"type": "string", "description": "设备类型别名", "maxLength": 128}, "belong_user": {"type": "string", "description": "物模型设备所属用户id, client_id使用", "maxLength": 64}, "cert_pem": {"type": "string", "description": "iot私钥"}, "key_pem": {"type": "string", "description": "iot公钥"}}, "required": ["alias_type", "created_at", "created_by", "updated_at", "updated_by"]}, "SimulateDeviceCallbackConfig": {"type": "object", "properties": {"id": {"type": "string", "readOnly": true}, "created_by": {"type": "string", "readOnly": true}, "updated_by": {"type": "string", "readOnly": true}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}, "name": {"type": "string", "description": "配置名称", "maxLength": 64}, "mac": {"type": "string", "description": "设备mac地址", "maxLength": 64}, "function_id": {"type": "integer", "maximum": 2147483647, "minimum": -2147483648, "description": "物模型方法id"}, "callback_template": {"type": "object", "additionalProperties": {}, "description": "物模型回复方法模板"}}, "required": ["callback_template", "created_at", "created_by", "function_id", "id", "mac", "name", "updated_at", "updated_by"]}, "SimulateDeviceType": {"type": "object", "properties": {"alias": {"type": "string", "description": "设备类型别名", "maxLength": 128}, "created_by": {"type": "string", "readOnly": true}, "updated_by": {"type": "string", "readOnly": true}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}, "type": {"allOf": [{"$ref": "#/components/schemas/TypeEnum"}], "description": "设备类型\n\n* `WoSweeperOrigin` - WoSweeper<PERSON><PERSON>in\n* `Hub2` - Hub2\n* `<PERSON><PERSON>` - <PERSON><PERSON>"}, "product_name": {"allOf": [{"$ref": "#/components/schemas/ProductNameEnum"}], "description": "产品名称\n\n* `Floor Cleaning Robot S10` - WoSweeperOrigin\n* `Hub 2` - Hub2\n* `<PERSON><PERSON>` - <PERSON><PERSON>"}, "properties": {"type": "object", "additionalProperties": {}, "description": "设备物模型属性"}}, "required": ["alias", "created_at", "created_by", "product_name", "properties", "type", "updated_at", "updated_by"]}, "StatusCommandRequest": {"type": "object", "description": "status command 请求参数", "properties": {"mac": {"type": "string"}, "props": {"type": "array", "items": {}}}, "required": ["mac", "props"]}, "StatusEnum": {"enum": ["on", "off"], "type": "string", "description": "* `on` - Online\n* `off` - Offline"}, "TestCase": {"type": "object", "properties": {"id": {"type": "string", "readOnly": true}, "created_by": {"type": "string", "readOnly": true}, "updated_by": {"type": "string", "readOnly": true}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}, "name": {"type": "string", "maxLength": 255}, "desc": {"type": "string"}, "project": {"type": "integer"}, "directory": {"type": "integer", "nullable": true}}, "required": ["created_at", "created_by", "desc", "id", "name", "project", "updated_at", "updated_by"]}, "TestCaseDetail": {"type": "object", "properties": {"data": {"type": "string", "writeOnly": true}}, "required": ["data"]}, "TestCaseDirectory": {"type": "object", "properties": {"project": {"type": "string", "writeOnly": true}, "key": {"type": "string"}, "title": {"type": "string", "maxLength": 255}, "children": {"type": "string", "readOnly": true}}, "required": ["children", "key", "project", "title"]}, "TestCaseDirectoryUpdate": {"type": "object", "properties": {"project_id": {"type": "string"}, "title": {"type": "string", "maxLength": 255}, "key": {"type": "string", "nullable": true}, "parent_id": {"type": "string", "nullable": true}}, "required": ["project_id", "title"]}, "TestCaseUpload": {"type": "object", "properties": {"file": {"type": "array", "items": {"type": "string", "format": "uri"}}, "project_id": {"type": "string"}, "directory": {"type": "string"}, "env": {"type": "integer"}}, "required": ["directory", "env", "file", "project_id"]}, "TestReport": {"type": "object", "properties": {"id": {"type": "string", "readOnly": true}, "created_by": {"type": "string", "readOnly": true}, "updated_by": {"type": "string", "readOnly": true}, "created_at": {"type": "string", "format": "date-time", "readOnly": true}, "updated_at": {"type": "string", "format": "date-time", "readOnly": true}, "is_deleted": {"type": "boolean"}, "name": {"type": "string", "maxLength": 512}, "env": {"type": "string", "maxLength": 255}, "result": {"nullable": true, "minimum": -2147483648, "maximum": 2147483647, "oneOf": [{"$ref": "#/components/schemas/ResultEnum"}, {"$ref": "#/components/schemas/NullEnum"}]}}, "required": ["created_at", "created_by", "env", "id", "updated_at", "updated_by"]}, "TestReportDetail": {"type": "object", "properties": {"id": {"type": "string", "readOnly": true}, "env": {"type": "string", "maxLength": 255}, "summary": {"type": "string"}}, "required": ["env", "id"]}, "Token": {"type": "object", "description": "Serializer for Token model.", "properties": {"key": {"type": "string", "maxLength": 40}}, "required": ["key"]}, "TypeEnum": {"enum": ["Wo<PERSON>we<PERSON><PERSON><PERSON><PERSON>", "Hub2", "Bot"], "type": "string", "description": "* `WoSweeper<PERSON><PERSON>in` - WoSwe<PERSON><PERSON><PERSON><PERSON>\n* `Hub2` - Hub2\n* `<PERSON><PERSON>` - <PERSON><PERSON>"}, "User": {"type": "object", "properties": {"pk": {"type": "integer", "readOnly": true, "title": "ID"}, "username": {"type": "string", "description": "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.", "pattern": "^[\\w.@+-]+$", "maxLength": 150}, "email": {"type": "string", "format": "email", "readOnly": true, "title": "Email address"}, "first_name": {"type": "string", "maxLength": 150}, "last_name": {"type": "string", "maxLength": 150}, "extra_data": {"type": "object", "additionalProperties": {}, "readOnly": true}}, "required": ["email", "extra_data", "pk", "username"]}}}}